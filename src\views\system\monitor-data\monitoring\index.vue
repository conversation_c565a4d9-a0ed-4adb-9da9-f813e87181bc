<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="终端名称">
        <a-input
          v-model="queryParam.terminalName"
          placeholder="请输入终端名称"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="监测指标">
        <a-select allowClear v-model="queryParam.indexCode" placeholder="请选择">
          <a-select-option v-for="(d, index) in monitoringIndexOptions" :key="index" :value="d.key">
            {{ d.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="通讯状态">
        <a-select allowClear v-model="queryParam.isOnline" placeholder="请选择">
          <a-select-option v-for="(d, index) in communicationStateOptions" :key="index" :value="d.key">
            {{ d.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        ></VxeTable>
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getMonitoringPage } from './services'
  import { getOptions } from '@/api/common'
  import TreeGeneral from '@/components/TreeGeneral'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'Monitoring',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
    },
    data() {
      return {
        monitoringIndexOptions: [],
        communicationStateOptions: [],
        list: [],
        tableTitle: '采集监控',
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          terminalName: undefined,
          indexCode: undefined,
          isOnline: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '终端编码',
            field: 'terminalCode',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '终端名称',
            field: 'terminalName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '关联站点',
            field: 'siteNames',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '监测指标',
            field: 'indexCode',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.monitoringIndexOptions.find(el => el.key == row.indexCode).value
              },
            },
          },
          {
            title: '监测值',
            field: 'indexValue',
            minWidth: 70,
          },
          {
            title: '最后上传时间',
            field: 'dateTime',
            minWidth: 160,
          },
          {
            title: '通讯状态',
            field: 'isOnline',
            width: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <div class='common-status-box'>
                    <i
                      class={[
                        'common-status-icon',
                        row.isOnline == '0' ? 'common-status-abnormal' : 'common-status-completed',
                      ]}
                    ></i>
                    <span style={{ color: row.isOnline == 0 ? '#fc011a' : '#52c41a' }}>
                      {this.communicationStateOptions.find(el => el.key == row.isOnline).value}
                    </span>
                  </div>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getOptions('monitoringIndex').then(res => {
        this.monitoringIndexOptions = res.data
      })
      getOptions('communicationState').then(res => {
        this.communicationStateOptions = res.data
      })
      this.getList()
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.loading = true
        getMonitoringPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          terminalName: undefined,
          indexCode: undefined,
          isOnline: undefined,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
