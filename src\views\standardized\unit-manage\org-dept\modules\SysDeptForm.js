import { getDept, addDept, updateDept, getNextSort, listUser } from '../services'
import AntModal from '@/components/pt/dialog/AntModal'
export default {
  name: 'CreateForm',
  props: {
    deptTypeOptions: {
      type: Array,
      required: true,
    },
    deptTypeEditOptions: {
      type: Array,
      required: true,
    },
    deptOptions: {
      type: Array,
      required: true,
    },
  },
  components: {
    AntModal,
  },
  data() {
    const validateDeptName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('部门名不允许为空'))
      } else {
        callback()
      }
    }
    const validateParentId = (rule, value, callback) => {
      if (value.ids === '') {
        callback(new Error('上级部门不允许为空'))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      parentIdShow: false,
      hasChild: false,
      selectScope: 'all',
      deptTypeEnableValue: [],
      formTitle: '',
      currentRow: undefined,
      oldParentId: '',
      deptType: '',
      spinning: false,
      delayTime: 200,
      editStatus: 1, //2新增，3修改
      customStyle: 'background: #fff;ssborder-radius: 4px;margin-bottom: 24px;border: 0;overflow: hidden',

      userListOptions: [],
      // 表单参数
      form: {
        deptId: undefined,
        parentId: undefined,
        deptName: undefined,
        sort: '',
        type: '1',
        deptType: '1',
        remark: '',
        principalId: undefined,
      },
      open: false,
      rules: {
        parentId: [{ required: true, message: '上级部门不能为空', trigger: 'blur', validator: validateParentId }],
        type: [{ required: true, message: '部门类型不能为空', trigger: 'blur' }],
        deptName: [{ required: true, message: '部门名称不能为空', validator: validateDeptName, trigger: 'blur' }],
        sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
      },
    }
  },
  filters: {},
  created() {
    listUser({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
      this.userListOptions = res.data.data.map(el => ({ label: el.name, value: el.userId }))
    })
  },
  computed: {},
  watch: {
    // form: {
    //   handler(newVal, oldVal) {
    //     if (newVal) {
    //       this.onDeptTreeChange(newVal.roleId);
    //     }
    //   },
    //   immediate: true,
    //   deep: true
    // }
  },
  methods: {
    onDeptTypeChange(item) {
      if (item.target.value === 'company') {
        this.selectScope = 'nonDept'
      } else {
        this.selectScope = 'all'
      }
    },
    onDeptTreeChange(parentId, label, extra) {
      const deptId = this.form.deptId
      this.deptType = extra.triggerNode.$options.propsData.dataRef.type
      this.form.deptType = String(this.deptType)

      if (this.form.deptType == '2') {
        this.form.type = '2'
      }
      // if(type == '2'){
      //   this.deptTypeEditOptions = this.deptTypeOptions.filter(function (item) {return item.dictValue != '1'})
      //   // this.deptTypeOptions = this.deptTypeOptions.filter(function (item) {return item !== record.deptId;});
      // }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.$emit('close')
    },
    // 表单重置
    reset() {
      if (this.$refs.form !== undefined) {
        this.$refs.form.resetFields()
      }
    },
    /** 新增按钮操作  */
    handleAdd(row) {
      this.parentIdShow = true
      this.oldParentId = ''
      this.deptTypeEnableValue = this.deptTypeOptions.map(function (item) {
        return item.dictValue
      })
      if (row !== undefined) {
        this.currentRow = row
        this.oldParentId = row.deptId
        this.form.parentId = row.deptId
      }
      /** 获取最大编号 */
      getNextSort(row !== undefined ? row.deptId : '0').then(response => {
        this.form.sort = response.data
      })
      this.$emit('select-tree')
      this.formTitle = '添加部门'
      this.editStatus = 2
      this.open = true
    },
    setNodeData(data) {
      this.currentRow.deptName = data.deptName
      this.currentRow.deptCode = data.deptCode
      this.currentRow.leader = data.leader
      this.currentRow.phone = data.phone
      this.currentRow.email = data.email
      this.currentRow.status = data.status
      this.currentRow.treeSort = data.treeSort
      this.currentRow.createTime = data.createTime
      this.currentRow.deptType = data.deptType
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.currentRow = row
      this.open = true
      this.formTitle = '修改部门'
      this.editStatus = 3
      this.spinning = !this.spinning
      const deptId = row.deptId
      getDept(deptId).then(response => {
        this.oldParentId = response.data.parentId
        this.form = response.data
        if (response.data.parentId !== '0') {
          this.parentIdShow = true
        } else {
          this.parentIdShow = false
        }
        this.form.type = String(response.data.type)
        this.spinning = !this.spinning
      })
      this.$emit('select-tree', row)
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs.form.validate(valid => {
        if (valid) {
          const saveForm = JSON.parse(JSON.stringify(this.form))

          if (this.form.parentId !== undefined) {
            saveForm.parentId = this.form.parentId
          }
          if (this.editStatus == 3) {
            updateDept(saveForm)
              .then(response => {
                if (response.code == 200 && response.success == true) {
                  this.$message.success('修改成功', 3)
                  if (this.oldParentId !== this.form.parentId) {
                    // 如果修改父节点则刷新树
                    this.$emit('ok')
                  } else {
                    // 设置节点数据
                    this.$emit('ok')
                  }
                  this.cancel()
                } else {
                  this.$message.success('修改失败:', response.message)
                  this.cancel()
                }
              })
              .catch(res => {
                console.log('修改失败:', res)
              })
              .finally(() => this.$emit('close'))
          }
          if (this.editStatus == 2) {
            addDept(saveForm)
              .then(response => {
                if (response.code == 200 && response.success == true) {
                  this.$message.success('新增成功', 3)
                  // 修改父节点后刷新整个树，如果直接添加子节点不更换父节点则追加节点
                  if (this.oldParentId !== this.form.parentId) {
                    // 如果修改父节点则刷新树
                    this.$emit('ok')
                  } else {
                    this.$emit('ok')
                  }
                  this.cancel()
                } else {
                  this.$message.success('新增失败:', response.message)
                  this.cancel()
                }
              })
              .finally(() => this.$emit('close'))
          }
        } else {
          return false
        }
      })
    },
    onLoadData(treeNode) {
      return new Promise(resolve => {
        if (treeNode.dataRef.children) {
          resolve()
          return
        }
        listDeptTree(treeNode.dataRef.id, 1).then(response => {
          treeNode.dataRef.children = response.data
          resolve()
        })
      })
    },
  },
}
