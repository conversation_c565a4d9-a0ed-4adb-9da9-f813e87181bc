<template>
  <div class="common-table-page" :style="isDetail ? { height: '100%' } : {}">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="名称">
        <a-input
          v-model="queryParam.inspectionName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="检查类型">
        <a-select
          show-search
          allow-clear
          placeholder="请选择"
          v-model="queryParam.inspectionType"
          option-filter-prop="children"
        >
          <a-select-option v-for="item in inspectionTypeOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="审核状态">
        <a-select show-search placeholder="请选择" v-model="queryParam.status" option-filter-prop="children">
          <a-select-option v-for="item in statusOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="工程名称">
        <a-tree-select
          :disabled="isDetail"
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-item>
      <a-form-item label="计划完成时间" v-if="observationType == 3">
        <a-range-picker
          allow-clear
          :value="planTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          :isInModal="isDetail"
          :modalHeight="modalHeight"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :inspectionTypeOptions="inspectionTypeOptions"
          :projectOptions="projectOptions"
          :planTypeOptions="planTypeOptions"
          :inspectionStatusOptions="inspectionStatusOptions"
          :observationType="observationType"
          :isProperty="isProperty"
          :statusOptions="statusOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :inspectionTypeOptions="inspectionTypeOptions"
          :projectOptions="projectOptions"
          :planTypeOptions="planTypeOptions"
          :inspectionStatusOptions="inspectionStatusOptions"
          :observationType="observationType"
          :isProperty="isProperty"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />

        <FormExamine
          v-if="showFormExamine"
          ref="formExamineRef"
          @ok="onOperationComplete"
          @close="showFormExamine = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getInspectionPage, deleteInspection } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormExamine from './modules/FormExamine.vue'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'SafetyInspection',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
      FormExamine,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      type: {
        type: String,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        localUserId: '',
        isProperty: '',
        observationType: 1,
        showFormExamine: false,
        statusOptions: [],
        inspectionStatusOptions: [
          { key: 1, value: '未判定' },
          { key: 2, value: '正常' },
          { key: 3, value: '异常' },
        ],
        planTypeOptions: [
          { key: 1, value: '计划内' },
          { key: 2, value: '计划外' },
        ],
        isChecked: false,
        inspectionTypeOptions: [],
        inspectionTypes: [],
        inspectionStatuses: [],
        planTypes: [],
        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        planTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '安全检查',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          endTime: '',
          inspectionMenu: 0,
          inspectionName: '',
          inspectionType: undefined,
          isProperty: '',
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          sort: [],
          startTime: '',
          status: undefined,
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '检查名称',
            field: 'inspectionName',
            minWidth: 120,
            showOverflow: true,
          },
          {
            title: '计划类型',
            field: 'planType',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.planTypes[row.planType]?.value || ''
              },
            },
          },
          {
            title: '检查类型',
            field: 'inspectionType',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.inspectionTypes[row.inspectionType]?.value || ''
              },
            },
          },

          {
            title: '计划完成时间',
            minWidth: 120,
            field: 'planCompleteTime',
          },
          {
            title: '检查结果',
            field: 'inspectionStatus',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                let statusPointColor = 'common-status-incomplete'
                if (row.inspectionStatus == 2) {
                  statusPointColor = 'common-status-completed'
                } else if (row.inspectionStatus == 3) {
                  statusPointColor = 'common-status-abnormal'
                }
                return (
                  <div class='common-status-box'>
                    <i class={['common-status-icon', statusPointColor]}></i>
                    <span>{this.inspectionStatuses[row.inspectionStatus]?.value}</span>
                  </div>
                )
              },
            },
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 90,
          },
          {
            title: '创建人',
            field: 'createdUserName',
            minWidth: 90,
          },
          {
            title: '创建时间',
            field: 'createdTime',
            minWidth: 170,
          },
          {
            title: '审核状态',
            field: 'status',
            minWidth: 80,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.statusOptions[row.status]?.value
              },
            },
          },

          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 200,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    {!this.isDetail && <a-divider type='vertical' />}

                    {!this.isDetail &&
                      row.status === 0 &&
                      this.observationType != 3 &&
                      row.createdUserId == this.localUserId && (
                        <span key='1'>
                          <a onClick={() => this.handleEdit(row)}>修改</a>
                          <a-divider type='vertical' />
                        </span>
                      )}
                    {!this.isDetail && row.status === 0 && this.observationType == 3 && (
                      <span key='2'>
                        <a onClick={() => this.handleEdit(row)}>修改</a>
                        <a-divider type='vertical' />
                      </span>
                    )}

                    {!this.isDetail && row.status === 0 && (
                      <span>
                        <a v-permission={`${this.$route.meta.menuId}-audit`} onClick={() => this.handleExamine(row)}>
                          审核
                        </a>
                        <a-divider v-permission={`${this.$route.meta.menuId}-audit`} type='vertical' />
                      </span>
                    )}
                    {!this.isDetail && <a onClick={() => this.handleDelete(row)}>删除</a>}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.observationType = this.$route.meta.query?.type || this.type
      this.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
      // inspectionMenu	菜单(1定期检查2专项检查3安全检查)
      // isProperty	是否物业单位 'true' null
      this.queryParam.inspectionMenu = this.observationType
      this.queryParam.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
      this.tableTitle =
        this.observationType == 1
          ? '定期检查'
          : this.observationType == 2
          ? '专项检查'
          : this.observationType == 3 && this.isProperty
          ? '安全管理'
          : '安全检查'

      /*检查类型
      inspectionSpecialType 专项检查 inspectionRegularType 定期检查 inspectionType 安全检查
      */
      this.localUserId = localStorage.getItem('userId')
      if (this.observationType == '1') {
        getOptions('inspectionRegularType').then(res => {
          this.inspectionTypeOptions = res.data
          this.inspectionTypes = getFlatTreeMap(this.inspectionTypeOptions, 'key')
        })
      } else if (this.observationType == '2') {
        getOptions('inspectionSpecialType').then(res => {
          this.inspectionTypeOptions = res.data
          this.inspectionTypes = getFlatTreeMap(this.inspectionTypeOptions, 'key')
        })
      } else if (this.observationType == '3') {
        getOptions('inspectionType').then(res => {
          this.inspectionTypeOptions = res.data
          this.inspectionTypes = getFlatTreeMap(this.inspectionTypeOptions, 'key')
        })
      }
      getOptions('auditStatus').then(res => {
        this.statusOptions = res.data
      })
      this.getList()

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
      this.planTypes = getFlatTreeMap(this.planTypeOptions, 'key')
      this.inspectionStatuses = getFlatTreeMap(this.inspectionStatusOptions, 'key')
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        // this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      onRangeChange(value, planTimes) {
        this.planTimes = value
        if (planTimes.length == 0) {
          return
        }
        this.queryParam.startTime = planTimes[0] ? moment(planTimes[0]).format('YYYY-MM-DD') : undefined
        this.queryParam.endTime = planTimes[1] ? moment(planTimes[1]).format('YYYY-MM-DD') : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getInspectionPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.inspectionId)
        this.names = valObj.records.map(item => item?.inspectionName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.planTimes = []
        this.queryParam = {
          ...this.queryParam,
          endTime: '',
          inspectionName: '',
          inspectionType: undefined,
          status: undefined,
          pageNum: 1,
          projectId: undefined,
          sort: [],
          startTime: '',
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 审核 */
      handleExamine(record) {
        this.showFormExamine = true
        this.$nextTick(() => this.$refs.formExamineRef.handle(record))
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.inspectionId ? [row?.inspectionId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteInspection({ inspectionIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
