<template>
  <div style="height: 100%; display: flex; flex-direction: column; margin-top: 16px">
    <a-form-model labelAlign="right" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
      <a-row :gutter="16">
        <a-col :span="12" v-for="(item, index) in detail.inputParameter.inOutWaters" :key="index">
          <a-form-model-item :label="item.name">
            <a-input-number
              disabled
              size="small"
              v-model="detail.inputParameter.inOutWaters[index].wlv"
              :min="0"
              :step="0.1"
            />
            m
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <div style="flex: 1">
      <VxeTable
        size="small"
        :columns="columns"
        :tableData="list"
        :loading="loading"
        :isDrop="false"
        :tablePage="false"
        :isShowTableHeader="false"
      />
    </div>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'
  import { getDispathProjectList } from '../../../services.js'

  export default {
    name: 'Type1',
    components: { VxeTable },
    props: ['detail'],
    data() {
      return {
        loading: false,
        columns: [],
        list: [],
      }
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {
      getDispathProjectList({
        dispathType: this.detail.dispathType,
        fcstRange: this.detail.fcstRange,
        startTime: this.detail.startTime,
        endTime: this.detail.endTime,
      }).then(res => {
        this.list = JSON.parse(JSON.stringify(this.detail.inputParameter.projectFlows)).map(el => {
          const obj = {}
          el.projects.forEach(ele => (obj[ele.projectCode] = { ...ele }))
          return { ...el, projectsObj: obj }
        })

        this.columns = [
          {
            title: '时间',
            minWidth: 150,
            field: 'tm',
          },
          ...Object.keys(res.data[0].map).map(element => {
            return {
              title: element,
              headerAlign: 'center',
              children: res.data[0].map[element].map(el => ({
                title: el.projectName,
                minWidth: 180,
                headerAlign: 'center',
                slots: {
                  default: ({ row, rowIndex }) => (
                    <div class='cell-box'>
                      <a-input-number
                        disabled
                        value={this.list[rowIndex].projectsObj[el.projectCode].value}
                        size='small'
                        step={0.1}
                        min={0}
                        onChange={value => {
                          this.list[rowIndex].projectsObj[el.projectCode].value = value
                        }}
                      />
                      <a
                        style='margin-left: 5px'
                        onClick={() =>
                          this.list.forEach((item, index) => {
                            if (index > rowIndex) {
                              item.projectsObj[el.projectCode].value =
                                this.list[rowIndex].projectsObj[el.projectCode].value
                            }
                          })
                        }
                      >
                        向下填充
                      </a>
                    </div>
                  ),
                },
              })),
            }
          }),
        ]
      })
    },
    methods: {},
  }
</script>

<style lang="less" scoped>
  .cell-box {
    a {
      display: none;
    }
    &:hover {
      a {
        display: inline;
      }
    }
  }
</style>
