<template>
  <!-- 代表站配置 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :modalWidth="windowSize.width"
    @cancel="cancel"
    :modalHeight="windowSize.height"
    :footer="false"
  >
    <div slot="content">
      <!-- 筛选栏 -->
      <div class="common-table-page">
        <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
          <a-form-item label="汇入单位">
            <a-input
              v-model="queryParam.receivingUnit"
              placeholder="请输入汇入单位"
              allow-clear
              @keyup.enter.native="handleQuery"
            />
          </a-form-item>
          <a-form-item label="支出类型">
            <a-select
              allowClear
              v-model="queryParam.payType"
              placeholder="请选择"
              :options="expenditureType"
            ></a-select>
          </a-form-item>
          <a-form-item label="年份">
            <a-select
              allowClear
              v-model="queryParam.payYear"
              format="YYYY-MM-DD"
              formatValue="YYYY-MM-DD"
              placeholder="请选择"
              :options="payYearOptions"
            ></a-select>
          </a-form-item>

          <template #table>
            <VxeTable
              ref="vxeTableRef"
              :tableTitle="tableTitle"
              :columns="columns"
              :tableData="list"
              :loading="loading"
              @refresh="getList"
              :tablePage="false"
            >
              <div class="table-operations" slot="button">
                <a-button type="primary" @click="handleAdd()">
                  <a-icon type="plus" />
                  新增
                </a-button>
                <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
                <!-- <a-button type="danger" v-if="isChecked" @click="handleDelete">
                  <a-icon type="delete" />
                  删除
                </a-button> -->
              </div>
            </VxeTable>
          </template>
        </VxeTableForm>
      </div>

      <FormPayment
        v-if="showFormPayment"
        ref="formPaymentRef"
        :expenditureType="expenditureType"
        @ok="onOperationComplete"
        @close="showFormPayment = false"
      />
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getPaymentYearList, deleteBudgetPayment, getBudgetPayment } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTableForm from '@/components/VxeTableForm'
  import VxeTable from '@/components/VxeTable'
  import FormPayment from './FormPayment'

  export default {
    name: 'PaymentManage',
    components: { AntModal, VxeTableForm, VxeTable, FormPayment },
    data() {
      return {
        open: false,
        showFormPayment: false,
        expenditureType: [],
        payYearOptions: [],

        list: [],
        tableTitle: '支付管理',
        isChecked: false,
        formTitle: '',

        windowSize: {},
        ids: [],
        names: [],
        loading: false,
        modalLoading: false,
        exportLoading: false,
        queryParam: {
          payType: undefined,
          payYear: undefined,
          receivingUnit: '',
          projectId: undefined,
        },
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '发票号',
            field: 'invoiceNo',
            showOverflow: 'tooltip',
          },
          {
            title: '支付内容',
            field: 'payContent',
            showOverflow: 'tooltip',
          },
          {
            title: '汇入单位',
            field: 'receivingUnit',
          },
          {
            title: '支出类型',
            field: 'payType',
          },
          {
            title: '支付金额(元)',
            field: 'payAmount',
          },
          {
            title: '经办人',
            field: 'operator',
          },
          {
            title: '报送人',
            field: 'sender',
          },
          {
            title: '支付日期',
            field: 'payDate',
          },
          {
            title: '备注',
            field: 'remark',
          },
          {
            title: '操作',
            field: 'operate',
            width: 96,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    filters: {},
    created() {
      this.windowSize = {
        width: `${parseInt(window.innerWidth * 0.9)}`,
        height: `${parseInt(window.innerHeight * 0.95)}`,
      }
      getOptions('customBidPrjPayOutType').then(res => {
        this.expenditureType = res?.data?.map(el => ({ label: el.key, value: el.value }))
      })
    },
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 修改按钮操作 */
      handlePaymentManage(record) {
        this.open = true
        this.formTitle = '支付管理'
        this.queryParam.projectId = record.projectId
        this.getYearData()
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showFormPayment = false
        this.loading = true
        getBudgetPayment(this.queryParam).then(response => {
          this.list = response?.data
          this.loading = false
        })
      },
      getYearData() {
        getPaymentYearList({ projectId: this.queryParam.projectId }).then(res => {
          this.payYearOptions = res?.data?.map(el => ({ label: el, value: el }))
        })
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          payType: undefined,
          payYear: undefined,
          receivingUnit: '',
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getYearData()
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showFormPayment = true
        this.$nextTick(() => this.$refs.formPaymentRef.handleFormPayment(this.queryParam.projectId))
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormPayment = true
        this.$nextTick(() => this.$refs.formPaymentRef.handleFormPayment(this.queryParam.projectId, record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const projectPayIds = row.projectPayId ? [row.projectPayId] : this.ids
        const names = row.invoiceNo

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中发票号为"' + names + '"的数据',
          onOk() {
            return deleteBudgetPayment({ projectPayIds: projectPayIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.getList()
            })
          },
          onCancel() {},
        })
      },

      // 导出
      handleExport() {},
    },
  }
</script>
<style lang="less" scoped>
  .common-table-page {
    height: calc(100vh - 182px);
  }
</style>
