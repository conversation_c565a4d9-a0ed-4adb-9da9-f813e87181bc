<template>
  <a-drawer
    width="480"
    :title="formTitle"
    :label-col="4"
    :wrapper-col="14"
    :visible="open"
    :body-style="{ height: 'calc(100vh - 100px)', overflow: 'auto' }"
    @close="cancel"
  >
    <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
      <a-spin :spinning="spinning" :delay="delayTime" tip="Loading...">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="上级部门" prop="parentId">
              <a-tree-select
                v-model="form.parentId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="deptOptions"
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'deptName',
                  key: 'deptId',
                  value: 'deptId',
                }"
                @change="onDeptTreeChange"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="部门名称" prop="deptName">
              <a-input v-model="form.deptName" placeholder="请输入" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="单位负责人" prop="unitId">
              <a-select
                allowClear
                show-search
                option-filter-prop="children"
                v-model="form.principalId"
                placeholder="请选择"
                :options="userListOptions"
              ></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="排序" prop="sort">
              <a-input-number v-model="form.sort" :min="0" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="部门类别" prop="type">
              <a-radio-group v-model="form.type" button-style="solid">
                <a-radio-button value="1" disabled>机构</a-radio-button>
                <a-radio-button value="2" disabled>部门</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="备注" prop="remark">
              <a-input v-model="form.remark" placeholder="请输入" type="textarea" allow-clear />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-spin>
      <div class="bottom-control">
        <a-space>
          <a-button type="primary" @click="submitForm">保存</a-button>
          <a-button @click="cancel">取消</a-button>
        </a-space>
      </div>
    </a-form-model>
  </a-drawer>
</template>

<script lang="jsx">
  import sysDeptForm from './SysDeptForm.js'
  export default {
    ...sysDeptForm,
  }
</script>
