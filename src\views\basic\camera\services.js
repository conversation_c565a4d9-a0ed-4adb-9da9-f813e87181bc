import request from '@/utils/request'

// 视频监控-列表分页查询
export function getCameraPage(data) {
  return request({
    url: '/base/camera/page',
    method: 'post',
    data,
  })
}

// 视频监控-删除
export function deleteCamera(params) {
  return request({
    url: '/base/camera/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 视频监控-新增
export function addCamera(data) {
  return request({
    url: '/base/camera/add',
    method: 'post',
    data,
  })
}

// 视频监控-更新
export function updateCamera(data) {
  return request({
    url: '/base/camera/update',
    method: 'post',
    data,
  })
}

// 视频监控-详情
export function getCamera(params) {
  return request({
    url: '/base/camera/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水利对象分类-获取树 监测站点树
export function getBaseSite() {
  return request({
    url: 'base/site/list', //'/base/site/category/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
// 水系树
export function getRiverSystemTree(params) {
  return request({
    url: '/base/riverSystem/category/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 其他对象树
export function getOtherObjectTree(params) {
  return request({
    url: '/base/otherObject/category/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
