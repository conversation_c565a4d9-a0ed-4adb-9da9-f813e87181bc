<template>
  <div style="flex: 1; display: flex; flex-direction: column">
    <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 10px">
      <a-radio-group v-model="scene" button-style="solid" @change="onRadioChange">
        <a-radio-button :value="1">实测降雨</a-radio-button>
        <a-radio-button :value="2" v-if="baseInfo.scene === 2">未来预报</a-radio-button>
      </a-radio-group>

      <div style="flex: 1; display: flex; align-items: center; margin: 0 40px" v-if="scene === 2">
        <span style="margin-right: 10px">缩放系数</span>
        <a-slider
          style="flex: 1"
          :defaultValue="9"
          :min="0"
          :max="18"
          :tooltipVisible="false"
          @change="onSliderChange"
        />
        <div class="slide-val" style="width: 50px">{{ slideVal }}倍</div>

        <a-button style="margin-left: 10px" type="primary" size="small" @click="resetZoom">重置</a-button>
      </div>
      <div style="display: flex; align-items: center; width: 220px">
        <span>总降雨量&nbsp;</span>
        <!-- <div class="slide-val">{{ sumRain }}</div> -->
        <a-input-number size="small" v-model="sumRain" @change="changeSumRain" />
        mm
      </div>
    </div>
    <div style="display: flex; flex: 1">
      <div style="flex: 1">
        <VxeTable
          v-if="columns.length > 0"
          :key="tableKey"
          ref="vxeTableRef"
          :tableTitle="'降雨过程'"
          :isShowSetBtn="false"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isDrop="false"
          :tablePage="false"
          :showFooter="true"
          :footerData="footerData"
          :footer-row-style="{ background: '#F8F8F9', fontWeight: 'bold' }"
          :scrollY="{ enabled: true, gt: 0 }"
          :scrollX="{ enabled: true, gt: 0 }"
        >
          <div class="table-operations" slot="button">
            <a-button v-if="scene === 2" type="link" @click="handleImport">批量导入</a-button>
          </div>
        </VxeTable>
      </div>

      <div style="flex: 1; display: flex; flex-direction: column">
        <div style="display: flex; align-items: center; justify-content: space-between; padding: 14px 0">
          <div style="font-size: 16px; font-weight: 700">降雨统计</div>
          <a-select
            v-if="scene === 1"
            v-model="chartSiteSelect"
            style="width: 200px"
            :options="checkedSites"
            placeholder="请选择"
            allowClear
          />
        </div>
        <div style="flex: 1; display: flex; flex-direction: column">
          <div
            style="display: flex; align-items: center; justify-content: end"
            :style="{ opacity: chartCurrentSelect ? 1 : 0 }"
          >
            当前选中:&nbsp; {{ chartCurrentSelect?.seriesName }}&nbsp;&nbsp;{{ chartCurrentSelect?.name }}&nbsp;&nbsp;
            <a-input-number :step="0.1" :min="0" v-model="chartIptVal" @change="changeChartIptVal" />
          </div>
          <div style="flex: 1">
            <BaseEchart :height="'100%'" style="height: 100%" @getEchartsIns="getEchartsIns" :option="chartOptions" />
          </div>
        </div>
      </div>
    </div>

    <ImportDrawer
      :visible.sync="showImportDrawer"
      :timeList="list.map(item => item.tm)"
      @save="handleBatchImportSave"
    />
  </div>
</template>

<script lang="jsx">
  import { getRainfallList, getFutureRainfallList } from '../../services'
  import VxeTable from '@/components/VxeTable/index.vue'
  import { sliderOptions } from '../../config.js'
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import ImportDrawer from './ImportDrawer/index.vue'
  import * as _ from 'lodash'

  export default {
    name: 'RainfallProcess',
    props: ['baseInfo'],
    components: { VxeTable, BaseEchart, ImportDrawer },
    data() {
      return {
        tableKey: 1,
        showImportDrawer: false,

        loading: false,
        list: [],
        list1: [],
        initList1: [],
        columns: [],
        footerData: [],

        list2: [],
        initList2: [],

        scene: this.baseInfo.scene,
        slideVal: 1,
        preSlideVal: 1,
        sumRain: 0,
        preSumRain: 0,

        checkedSites: [],
        chartSiteSelect: undefined,

        echartsIns: null,
        chartDataSource: [],
        chartCurrentSelect: undefined,
        chartIptVal: undefined,
      }
    },
    computed: {
      chartOptions() {
        return {
          grid: {
            left: '4%',
            right: '4%',
            bottom: '5%',
            top: '8%',
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            textStyle: {
              color: '#000',
            },
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
          },
          xAxis: {
            type: 'category',
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: '#000',
              },
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#000',
              },
            },
          },
          yAxis: {
            type: 'value',
            name: '雨量(mm)',
            axisPointer: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#BBB',
              },
            },
            axisLabel: {
              textStyle: {
                color: '#000',
              },
            },
          },
          dataZoom: [
            {
              type: 'inside',
            },
          ],
          legend: { show: false },
          series: this.chartDataSource?.map((item, index) => {
            return {
              type: 'bar',
              barMaxWidth: 8,
              name: item.name,
              itemStyle: {
                borderRadius: [2, 2, 0, 0], //（顺时针左上，右上，右下，左下）
                color: item.color || '#1978E5',
              },
              data: item.data,
            }
          }),
        }
      },
      // sumRain() {
      //   if (this.scene === 1) {
      //     let sumRain = 0
      //     this.checkedSites.forEach(el => {
      //       sumRain += this.list.reduce((pre, cur) => pre + cur.sitesObj[el.value].rain, 0)
      //     })
      //     return +sumRain?.toFixed?.(1)
      //   } else {
      //     let sumRain = this.list.reduce((pre, cur) => pre + cur.sitesObj[Object.keys(cur.sitesObj)[0]].rain, 0)
      //     return +sumRain?.toFixed?.(1)
      //   }
      // },
    },
    watch: {
      list: {
        handler(newVal) {
          if (this.scene === 1) {
            this.list1 = JSON.parse(JSON.stringify(newVal))
          } else {
            this.list2 = JSON.parse(JSON.stringify(newVal))
          }

          if (this.scene === 1) {
            let obj = {}
            Object.keys(newVal[0].sitesObj).forEach(el => {
              const total = newVal.reduce((pre, cur) => pre + cur.sitesObj[el].rain, 0)
              if (typeof total === 'string') {
                obj[el] = undefined
              } else {
                obj[el] = +total.toFixed(1)
              }
            })
            this.footerData = [obj]

            this.$nextTick(() => {
              this.chartDataSource = this.checkedSites
                .filter(el => (this.chartSiteSelect ? el.value === this.chartSiteSelect : true))
                .map(el => {
                  return {
                    name: el.label,
                    data: newVal.map(item => [item.tm, item.sitesObj[el.value].rain, item.sitesObj[el.value].siteId]),
                  }
                })
            })
          } else {
            let obj = {}
            Object.keys(newVal[0].sitesObj).forEach(el => {
              const total = newVal.reduce((pre, cur) => pre + cur.sitesObj[el].rain, 0)
              if (typeof total === 'string') {
                obj[el] = undefined
              } else {
                obj[el] = +total.toFixed(1)
              }
            })
            this.footerData = [obj]

            this.$nextTick(() => {
              this.chartDataSource = newVal[0].sites.map(el => {
                return {
                  name: '时段降雨量',
                  data: newVal.map(item => [item.tm, item.sitesObj[el.siteId].rain, item.sitesObj[el.siteId].siteId]),
                }
              })
            })
          }

          if (this.scene === 1) {
            let sumRain = 0
            this.checkedSites.forEach(el => {
              sumRain += this.list.reduce((pre, cur) => pre + cur.sitesObj[el.value].rain, 0)
            })
            this.sumRain = +sumRain?.toFixed?.(1)
          } else {
            let sumRain = this.list.reduce((pre, cur) => pre + cur.sitesObj[Object.keys(cur.sitesObj)[0]].rain, 0)
            this.sumRain = +sumRain?.toFixed?.(1)
          }
          this.preSumRain = this.sumRain
        },
        deep: true,
      },
      chartSiteSelect(newVal) {
        this.$nextTick(() => {
          this.chartDataSource = this.checkedSites
            .filter(el => (newVal ? el.value === newVal : true))
            .map(el => {
              return {
                name: el.label,
                data: this.list.map(item => [item.tm, item.sitesObj[el.value].rain, item.sitesObj[el.value].siteId]),
              }
            })
        })
      },
    },
    activated() {
      if (this.list.length > 0) return

      this.scene = this.baseInfo.scene

      if (this.baseInfo.scene === 1) {
        getRainfallList({ ...this.baseInfo, scene: 1 }).then(res => {
          this.checkedSites = res.data[0].sites.map(el => ({ label: el.siteName, value: el.siteId }))

          this.list1 = res.data.map(el => {
            const obj = {}
            el.sites.forEach(ele => (obj[ele.siteId] = { ...ele, rain: ele.rain }))
            return { tm: el.tm, sites: el.sites, sitesObj: obj }
          })
          this.initList1 = JSON.parse(JSON.stringify(this.list1))
          this.list = JSON.parse(JSON.stringify(this.list1))

          this.dealColumns()
        })
      }

      if (this.baseInfo.scene === 2) {
        getFutureRainfallList({ ...this.baseInfo }).then(res => {
          this.checkedSites = res.data[0].sites.map(el => ({ label: el.siteName, value: el.siteId }))

          this.list1 = res.data.map(el => {
            const obj = {}
            el.sites.forEach(ele => (obj[ele.siteId] = { ...ele, rain: ele.rain }))
            return { tm: el.tm, sites: el.sites, sitesObj: obj }
          })
          this.initList1 = JSON.parse(JSON.stringify(this.list1))
        })
        getRainfallList({ ...this.baseInfo, scene: 2 }).then(res => {
          this.list2 = res.data.map(el => {
            const obj = {}
            el.sites.forEach(ele => (obj[ele.siteId] = { ...ele, rain: ele.rain }))
            return { tm: el.tm, sites: el.sites, sitesObj: obj }
          })
          this.initList2 = JSON.parse(JSON.stringify(this.list2))

          this.list = JSON.parse(JSON.stringify(this.list2))
          this.dealColumns()
        })
      }
    },
    methods: {
      dealColumns() {
        if (this.scene === 1) {
          this.columns = [
            { title: '时间', field: 'tm', minWidth: 150, slots: { footer: ({ row }) => '总降雨量' } },
            ...this.list[0].sites.map(el => ({
              title: el.siteName + '(mm)',
              field: `sitesObj.${el.siteId}.rain`,
              minWidth: 180,
              slots: {
                header: ({ row }) => {
                  return (
                    <a-checkbox
                      checked={this.checkedSites.some(item => item.value === el.siteId)}
                      onChange={e => {
                        if (this.checkedSites.some(item => item.value === el.siteId)) {
                          this.checkedSites = this.checkedSites.filter(item => item.value !== el.siteId)

                          if (e.target.checked === false) {
                            this.chartSiteSelect = undefined
                            this.chartDataSource = this.checkedSites.map(el => {
                              return {
                                name: el.label,
                                data: this.list.map(item => [
                                  item.tm,
                                  item.sitesObj[el.value].rain,
                                  item.sitesObj[el.value].siteId,
                                ]),
                              }
                            })
                          }
                        } else {
                          /* 按顺序添加 */
                          this.checkedSites = this.list[0].sites
                            .map(ele => ({ label: ele.siteName, value: ele.siteId }))
                            .filter(item =>
                              [...this.checkedSites, { label: el.siteName, value: el.siteId }].some(
                                elem => elem.value === item.value,
                              ),
                            )
                          this.chartDataSource = this.checkedSites.map(el => {
                            return {
                              name: el.label,
                              data: this.list.map(item => [
                                item.tm,
                                item.sitesObj[el.value].rain,
                                item.sitesObj[el.value].siteId,
                              ]),
                            }
                          })
                        }
                      }}
                    >
                      {el.siteName}
                    </a-checkbox>
                  )
                },
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='cell-box'>
                      <a-input-number
                        size='small'
                        step={0.1}
                        min={0}
                        v-model={this.list[rowIndex].sitesObj[el.siteId].rain}
                      />
                      <a
                        style='margin-left: 5px'
                        onClick={() =>
                          this.list.forEach((item, index) => {
                            if (index > rowIndex) {
                              item.sitesObj[el.siteId] = { ...this.list[rowIndex].sitesObj[el.siteId] }
                            }
                          })
                        }
                      >
                        向下填充
                      </a>
                    </div>
                  )
                },
                footer: ({ row }) => {
                  return this.footerData.reduce((pre, cur) => pre + cur[`${el.siteId}`], 0)
                },
              },
            })),
          ]
        } else {
          this.columns = [
            { title: '时间', field: 'tm', minWidth: 150, slots: { footer: ({ row }) => '总降雨量' } },
            ...this.list[0].sites.map(el => ({
              title: '时段降雨量(mm)',
              minWidth: 150,
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='cell-box'>
                      <a-input-number
                        size='small'
                        step={0.1}
                        min={0}
                        v-model={this.list[rowIndex].sitesObj[el.siteId].rain}
                      />
                      <a
                        style='margin-left: 5px'
                        onClick={() =>
                          this.list.forEach((item, index) => {
                            if (index > rowIndex) {
                              item.sitesObj[el.siteId] = { ...this.list[rowIndex].sitesObj[el.siteId] }
                            }
                          })
                        }
                      >
                        向下填充
                      </a>
                    </div>
                  )
                },
                footer: ({ row }) => {
                  return this.footerData.reduce((pre, cur) => pre + cur[`${el.siteId}`], 0)
                },
              },
            })),
          ]
        }

        this.tableKey += 1
      },
      onRadioChange(e) {
        if (e.target.value === 1) {
          this.list = JSON.parse(JSON.stringify(this.list1))
          this.dealColumns()
        } else {
          this.list = JSON.parse(JSON.stringify(this.list2))
          this.dealColumns()
        }
      },
      changeChartIptVal: _.debounce(function (val) {
        this.list = this.list.map(item => {
          if (this.chartCurrentSelect.name === item.tm) {
            return {
              ...item,
              sitesObj: {
                ...item.sitesObj,
                [this.chartCurrentSelect.value[2]]: {
                  ...item.sitesObj[this.chartCurrentSelect.value[2]],
                  rain: val,
                },
              },
            }
          }
          return item
        })
      }, 800),
      getEchartsIns(echartsIns) {
        this.echartsIns = echartsIns
        this.echartsIns.on('click', params => {
          this.chartIptVal = params.value[1]

          if (
            this.chartCurrentSelect?.seriesName === params.seriesName &&
            this.chartCurrentSelect?.name === params.name
          ) {
            this.chartCurrentSelect = undefined
          } else {
            this.chartCurrentSelect = params
          }

          this.echartsIns.setOption({
            ...this.chartOptions,
            series: this.chartOptions.series.map((item, index) => {
              return {
                ...item,
                itemStyle: {
                  color: selfParams => {
                    if (selfParams.seriesName === params.seriesName && selfParams.name === params.name) {
                      if (!!this.chartCurrentSelect) {
                        return '#19D7E5FF'
                      }
                      return '#1978E5'
                    }
                    return '#1978E5'
                  },
                },
              }
            }),
          })
        })
      },
      onSliderChange: _.debounce(function (val) {
        this.slideVal = sliderOptions[val]

        if (this.scene === 2) {
          this.list = this.list.map(item => {
            const obj = {}
            Object.keys(item.sitesObj).forEach(key => {
              obj[key] = {
                ...item.sitesObj[key],
                rain: +((item.sitesObj[key].rain / this.preSlideVal) * this.slideVal).toFixed(1),
              }
            })
            return {
              ...item,
              sitesObj: obj,
            }
          })
          this.preSlideVal = this.slideVal
        }
      }, 400),
      resetZoom() {
        if (this.scene === 1) {
          this.list = JSON.parse(JSON.stringify(this.initList1))
        } else {
          this.list = JSON.parse(JSON.stringify(this.initList2))
        }
      },

      changeSumRain: _.debounce(function (val) {
        this.$nextTick(() => {
          const scale = this.sumRain / (this.preSumRain || 1)

          if (this.scene === 2) {
            this.list = this.list.map(item => {
              const obj = {}
              Object.keys(item.sitesObj).forEach(key => {
                obj[key] = {
                  ...item.sitesObj[key],
                  rain: +(item.sitesObj[key].rain * scale).toFixed(1),
                }
              })
              return {
                ...item,
                sitesObj: obj,
              }
            })
          }

          this.preSumRain = this.sumRain
        })
      }, 500),

      handleImport() {
        this.showImportDrawer = true
        this.$nextTick(() => {
          this.$refs.importDrawerRef?.handleOpen()
        })
      },

      // 批量导入保存
      handleBatchImportSave(importData) {
        this.list.forEach((item, idx) => {
          this.list[idx].sitesObj[item.sites[0].siteId].rain = importData[idx].rainfall
        })

        this.showImportDrawer = false
        this.$message.success('批量导入成功')
      },

      save() {
        this.$emit('saveData', {
          rains: this.list.map(el => {
            const arr = []
            Object.keys(el.sitesObj).forEach(key => {
              arr.push({ siteId: key, ...el.sitesObj[key] })
            })
            return { tm: el.tm, sites: arr }
          }),
          futureRains:
            this.baseInfo.scene === 1
              ? undefined
              : this.list1.map(el => {
                  const arr = []
                  Object.keys(el.sitesObj).forEach(key => {
                    arr.push({ siteId: key, ...el.sitesObj[key] })
                  })
                  return { tm: el.tm, sites: arr }
                }),
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .cell-box {
    a {
      display: none;
    }
    &:hover {
      a {
        display: inline;
      }
    }
  }

  .slide-val {
    margin-left: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    text-align: center;
  }
</style>
