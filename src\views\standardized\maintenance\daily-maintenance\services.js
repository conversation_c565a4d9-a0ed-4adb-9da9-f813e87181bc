import request from '@/utils/request'

// 工程管理_维修养护_维养计划-列表分页查询
export function getPlansPage(data) {
  return request({
    url: '/prjstd/curing/page',
    method: 'post',
    data,
  })
}
// 增加
export function addPlans(data) {
  return request({
    url: '/prjstd/curing/add',
    method: 'post',
    data,
  })
}
// 详情
export function getPlansById(params) {
  return request({
    url: '/prjstd/curing/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editPlans(data) {
  return request({
    url: '/prjstd/curing/update',
    method: 'post',
    data,
  })
}
// 删除
export function deletePlans(params) {
  return request({
    url: '/prjstd/curing/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 验收
export function checkPlans(params) {
  return request({
    url: '/prjstd/curing/check',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

export function getProjectListByPropertyId(params) {
  return request({
    url: '/prjstd/maintenance/getProjectList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
