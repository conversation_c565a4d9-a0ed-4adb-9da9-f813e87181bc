<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-row class="form-row" v-if="!!form" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">工单上报</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">隐患名称：</label>
            <span class="common-value-text">
              {{ form.hiddenName }}
            </span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">隐患类型：</label>
            <span class="common-value-text">
              {{ hiddenTypeOptions.find(el => +el.value === form.hiddenType)?.label }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">隐患地点：</label>
            <span class="common-value-text">
              {{ form.hiddenAddress }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">上报时间：</label>
            <span class="common-value-text">
              {{ form.createdTime }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">上报人：</label>
            <span class="common-value-text">
              {{ form.createdUserName }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">
              {{ form.projectName }}
            </span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">隐患图片：</label>
          </div>
          <UploadFile
            :fileUrl="form.phoneAttaches?.map(el => el.attachUrl)"
            :multiple="true"
            listType="picture-card"
            :onlyView="true"
            disabled
          />
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">部位：</label>
            <span class="common-value-text">
              {{ form.toponym }}
            </span>
          </div>
        </a-col>

        <template v-if="!!form.logs?.[0]">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">工单处置</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">处置反馈：</label>
              <span class="common-value-text">
                {{ form.logs[0]?.remark }}
              </span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">处置完成时间：</label>
              <span class="common-value-text">
                {{ form.logs[0]?.createdTime }}
              </span>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="item">
              <label class="common-label-text">处置照片：</label>
            </div>
            <UploadFile
              :fileUrl="form.logs[0].files"
              :multiple="true"
              listType="picture-card"
              :onlyView="true"
              disabled
            />
          </a-col>
        </template>

        <template v-if="!!form.logs?.[1]">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">工单办结</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">是否通过：</label>
              <span class="common-value-text">
                {{ hiddenStatusOptions.find(el => el.value === form.logs?.[1]?.status)?.label }}
              </span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">办结人：</label>
              <span class="common-value-text">
                {{ form.logs?.[1]?.createdUserName }}
              </span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">办结时间：</label>
              <span class="common-value-text">
                {{ form.logs?.[1]?.createdTime }}
              </span>
            </div>
          </a-col>
          <a-col v-if="form.logs?.[1]?.status === 4" :lg="24" :md="24" :sm="24">
            <div class="item">
              <label class="common-label-text">不通过原因：</label>
              <span class="common-value-text">
                {{ form.logs?.[1]?.remark }}
              </span>
            </div>
          </a-col>
        </template>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getHidden } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'FormDetails',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'userOptions', 'hiddenTypeOptions', 'hiddenStatusOptions'],
    data() {
      return {
        modalLoading: false,
        formTitle: '',
        open: false,

        form: null,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      downLoad(url) {
        window.open(url)
      },

      details(row) {
        this.open = true
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '查看'
          getHidden({ hiddenId: row.hiddenId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              this.modalLoading = false
            }
          })
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
