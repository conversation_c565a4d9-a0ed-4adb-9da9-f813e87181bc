import { MapboxOverlay } from '@deck.gl/mapbox'
import { GeoJsonLayer } from '@deck.gl/layers'
import { hexToRgb } from '@/utils/toDeckglRgb.js'
import * as turf from '@turf/turf'
import { getValueByKey } from '@/api/common'
import axios from 'axios'
import { mapBoundGeo } from '@/utils/mapBounds.js'

export default function initMap(mapIns) {
  // 天地图
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-base-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${process.env.VUE_APP_TIANDI_BASE}/img_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[0].id,
  )
  // 天地图标注
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-label-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${process.env.VUE_APP_TIANDI_BASE}/cia_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[1].id,
  )

  // 灌区边界
  // getValueByKey('gis.irr.boundary').then(res => {
  //   axios(res.data).then(resp => {
  //     mapIns.addLayer({
  //       id: 'gq-area-line',
  //       type: 'line',
  //       source: {
  //         type: 'geojson',
  //         data: resp.data, //区划的面数据
  //       },
  //       paint: {
  //         'line-color': '#03FFCD',
  //         'line-width': 2,
  //       },
  //     })
  //   })
  // })

  axios(
    `${process.env.VUE_APP_GEOSERVER_BASE}/district/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=district:zhejiang&filter=<PropertyIsEqualTo><PropertyName>code</PropertyName><Literal>330481</Literal></PropertyIsEqualTo>`,
  ).then(async res => {
    mapBoundGeo(res.data, mapIns, { top: 100, bottom: 100, left: 100, right: 100 })

    //蒙版边界
    mapIns.addLayer({
      id: 'mb-line',
      type: 'line',
      source: {
        type: 'geojson',
        data: res.data, //区划的面数据
      },
      paint: {
        'line-color': 'rgba(9, 236, 255, 1)',
        'line-width': 2,
      },
      // layout: {
      //   visibility: 'visible',
      // },
    })

    // 蒙版图层   //通过边界数据反选 达到挖洞效果
    // mapIns.addLayer({
    //   id: 'mb-tag',
    //   type: 'fill',
    //   source: {
    //     type: 'geojson',
    //     data: {
    //       type: 'Feature',
    //       geometry: {
    //         type: 'Polygon',
    //         coordinates: [
    //           [
    //             [-180, 90],
    //             [180, 90],
    //             [180, -90],
    //             [-180, -90],
    //           ],
    //           res.data.features[0].geometry.coordinates[0][0],
    //         ],
    //       },
    //     },
    //   },
    //   paint: { 'fill-color': 'rgba(0,0,0,0.4)' },
    //   layout: { visibility: 'visible' },
    // })
  })

  // 渠(沟)道
  // axios(
  //   `${process.env.VUE_APP_GEOSERVER_BASE}/sthgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=sthgq:HP005&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>2</Literal></PropertyIsEqualTo><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>3</Literal></PropertyIsEqualTo><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>4</Literal></PropertyIsEqualTo></Or>`,
  // ).then(res => {
  //   mapIns.addLayer({
  //     id: 'gouqudao-line',
  //     type: 'line',
  //     source: {
  //       type: 'geojson',
  //       data: res.data,
  //     },
  //     paint: {
  //       'line-color': '#2BBCFF',
  //       'line-width': 2,
  //     },
  //   })
  // })

  // 灌区渐变图
  // mapIns.addLayer({
  //   id: 'wms-test-layer',
  //   type: 'raster',
  //   source: {
  //     type: 'raster',
  //     tiles: [
  //       `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq_drought/wms?service=wms&version=1.1.0&request=GetMap&layers=thjgq_drought:20240920&bbox={bbox-epsg-3857}&width=768&height=720&srs=EPSG:3857&styles=&format=image/png&TRANSPARENT=TRUE&exceptions=application/vnd.ogc.se_inimage`,
  //     ],
  //     tileSize: 256,
  //   },
  // })

  // Promise.all([
  //   axios(
  //     // 灌区的边界
  //     `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP004&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo></Or>`,
  //   ),
  //   axios(
  //     // 水库边界
  //     `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=thjgq:HP001&maxFeatures=50&outputFormat=application/json&filter=<PropertyIsEqualTo><PropertyName>object_name</PropertyName><Literal>桃花江水库</Literal></PropertyIsEqualTo>`,
  //   ),
  //   axios(
  //     // 沟渠
  //     `${process.env.VUE_APP_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP005&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>2</Literal></PropertyIsEqualTo></Or>`,
  //   ),
  // ]).then(res => {
  //   mapBoundGeo(res[0].data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })

  //   const all = {
  //     type: 'FeatureCollection',
  //     features: [
  //       ...res[0].data.features.map(el => ({ ...el, properties: { ...el.properties, name: 'guanqu' } })),
  //       ...res[1].data.features.map(el => ({ ...el, properties: { ...el.properties, name: 'shuiku' } })),
  //       ...res[2].data.features.map(el => ({ ...el, properties: { ...el.properties, name: 'gouqu' } })),
  //     ],
  //   }
  //   // 给每个区域加质点
  //   const geojson = {
  //     ...all,
  //     features: [
  //       ...all.features.map(el => {
  //         return turf.centerOfMass({ features: [el], type: 'FeatureCollection' }, { properties: el.properties })
  //       }),
  //       ...all.features,
  //     ],
  //   }

  //   mapIns.addSource('allGeojson', {
  //     type: 'geojson',
  //     data: geojson,
  //   })

  //   mapIns.addLayer({
  //     id: 'guanqu-line',
  //     type: 'line',
  //     source: 'allGeojson',
  //     paint: {
  //       'line-color': '#0AC8FC',
  //       'line-width': 2,
  //     },
  //     filter: ['all', ['==', '$type', 'LineString'], ['==', 'name', 'guanqu']],
  //   })

  //   mapIns.addLayer({
  //     id: 'shuiku-area-fill',
  //     type: 'fill',
  //     source: 'allGeojson',
  //     paint: {
  //       'fill-color': '#1CE951',
  //       'fill-outline-color': '#F6CC58',
  //     },
  //     filter: ['all', ['==', '$type', 'Polygon'], ['==', 'name', 'shuiku']],
  //   })

  //   mapIns.addLayer({
  //     id: 'gouqu-line',
  //     type: 'line',
  //     source: 'allGeojson',
  //     paint: {
  //       'line-color': '#29F934',
  //       'line-width': 2,
  //     },
  //     filter: ['all', ['==', '$type', 'LineString'], ['==', 'name', 'gouqu']],
  //   })

  //   mapIns.addLayer({
  //     id: 'all-point',
  //     type: 'symbol',
  //     source: 'allGeojson',
  //     layout: {
  //       'text-size': 12,
  //       'text-field': ['get', 'object_name'],
  //       // 'text-offset': [0, 1.25],
  //       'text-anchor': 'center',
  //     },
  //     filter: ['==', '$type', 'Point'],
  //   })
  // })
}
// export default function initMap(mapIns, geojson, id) {
//   const json = JSON.parse(JSON.stringify(geojson))
//   // 给每个区域加质点
//   const geojsonData = {
//     ...json,
//     features: [
//       ...json.features.map(el => {
//         return turf.centroid({ features: [el], type: 'FeatureCollection' }, { properties: el.properties })
//       }),
//       ...json.features,
//     ],
//   }

//   const deckOverlay = new MapboxOverlay({
//     id: 'deck-geojson-layer-overlay' + id,
//     layers: [
//       new GeoJsonLayer({
//         id: 'geojson-layer' + id,
//         data: geojsonData,
//         // filled: true,
//         pickable: false,
//         stroked: true,
//         getLineWidth: 6,
//         lineWidthMaxPixels: 10, //线条的最大宽度（以像素为单位）
//         lineWidthMinPixels: 2, //线条的最小宽度（以像素为单位）
//         getLineColor: [245, 221, 126, 255],

//         pointType: 'text',
//         getText: d => {
//           d.properties?.object_name
//         },
//         getTextColor: [29, 33, 41, 255],
//         textCharacterSet: 'auto',
//         getTextSize: 12,
//         textOutlineColor: [255, 255, 255, 255],
//         textOutlineWidth: 7,
//         textFontSettings: { sdf: true, smoothing: 0.3 },
//       }),
//     ],
//   })

//   mapIns.addControl(deckOverlay)
// }
