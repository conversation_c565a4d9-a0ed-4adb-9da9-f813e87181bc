<template>
  <div style="flex: 1; display: flex; flex-direction: column">
    <div style="font-size: 16px; font-weight: bold; margin-top: 10px">调度过程模拟</div>
    <a-tabs v-model="active" size="small" :animated="false" @change="onTabChange">
      <a-tab-pane v-for="(el, idx) in allData" :key="el.projectId" :tab="el.projectName"></a-tab-pane>
    </a-tabs>

    <template v-for="(el, idx) in allData">
      <div v-if="active === el.projectId">
        <div>
          初始闸后水位：
          <a-input-number size="small" :step="0.01" :min="0" v-model="allData[idx].wlv" />
          m
        </div>
        <div v-if="baseInfo.dispathType === 2" style="margin: 8px 0; font-weight: bold">
          开度调度，请输入闸门开度（单位：%）或开启水泵数量
        </div>
        <div v-if="baseInfo.dispathType === 1" style="margin: 8px 0; font-weight: bold">
          流量调度，请输入过闸流量（单位：m³/s）或开启水泵数量
        </div>
      </div>
    </template>

    <div style="flex: 1; display: flex; flex-direction: column">
      <VxeTable
        :key="tableKey"
        ref="vxeTableRef"
        size="small"
        :isShowTableHeader="false"
        :isDrop="false"
        :columns="tableColumns"
        :tableData="tableData"
        :loading="loading"
        :tablePage="false"
        :scrollY="{ enabled: true, gt: 0 }"
        :scrollX="{ enabled: true, gt: 0 }"
      ></VxeTable>
    </div>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'
  import { getChSimFlow } from '../../services'

  export default {
    name: 'CaseCompile',
    props: ['baseInfo', 'projectFlows'],
    components: { VxeTable },
    data() {
      return {
        tableKey: 1,
        loading: false,
        allData: [],
        active: undefined,
        tableData: [],
        tableColumns: [],
      }
    },
    computed: {},
    watch: {},
    created() {
      const dealData = data => {
        this.active = data[0].projectId

        this.allData = data.map((el, index) => {
          const resArr = el.res.map((ele, ind) => {
            let obj = {}
            ele.records.forEach(ele => (obj[ele.projectId] = ele))
            return { ...ele, recordsObj: obj, wlvObj: el.wlvs[ind], isRequired: !!el.isRequired }
          })

          return {
            ...el,
            res: resArr,
            columns: [
              { title: '调度时间', field: 'tm', fixed: 'left', minWidth: 140 },
              {
                title: '钱塘江水位',
                field: 'wlvObj.waterLevel',
                minWidth: 160,
                slots: {
                  default: ({ row, rowIndex }) => {
                    const obj = this.allData[index].res[rowIndex]

                    const fillDown = (
                      <a
                        style='margin-left: 5px; font-size: 12px'
                        onClick={() =>
                          this.allData[index].res.forEach((item, j) => {
                            if (j > rowIndex) {
                              item.wlvObj = {
                                ...this.allData[index].res[rowIndex].wlvObj,
                                waterLevel: obj.isRequired
                                  ? this.allData[index].res[rowIndex].wlvObj.waterLevel || 0
                                  : this.allData[index].res[rowIndex].wlvObj.waterLevel,
                              }
                            }
                          })
                        }
                      >
                        向下填充
                      </a>
                    )

                    return (
                      <div class='cell-box'>
                        <a-input-number
                          style='width: 80px'
                          size='small'
                          step={1}
                          min={0}
                          value={this.allData[index].res[rowIndex].wlvObj.waterLevel}
                          onChange={val => {
                            this.allData[index].res[rowIndex].wlvObj = {
                              ...this.allData[index].res[rowIndex].wlvObj,
                              waterLevel: obj.isRequired ? val || 0 : val,
                            }
                          }}
                        />
                        {fillDown}
                      </div>
                    )
                  },
                },
              },
              ...el.projects.map(ele => ({
                field: `recordsObj.${ele.projectId}`,
                minWidth: 240,
                title: ele.projectName,
                slots: {
                  default: ({ row, rowIndex }) => {
                    const obj = this.allData[index].res[rowIndex].recordsObj[ele.projectId]

                    const fillDown = (
                      <a
                        style='margin-left: 5px; font-size: 12px'
                        onClick={() =>
                          this.allData[index].res.forEach((item, j) => {
                            if (j > rowIndex) {
                              item.recordsObj[ele.projectId] = {
                                ...this.allData[index].res[rowIndex].recordsObj[ele.projectId],
                              }
                            }
                          })
                        }
                      >
                        向下填充
                      </a>
                    )

                    const fileKey = this.baseInfo.dispathType === 1 ? 'flow' : 'open'
                    // 有闸有泵  当是闸是判断流量调度还是开度调度
                    if (obj.type === 0 || obj.type === 1) {
                      return (
                        <div class='cell-box'>
                          <a-radio-group
                            size='small'
                            v-model={this.allData[index].res[rowIndex].recordsObj[ele.projectId].type}
                            options={[
                              { label: '闸', value: 0 },
                              { label: '泵', value: 1 },
                            ]}
                            style='font-size: inherit'
                          />
                          {obj.type === 0 ? (
                            <a-input-number
                              style='width: 80px'
                              size='small'
                              step={fileKey === 'flow' ? 0.001 : 1}
                              min={0}
                              max={fileKey === 'flow' ? 999 : 100}
                              v-model={this.allData[index].res[rowIndex].recordsObj[ele.projectId][fileKey]}
                            />
                          ) : (
                            <a-input-number
                              style='width: 80px'
                              size='small'
                              step={1}
                              min={0}
                              v-model={this.allData[index].res[rowIndex].recordsObj[ele.projectId].pumpCount}
                            />
                          )}

                          {fillDown}
                        </div>
                      )
                    }
                    // 单闸
                    if (obj.type === 2) {
                      // 开度调度  百分比
                      const fileKey = this.baseInfo.dispathType === 1 ? 'flow' : 'open'

                      return (
                        <div class='cell-box'>
                          <a-input-number
                            style='width: 80px'
                            size='small'
                            step={fileKey === 'flow' ? 0.001 : 1}
                            min={0}
                            max={fileKey === 'flow' ? 999 : 100}
                            v-model={this.allData[index].res[rowIndex].recordsObj[ele.projectId][fileKey]}
                          />
                          {fillDown}
                        </div>
                      )
                    }
                    // 单泵
                    if (obj.type === 4) {
                      return (
                        <div class='cell-box'>
                          <a-input-number
                            style='width: 80px'
                            size='small'
                            step={1}
                            min={0}
                            v-model={this.allData[index].res[rowIndex].recordsObj[ele.projectId].pumpCount}
                          />
                          {fillDown}
                        </div>
                      )
                    }
                    // 无闸无泵
                    if (obj.type === 3) {
                      if (this.baseInfo.dispathType === 1) {
                        return this.allData[index].res[rowIndex].recordsObj[ele.projectId].flow ?? '-'
                      }
                      if (this.baseInfo.dispathType === 2) {
                        return this.allData[index].res[rowIndex].recordsObj[ele.projectId].open ?? '-'
                      }
                    }
                  },
                },
              })),
            ],
          }
        })

        this.onTabChange(this.active)
      }

      if (this.projectFlows.length > 0) {
        dealData(this.projectFlows)
      } else {
        getChSimFlow(this.baseInfo).then(resp => {
          dealData(resp.data)
        })
      }
    },
    methods: {
      onTabChange(activeKey) {
        this.tableData = this.allData.find(el => el.projectId === activeKey).res
        this.tableColumns = this.allData.find(el => el.projectId === activeKey).columns
        this.tableKey += 1
      },
      save() {
        const infos = this.allData.map(el => {
          const wlvs = []
          const resArr = el.res.map(ele => {
            let arr = []
            Object.keys(ele.recordsObj).forEach(key => {
              arr.push({ ...ele.recordsObj[key] })
            })

            wlvs.push(ele.wlvObj)
            return { ...ele, records: arr, recordsObj: undefined }
          })

          return { ...el, columns: undefined, res: resArr, wlvs }
        })

        this.$emit('saveData', infos)
      },
    },
  }
</script>

<style lang="less" scoped>
  .cell-box {
    width: 100%;
    display: flex;
    align-items: center;
    a {
      display: none;
    }
    &:hover {
      a {
        display: inline;
      }
    }
  }

  ::v-deep .vxe-table--render-default.size--small .vxe-header--column.col--ellipsis > .vxe-cell {
    max-height: 60px;
  }

  ::v-deep .ant-radio-group {
    .ant-radio-wrapper {
      font-size: inherit;
      span {
        &:last-child {
          padding: 0 0 0 5px;
        }
      }
    }
  }
</style>
