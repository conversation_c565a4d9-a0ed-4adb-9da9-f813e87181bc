<template>
  <div style="height: 100%; display: flex; flex-direction: column">
    <div style="flex: 1">
      <ResultPanel v-if="!!dataSource" :resultData="dataSource" style="margin-top: 10px" />
    </div>
  </div>
</template>

<script>
  import { getOptions } from '@/api/common'
  import { getInWater } from '../../../services'
  import moment from 'moment'
  import ResultPanel from './components/ResultPanel.vue'

  export default {
    name: 'WaterForecast',
    components: {
      ResultPanel,
    },
    data() {
      return {
        moment,
        dataSource: null,
      }
    },
    computed: {},
    created() {
      getInWater({ inWaterId: this.$attrs.chSimId }).then(res => {
        this.dataSource = res.data
        this.$emit('getWaterData', this.dataSource)
      })
    },
    methods: {},
  }
</script>

<style lang="less" scoped></style>
