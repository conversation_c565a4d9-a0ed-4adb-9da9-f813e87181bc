<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="620"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row>
          <a-form-model-item label="预警分组名称" prop="groupName">
            <a-input allowClear v-model="form.groupName" :maxLength="15" placeholder="请输入" />
          </a-form-model-item>
          <a-form-model-item label="对象分类" prop="objectType">
            <a-select :disabled="!!form.groupId" allowClear v-model="form.objectType" placeholder="请选择">
              <a-select-option v-for="(item, index) in warningObjectTypeList" :key="index" :value="item.key">
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="分类" prop="categoryId">
            <a-tree-select
              v-model="form.categoryId"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="treeData"
              show-search
              treeNodeFilterProp="title"
              allowClear
              placeholder="请选择"
              :replaceFields="{
                children: 'children',
                title: 'categoryName',
                key: 'categoryId',
                value: 'categoryId',
              }"
              tree-default-expand-all
            ></a-tree-select>
          </a-form-model-item>
          <a-form-model-item label="预警等级" prop="warnLevel">
            <a-select allowClear v-model="form.warnLevel" placeholder="请选择">
              <a-select-option v-for="(item, index) in warningLevelList" :key="index" :value="item.key">
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <div style="display: flex; align-items: center">
            <a-form-model-item
              prop="isIntervalUnlimited"
              label="预警间隔是否无限制"
              :labelCol="{ span: 14 }"
              :wrapperCol="{ span: 10 }"
              style="width: 47%; line-height: 30px; margin-left: 10px"
            >
              <a-radio-group v-model="form.isIntervalUnlimited" @change="changeIntervalUnlimited">
                <a-radio value="1">是</a-radio>
                <a-radio value="0">否</a-radio>
              </a-radio-group>
            </a-form-model-item>
            <a-form-model-item
              v-if="form.isIntervalUnlimited == '0'"
              :label="null"
              :labelCol="{ span: 0 }"
              :wrapperCol="{ span: 24 }"
              prop="warnInterval"
              style="line-height: 30px"
            >
              <a-input-number
                allow-clear
                class="min-input"
                :min="1"
                :precision="0"
                placeholder="请输入"
                v-model="form.warnInterval"
              />
              <span v-if="form.isIntervalUnlimited == '0'">分钟</span>
            </a-form-model-item>
          </div>

          <div style="display: flex; align-items: center">
            <a-form-model-item
              prop="isNeverExpire"
              label="预警消息是否永不失效"
              :labelCol="{ span: 14 }"
              :wrapperCol="{ span: 10 }"
              style="width: 47%; line-height: 30px; margin-left: 10px"
            >
              <a-radio-group v-model="form.isNeverExpire" @change="changeNeverExpire">
                <a-radio value="1">是</a-radio>
                <a-radio value="0">否</a-radio>
              </a-radio-group>
            </a-form-model-item>

            <a-form-model-item
              v-if="form.isNeverExpire == '0'"
              :label="null"
              :labelCol="{ span: 0 }"
              :wrapperCol="{ span: 24 }"
              prop="warnExpire"
              style="line-height: 30px"
            >
              <a-input-number
                allow-clear
                class="min-input"
                :min="1"
                :precision="0"
                placeholder="请输入"
                v-model="form.warnExpire"
              />
              <span v-if="form.isNeverExpire == '0'">分钟</span>
            </a-form-model-item>
          </div>
          <a-form-model-item label="消息模板" prop="msgTplId">
            <a-select allowClear v-model="form.msgTplId" placeholder="请选择">
              <a-select-option v-for="(item, index) in msgTemplateList" :key="index" :value="item.msgTplId">
                {{ item.msgTplName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="备注">
            <a-textarea v-model="form.remark" placeholder="请输入" allow-clear />
          </a-form-model-item>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
      <!-- <a-button type="primary" @click="submitForm">保存</a-button> -->
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions, getBaseCategory } from '@/api/common'
  import { getWarnMsgTemList, addWarnGroup, getWarnGroupDetails, editWarnGroup } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormMaintenance',
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        labelCol: { span: 7 },
        wrapperCol: { span: 16 },
        formTitle: '',
        treeData: [],
        reportItemId: null,
        form: {
          categoryId: undefined,
          groupName: '',
          isIntervalUnlimited: '1',
          isNeverExpire: '1',
          msgTplId: undefined,
          objectType: undefined,
          remark: '',
          warnExpire: undefined,
          warnInterval: undefined,
          warnLevel: '',
        },
        open: false,
        warningObjectTypeList: [],
        warningLevelList: [],
        msgTemplateList: [],
        autoExpandParent: true,
        expandedKeys: [],
        rules: {
          groupName: [{ required: true, message: '预警分组名称不能为空', trigger: 'blur' }],
          objectType: [{ required: true, message: '对象分类不能为空', trigger: 'change' }],
          categoryId: [{ required: true, message: '分类不能为空', trigger: 'change' }],
          warnLevel: [{ required: true, message: '预警等级不能为空', trigger: 'change' }],
          warnExpire: [{ required: true, message: '预警消息失效时间不能为空', trigger: 'blur' }],
          warnInterval: [{ required: true, message: '预警间隔不能为空', trigger: 'blur' }],
          isIntervalUnlimited: [{ required: true, message: '请选择', trigger: 'blur' }],
          isNeverExpire: [{ required: true, message: '请选择', trigger: 'blur' }],
          msgTplId: [{ required: true, message: '消息模板不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      getBaseCategory({ categoryType: 'warningCode' }).then(res => {
        this.treeData = res?.data || []
      })
      getOptions('warningObjectType').then(res => {
        this.warningObjectTypeList = res?.data || []
      })
      //warningLevel
      getOptions('warningLevel').then(res => {
        this.warningLevelList = res?.data || []
      })
    },
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      handleRule(type, id) {
        this.open = true
        //消息模板
        getWarnMsgTemList({ pageNum: 1, pageSize: 2147483647 }).then(res => {
          this.msgTemplateList = res?.data?.data
        })
        if (type == 'add') {
          this.formTitle = '新增'
          this.form.categoryId = id
        } else if (type == 'edit') {
          this.formTitle = '修改'
          this.modalLoading = true
          getWarnGroupDetails({ groupId: id }).then(res => {
            this.modalLoading = false
            this.form = {
              categoryId: res?.data.categoryId,
              groupName: res?.data.groupName,
              isIntervalUnlimited: res?.data.isIntervalUnlimited.toString(),
              isNeverExpire: res?.data.isNeverExpire.toString(),
              msgTplId: res?.data.msgTplId,
              objectType: res.data.objectType.toString(),
              remark: res?.data.remark,
              warnExpire: res?.data.warnExpire,
              warnInterval: res?.data.warnInterval,
              warnLevel: res?.data.warnLevel,
              groupId: res?.data.groupId,
            }
          })
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.formTitle == '新增') {
              addWarnGroup(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('close')
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else if (this.formTitle == '修改') {
              editWarnGroup(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('close')
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
      changeIntervalUnlimited() {
        this.form.warnInterval = undefined
      },
      changeNeverExpire() {
        this.form.warnExpire = undefined
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
  }
  .min-input {
    width: 150px !important;
    margin-right: 8px;
  }
</style>
