<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      modalWidth="680"
      :maskClosable="false"
      @cancel="cancel"
      modalHeight="880"
    >
      <div slot="content">
        <a-form-model :model="form">
          <div class="preview">
            <div class="content-preview">
              <div class="title" :title="form.newsName">{{ form.newsName }}</div>
              <div class="time" v-if="form.typeMenu == 1">发表时间({{ startTime }})</div>
              <div class="time" v-if="form.typeMenu == 2">活动时间({{ startTime }} ~ {{ endTime }})</div>
              <div class="content" v-html="form.context"></div>
            </div>
          </div>
        </a-form-model>
      </div>

      <template slot="footer">
        <a-button v-if="form.status == 0" @click="edit(3)">编辑</a-button>
        <a-button v-if="form.status == 0" type="primary" @click="saveForm(1)" :loading="loading">保存</a-button>
        <a-button v-if="form.status == 0" type="primary" @click="submitForm(2)" :loading="loading">发布</a-button>

        <a-button v-if="form.status == 1" @click="edit(2)">编辑</a-button>
        <a-button v-if="form.status == 1 && form.isSave == 2" type="primary" @click="saveForm(2)" :loading="loading">
          保存
        </a-button>
        <a-button v-if="form.status == 1" type="primary" @click="submitForm(3)" :loading="loading">发布</a-button>

        <a-button v-if="form.status == 2" @click="setTop">
          {{ isTrue(this.form.isTop) ? '取消置顶' : '首页置顶' }}
        </a-button>
      </template>
    </ant-modal>
    <FormModal
      v-if="showForm"
      ref="formRef"
      @ok="onOperationComplete"
      :typeOptions="typeOptions"
      :stateOptions="stateOptions"
      :deptOptions="deptOptions"
      @close="showForm = false"
    />
  </div>
</template>
<script lang="jsx">
  import { addNews, updateNews, getNews, setTop, addIssue, issue } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'
  import isTrue from '@/utils/util.js'

  export default {
    name: 'FormModalPreview',
    components: { AntModal, FormModal: () => import('./FormModal.vue') },
    props: ['typeOptions', 'stateOptions', 'deptOptions'],
    data() {
      return {
        showForm: false,
        loading: false,
        modalLoading: false,
        currentLength: 0,
        formTitle: '',
        typeTitle: '',
        startTime: '',
        endTime: '',
        form: {
          isSave: undefined,
          status: undefined,

          context: '',
          coverImage: '',
          deptIds: null,
          endDate: null,
          newsName: '',
          startDate: null,
          state: null,
          summary: '',
          type: undefined,
          typeMenu: 1,
        },
        open: false,
      }
    },
    created() {
      this.form.typeMenu = this.$route.meta.query?.type
      this.typeTitle = this.$route.meta.query?.type == 1 ? '新闻' : '活动'
    },
    mounted() {},
    methods: {
      isTrue(val) {
        return val === 1 || val === '1'
      },
      edit(type) {
        const _that = this

        this.open = false
        _that.showForm = true
        if (type == 2) {
          getNews({ newsId: this.form.newsId }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
              }

              this.startTime = moment(this.form.startDate).format('YYYY-MM-DD HH:mm')
              this.endTime = this.form.endDate ? moment(this.form.endDate).format('YYYY-MM-DD HH:mm') : '--'
              this.form.type = String(this.form.type)
            }
          })
        }

        this.form.editType = type
        setTimeout(() => {
          _that.$nextTick(() => _that.$refs.formRef.handle(_that.form, type))
        }, 200)
      },

      // 置顶
      setTop(val) {
        let isTopNew = this.form.isTop == 0 ? 1 : this.form.isTop == 1 ? 0 : ''
        const newsNames = this.form.newsName
        this.confirmIns = this.$confirm({
          title: `确认${val ? '将' + this.typeTitle + '设为置顶显示' : '取消' + this.typeTitle + '的置顶状态'}?`,

          content: isTopNew
            ? '选择首页置顶后，将会取消当前类别中已置顶' + this.typeTitle + '的置顶状态,请谨慎操作。'
            : '取消首页置顶后，将会取消该' + this.typeTitle + '的置顶状态,请谨慎操作。',
          onOk: () => {
            setTop({ newsId: this.form.newsId, isTop: isTopNew }).then(res => {
              if (res.code == 200) {
                this.$message.success('设置成功', 3)
                this.open = false
                this.$emit('ok')
              }
            })
          },
          onCancel() {},
        })
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleRecordNews(value) {},
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      filterName(str, length) {
        if (str.length > length) {
          str = str.substring(0, length) + '...'
        }
        return str
      },
      /** 新闻预览操作 */
      handle(row, type) {
        this.open = true

        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = this.form.typeMenu == 1 ? '新闻预览' : '活动预览'
          if (row.newsId && (type == 1 || type == 3)) {
            getNews({ newsId: row.newsId }).then(res => {
              //附件显示
              this.form = {
                ...res.data,
              }
              this.form.type = String(this.form.type)
              this.startTime =
                this.form.typeMenu == 1
                  ? moment(row.startDate).format('YYYY-MM-DD')
                  : moment(row.startDate).format('YYYY-MM-DD HH:mm')
              this.endTime = this.form.typeMenu == 2 ? moment(row.endDate).format('YYYY-MM-DD HH:mm') : '--'

              this.modalLoading = false
            })
          } else {
            this.modalLoading = false
            this.form = row
            this.startTime =
              this.form.typeMenu == 1
                ? moment(row.startDate).format('YYYY-MM-DD')
                : moment(row.startDate).format('YYYY-MM-DD HH:mm')
            this.endTime = this.form.typeMenu == 2 ? moment(row.endDate).format('YYYY-MM-DD HH:mm') : '--'
          }
        }
      },
      // 操作完成后
      onOperationComplete() {
        this.$emit('ok')
      },
      /** 发布按钮 */
      submitForm(type) {
        let tmp = type ? '发布' : ''
        this.confirmIns = this.$confirm({
          title: `确认${type ? tmp : '取消'}?`,
          content: '发布后，可以对该' + this.typeTitle + '进行置顶状态操作。',
          onOk: () => {
            if (this.form.newsId == null) {
              this.form.startDate = moment(this.form.startDate).format('YYYY-MM-DD HH:mm:ss')
              this.form.status = this.form.status == 0 ? undefined : 2
              addIssue(this.form).then(publishRes => {
                if (publishRes.code == 200) {
                  this.$message.success('发布成功', 3)
                  this.open = false
                  this.form.isSave = undefined
                  this.$emit('ok')
                }
              })
            } else {
              if (type == 2) {
                updateNews(this.form)
                  .then(res => {
                    if (res.code == 200) {
                      this.open = false
                      this.form.isSave = undefined
                      this.$emit('ok')
                    }
                  })
                  .catch(() => (this.loading = false))
              }
              issue({ newsId: this.form.newsId }).then(publishRes => {
                if (publishRes.code == 200) {
                  this.$message.success('发布成功', 3)
                  this.open = false
                  this.form.isSave = undefined
                  this.$emit('ok')
                }
              })
            }
          },
          onCancel() {},
        })
      },
      /** 保存按钮 */
      saveForm(type) {
        let tmp = type ? '保存' : ''
        this.confirmIns = this.$confirm({
          title: `确认${type ? tmp : '取消'}?`,
          content: '保存后，可对该' + this.typeTitle + '进行编辑与发布。',
          onOk: () => {
            if (this.form.newsId == null) {
              this.form.startDate = moment(this.form.startDate).format('YYYY-MM-DD HH:mm:ss')
              this.form.status = this.form.status == 0 ? undefined : 2
              addNews(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.form.isSave = undefined
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              this.form.startDate = moment(this.form.startDate).format('YYYY-MM-DD HH:mm:ss')
              updateNews(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.form.isSave = undefined
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          },
          onCancel() {},
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .preview {
    width: 650px;
    height: 100%;
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    .web-preview {
      width: 400px;
      height: 100%;
      display: flex;
      flex-direction: column;

      border-radius: 4px;
      margin-right: 10px;
      .img {
        width: 400px;
        height: 224px;
      }
      .title {
        padding: 5px 15px;
        font-size: 16px;
        font-family: PingFangBold;
        font-weight: 500;
        color: #333;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .time {
        padding: 5px 15px;
        font-size: 16px;
      }
    }
    .content-preview {
      // width: 1240px;
      width: 400px;
      height: 100%;
      display: flex;
      flex-direction: column;
      background: url('~@/assets/images/phone-frame.png') no-repeat;
      background-size: 100% 100%;
      padding: 20px 10px 38px 10px;
      .title {
        width: 76%;
        font-size: 14px;
        color: #000;
        font-family: PingFangBold;
        font-weight: 600;
        line-height: 32px;
        text-align: center;
        margin-top: 40px;
        margin-bottom: 10px;
        margin-left: 46px;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      .time {
        width: 100%;
        text-align: center;
        font-size: 14px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        color: #333 !important;
        // line-height: 20px;
      }
      .content {
        margin-top: 10px;
        margin-left: 20px;

        width: 90%;
        height: 560px;
        overflow-y: auto;
        color: #333 !important;
      }
      ::v-deep .content img {
        max-width: 98% !important; /* 使图片宽度不超过父容器 */
        height: auto; /* 保持图片原始比例 */
        display: block; /* 避免图片下方出现空白间隙 */
      }
    }
  }
</style>
