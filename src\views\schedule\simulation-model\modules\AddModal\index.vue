<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="880"
    :maskClosable="false"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <a-steps :current="step" size="small" style="padding: 4px 220px">
        <a-step title="基本信息" />
        <a-step title="降雨过程" />
        <a-step title="调度过程模拟" />
        <a-step title="模拟结果" />
      </a-steps>

      <keep-alive>
        <Basic
          v-if="step == 0"
          ref="basicRef"
          v-bind="$attrs"
          @saveData="saveData"
          :inWaterEchoData="inWaterEchoData"
        />

        <RainfallList v-if="step == 1" ref="rainfallListRef" :baseInfo="baseInfo" @saveData="saveData" />

        <CaseCompile
          v-if="step == 2"
          ref="caseCompileRef"
          :baseInfo="baseInfo"
          :projectFlows="projectFlows"
          @saveData="saveData"
        />
      </keep-alive>
      <Result
        v-if="step == 3"
        ref="resultRef"
        :baseInfo="baseInfo"
        :rainfall="rainfall"
        :projectFlows="projectFlows"
        :isDisabledBtn.sync="isDisabledBtn"
        @saveData="saveData"
      />
    </div>
    <template slot="footer">
      <a-button @click="cancel" v-if="step === 0">取消</a-button>
      <a-button @click="preStep" v-if="step === 3" :disabled="isDisabledBtn">上一步</a-button>
      <a-button type="primary" @click.stop="onSubmit" :loading="loading" :disabled="isDisabledBtn">
        {{ step === 3 ? '保存' : '下一步' }}
      </a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getInParameter, saveChSim } from '../../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import Basic from './Basic.vue'
  import RainfallList from './RainfallList.vue'
  import CaseCompile from './CaseCompile.vue'
  import Result from './Result.vue'

  export default {
    name: 'AddModal',
    components: { AntModal, Basic, RainfallList, CaseCompile, Result },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '新增',
        step: 0,
        baseInfo: {},
        rainfall: null,
        projectFlows: [],

        isDisabledBtn: false,
        inWaterEchoData: null,
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      preStep() {
        this.step--
      },
      handleShow(params, type) {
        this.open = true

        if (type === 'copy') {
          getInParameter({ chSimId: params.chSimId }).then(res => {
            this.inWaterEchoData = res.data
            this.projectFlows = res.data.projectFlows
          })
        }
      },

      saveData(params) {
        if (!!params) {
          switch (this.step) {
            case 0:
              if (!!this.inWaterEchoData) {
                if (
                  this.inWaterEchoData.simulateType !== params.simulateType ||
                  this.inWaterEchoData.resvrDispId !== params.resvrDispId ||
                  this.inWaterEchoData.startTime !== params.startTime ||
                  this.inWaterEchoData.endTime !== params.endTime ||
                  this.inWaterEchoData.dispathType !== params.dispathType ||
                  this.inWaterEchoData.fcstRange !== params.fcstRange
                ) {
                  this.baseInfo = params
                  this.projectFlows = []
                }
              } else {
                this.baseInfo = params
              }
              this.$nextTick(() => (this.step += 1))
              return
            case 1:
              this.rainfall = params
              this.$nextTick(() => (this.step += 1))
              return
            case 2:
              this.projectFlows = params
              this.$nextTick(() => (this.step += 1))
              return
            case 3:
              saveChSim({ chSimId: params }).then(res => {
                this.$message.success('保存成功', 3)
                this.$emit('close')
                this.$emit('ok')
              })
              return
            default:
              return
          }
        }
      },
      onSubmit() {
        switch (this.step) {
          case 0:
            this.$refs.basicRef.save()
            return
          case 1:
            this.$refs.rainfallListRef.save()
            return
          case 2:
            this.$refs.caseCompileRef.save()
            return
          case 3:
            this.$refs.resultRef.save()
            return
          default:
            return
        }
        return
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
</style>
