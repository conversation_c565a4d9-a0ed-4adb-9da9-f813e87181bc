<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <TreeGeneral
        style="width: 220px"
        ref="treeGeneralRef"
        :treeOptions="treeOptions"
        @onTreeMounted="onTreeMounted"
        @select="node => clickTreeNode(node, 'category')"
      />
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="设备编码">
          <a-input
            v-model="queryParam.deviceCode"
            placeholder="请输入设备编码"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="设备名称">
          <a-input
            v-model="queryParam.deviceName"
            placeholder="请输入设备名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="设备使用状态">
          <a-select v-model="queryParam.deviceStatus" allowClear placeholder="请选择">
            <a-select-option v-for="(d, index) in equipmentStatusOptions" :key="index" :value="d.key">
              {{ d.value }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          ></VxeTable>
        </template>
      </VxeTableForm>
    </div>

    <DetailModal ref="detailModalRef" v-if="showDetailModal" @close="showDetailModal = false" />
  </div>
</template>

<script lang="jsx">
  import { getDevicePage } from './services'
  import {
    getDistrictTree,
    getProjectTree,
    getProjectCategoryTree,
    getDeviceCategoryTree,
    getOptions,
  } from '@/api/common'

  import DetailModal from './modules/DetailModal'
  import TreeGeneral from '@/components/TreeGeneral'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'EquipmentRecord',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      DetailModal,
    },
    data() {
      return {
        treeTabKey: '1',
        treeOptions: {
          getDataApi: getProjectCategoryTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'key',
          },
        },
        equipmentStatusOptions: [],
        deviceCategoryOptions: [],
        projectOptions: [],

        showDetailModal: false,
        list: [],
        tableTitle: '',
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          treeNodeId: undefined,
          treeNodeType: undefined,
          deviceCode: undefined,
          deviceName: undefined,
          districtCode: undefined,
          deviceStatus: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        districtTypes: [],
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '设备编码',
            field: 'deviceCode',
            minWidth: 140,
          },
          {
            title: '设备名称',
            field: 'deviceName',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '设备类别',
            field: 'deviceCategoryName',
            minWidth: 140,
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      getOptions('deviceStatus').then(res => {
        this.equipmentStatusOptions = res.data
      })

      // 设备分类
      getDeviceCategoryTree().then(res => {
        this.deviceCategoryOptions = res.data
      })
      // getDistrictTree().then(res => {
      //   this.districtTypes = getFlatTreeMap(res.data, 'districtCode')
      // })
    },
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        getDevicePage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          deviceCode: undefined,
          deviceName: undefined,
          deviceStatus: undefined,

          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange(page) {
        const { currentPage, pageSize, event } = page

        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 树加载完成后
      onTreeMounted(data) {
        if (this.treeTabKey === '1') {
          this.queryParam.treeNodeId = data[0].key.substr(1, data[0].key.length)
          this.queryParam.treeNodeType = data[0].type
          this.queryParam.districtCode = ''
          this.tableTitle = data[0].name

          this.treeOptions.dataSource = data
        }
        if (this.treeTabKey === '2') {
          this.queryParam.treeNodeId = ''
          this.queryParam.treeNodeType = ''
          this.queryParam.districtCode = data[0].districtCode
          this.tableTitle = data[0].districtName
        }

        // 获取工程树
        getProjectTree({ objectCategoryId: this.queryParam.treeNodeId }).then(res => {
          this.projectOptions = res.data
        })
        this.getList()
      },
      clickTreeNode(node, type) {
        if (type === 'category') {
          const key = node.$options.propsData.dataRef.key
          this.queryParam.treeNodeId = key.substr(1, key.length)
          this.queryParam.treeNodeType = node.$options.propsData.dataRef.type
        }
        if (type === 'district') {
          this.queryParam.districtCode = node.$options.propsData.eventKey
        }

        this.tableTitle = node.$options.propsData.dataRef.title
        this.queryParam.pageNum = 1
        this.getList()
      },

      /* 详情 */
      handleDetail(record) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleDetail(record))
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
