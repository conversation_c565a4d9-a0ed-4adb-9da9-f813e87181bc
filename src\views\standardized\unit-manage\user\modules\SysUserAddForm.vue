<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :adjust-size="true"
    modalHeight="480"
    modalWidth="640"
    @cancel="cancel"
  >
    <a-form-model ref="form" :model="form" :rules="rules" slot="content" layout="vertical">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="姓名" prop="name">
            <a-input v-model="form.name" placeholder="请输入" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="用户名" prop="username" v-if="form.userId == undefined">
            <a-input v-model="form.username" placeholder="请输入" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="所属部门" prop="deptId">
            <a-tree-select
              v-model="form.deptId"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="deptOptions"
              placeholder="请选择"
              :replaceFields="{
                children: 'children',
                title: 'deptName',
                key: 'deptId',
                value: 'deptId',
              }"
              tree-default-expand-all
            ></a-tree-select>
          </a-form-model-item>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="邮箱地址" prop="email">
            <a-input v-model="form.email" placeholder="请输入" />
          </a-form-model-item>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="手机号" prop="mobile">
            <a-input v-model="form.mobile" placeholder="请输入" />
          </a-form-model-item>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="岗位" prop="postIds">
            <a-select mode="multiple" v-model="form.postIds" placeholder="请选择" option-filter-prop="children">
              <a-select-option
                v-for="(d, index) in postOptions"
                :key="index"
                :disabled="d.isDisabled == 1"
                :value="d.postId"
              >
                {{ d.postName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <a-form-model-item label="角色" prop="roleIds">
            <a-select mode="multiple" v-model="form.roleIds" placeholder="请选择" option-filter-prop="children">
              <a-select-option
                v-for="(d, index) in roleOptions"
                :key="index"
                :disabled="d.isDisabled == 1"
                :value="d.roleId"
              >
                {{ d.roleName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-collapse :bordered="false" expandIconPosition="left">
        <template #expandIcon="props">
          <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0" />
        </template>
        <a-collapse-panel key="1" header="填写更多信息（可选)" :style="customStyle">
          <a-row :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="身份证号" prop="idNo">
                <a-input v-model="form.idNo" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="性别" prop="sex">
                <a-select placeholder="请选择性别" v-model="form.sex" style="width: 100%" allow-clear>
                  <a-select-option v-for="(d, index) in sexOptions" :key="index" :value="d.dictKey">
                    {{ d.dictValue }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="12" :span="24">
              <a-form-model-item label="学历" prop="education">
                <a-select placeholder="请选择" v-model="form.education" style="width: 100%" allow-clear>
                  <a-select-option v-for="(d, index) in educationOptions" :key="index" :value="d.dictKey">
                    {{ d.dictValue }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="12" :span="24">
              <a-form-model-item label="工作经验" prop="workExperience">
                <a-select placeholder="请选择" v-model="form.workExperience" style="width: 100%" allow-clear>
                  <a-select-option v-for="(d, index) in workExperienceOptions" :key="index" :value="d.dictKey">
                    {{ d.dictValue }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="12" :span="24">
              <a-form-model-item label="职称" prop="title">
                <a-input v-model="form.title" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="12" :span="24">
              <a-form-model-item label="生日" prop="birthday">
                <a-date-picker
                  style="width: 100%"
                  v-model="form.birthday"
                  valueFormat="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  allow-clear
                  placeholder="请输入"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注" prop="remark">
                <a-input v-model="form.remark" placeholder="请输入" type="textarea" allow-clear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-collapse-panel>
      </a-collapse>
    </a-form-model>
    <template slot="footer">
      <a-button type="primary" @click="submitForm">保存</a-button>
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import sysUserForm from './SysUserForm'
  export default {
    ...sysUserForm,
  }
</script>
<style></style>
