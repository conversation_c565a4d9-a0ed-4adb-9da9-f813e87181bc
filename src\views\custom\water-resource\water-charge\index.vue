<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="年份">
        <a-select
          v-model="queryParam.year"
          placeholder="请选择"
          :options="yearOptions"
          showSearch
          allowClear
        ></a-select>
      </a-form-item>
      <a-form-item label="乡镇">
        <a-select
          showSearch
          allowClear
          v-model="queryParam.districtCode"
          placeholder="请选择"
          :options="countyOptions"
          option-filter-prop="children"
        ></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :countyOptions="countyOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getDistrictTree } from '@/api/common'
  import { getPage, deleteWaterCharge, getYear } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import { filter } from 'lodash'

  export default {
    name: 'WaterChargeMgnt',
    components: {
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    data() {
      return {
        yearOptions: [],
        countyOptions: [],
        isChecked: false,

        showForm: false,
        showFormDetails: false,

        list: [],
        tableTitle: '水费收取管理',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          archivesName: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
          },
          {
            title: '年份',
            field: 'year',
            width: 80,
          },
          {
            title: '乡镇',
            field: 'districtName',
            minWidth: 100,
          },
          {
            title: '面积（亩）',
            field: 'area',
            minWidth: 100,
          },
          {
            title: '定价（元/亩）',
            field: 'price',
            minWidth: 100,
          },
          {
            title: '水费(万元)',
            field: 'waterRate',
            minWidth: 100,
          },
          {
            title: '创建人',
            field: 'createdUserName',
            minWidth: 100,
          },

          {
            title: '创建时间',
            field: 'createdTime',
            width: 180,
            minWidth: 180,
          },

          {
            title: '操作',
            field: 'operate',
            width: 100,
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
      getDistrictTree().then(res => {
        let countys = this.filterCounty(res?.data)
        this.countyOptions = countys.map(el => ({ label: el.districtName, value: el.districtCode }))
      })
      this.loadYears()
    },
    mounted() {},
    methods: {
      loadYears() {
        getYear().then(res => {
          this.yearOptions = res?.data?.map(el => ({ label: el, value: el }))
        })
      },
      filterCounty(nodes) {
        if (!nodes) {
          return []
        }
        let countys = []
        for (let i = 0; i < nodes.length; i++) {
          let e = nodes[i]
          if (e.icon == '4') {
            countys.push(e)
          }
          let tmp = this.filterCounty(e.children)
          if (tmp.length > 0) {
            countys = countys.concat(tmp)
          }
        }
        return countys
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
        // this.names = valObj.records.map(item => item.planName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          pageNum: 1,
          sort: [],
        }
        this.planTimes = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteWaterCharge({ ids: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
        this.loadYears()
      },
    },
  }
</script>
<style lang="less" scoped></style>
