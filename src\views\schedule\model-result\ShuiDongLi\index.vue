<template>
    <div style="position: relative; display: flex; flex-direction: column;  height: 7.7rem; width: 100%;  ">
        <!-- 地图 -->
        <div
            style="flex: 6;  height: 100%;  display: flex; flex-direction: row;justify-content: center; align-items: center;">
            <div style="flex: 6;width: 100%;height: 100%;  position: relative;">
                <Mapbox @onMapMounted="onMapMounted" mapBoxId="shuiDongLi" />
                <!-- 颜色图例 -->
                <div
                    style="position: absolute;width: 250px;  top: 10px; right: 10px; background: rgba(255, 255, 255, 0.7); padding: 5px; border-radius: 3px; z-index: 1000;">
                    <div style="font-size: 10px; font-weight: bold; margin-bottom: 5px;">闸站水位流量图例</div>
                    <div style="display: flex; flex-direction: column; width: 100%;">
                        <div style="display: flex; height: 15px; width: 100%;">
                            <div v-for="(color, index) in waterColors" :key="index" :style="{
                                flex: 1,
                                height: '100%',
                                backgroundColor: color
                            }">
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-top: 3px;">
                            <span style="font-size: 10px;">{{ min.toFixed(2) }}</span>
                            <span style="font-size: 10px;">{{ max.toFixed(2) }}</span>
                        </div>
                    </div>
                    <div style="margin-top: 3px; display: flex; align-items: center;">
                        <span style="font-size: 10px; margin-right: 5px;">显示参数：</span>
                        <a-radio-group v-model="selectedShow" style="font-size: 10px;">
                            <a-radio value="wlevel" style="--antradio-size: 10px;">水位</a-radio>
                            <a-radio value="q" checked style="--antradio-size: 10px;">流量</a-radio>
                        </a-radio-group>
                    </div>
                </div>
            </div>
            <!-- 表数据 -->
            <div style="flex: 4; width: 100%; height: 100%; margin-left: 10px;">
                <VxeTable ref="vxeDrainageTableRef" size="small" :isShowTableHeader="false"
                    :header-cell-class-name="headerCellClassName" :row-class-name="rowClassName"
                    :columns="columnsDrainage" :tableData="listDrainage" :tablePage="false">
                </VxeTable>
            </div>

        </div>
        <div style="flex: 4; height: 100%; width: 100%; padding-left: 20px; display: flex; flex-direction: column;">
            <!-- 折线图 -->
            <div style="flex: 22; position: relative;">
                <div
                    style="position: absolute; top: 22px; left: calc(50% - 120px); width: 230px; height: 30px;z-index: 1000;font-size: 13px;">
                    {{ "当前时刻： " + currentTime }}
                </div>
                <div style="position: absolute; top: 10px; left: 90px; width: 200px; height: 30px;z-index: 1000;">
                    <a-select v-model="hedaoName" allowClear
                        style="width: 100%; height: 25px; font-weight: 400; font-size: 12px;" placeholder="请选择"
                        :options="hedaoOptions" show-search></a-select>
                </div>
                <LineEchart :height="'210px'" style="margin-top: 20px; " :dataSource="lineChartData"
                    :custom="lineChartCustom">
                </LineEchart>
            </div>

            <div style="width: 100%; flex: 1;">
                <TimePlaySlider v-if="times.length" :times="times" @onTimeChange="onTimeChange" style="flex: 1;" />
            </div>
        </div>
    </div>
</template>

<script lang="jsx">
import { getValueByKey, getOptions } from '@/api/common'
import Mapbox from '../Mapbox/index.vue'
import { LineLayer, Scene } from '@antv/l7';
import VxeTable from '@/components/VxeTable/index.vue'
import TimePlaySlider from './TimePlaySlider/index.vue'
import { mapBoundGeo } from '@/utils/mapBounds.js'
import axios from 'axios'
import * as echarts from 'echarts'
import LineEchart from '../Linechart/index.vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
// import hedaoData from './data.json'
import { waterColors, extractData, getChartsData } from './Utils.js'
import StationSVG from '@/assets/icons/svg/station.svg'
import arrow2 from '@/assets/images/arrow2.png'
import { forIn } from 'lodash';
let lineInverse = [
    10029, 10036, 10038,
    20004, 20008, 20012,
    20017, 20019, 20020,
    20025, 20026, 10048
]
export default {
    name: 'ShuiDongLi',
    mapIns: null, // 地图实例
    hedaoOptions: [],
    components: { VxeTable, Mapbox, LineEchart, TimePlaySlider },
    data() {
        return {
            waterColors,
            hedaoData: [],
            loading: false,
            hedaoName: '',
            lineChartData: [],
            lineChartCustom: {
                shortValue: true, // 缩写坐标值
                xLabel: '', // x轴名称
                yLabel: '水位(m)', //y轴名称
                yUnit: '', //y轴单位
                legend: true, // 图例
                showAreaStyle: true, // 颜色区域
                rYUnit: '', // 右侧y轴单位
                rYLabel: '流量(m³/s)', // 右侧y轴名称
                rYInverse: false, // 右侧y轴是否反向
                yNameLocation: "end",
                dataZoom: false,
                color: null,
                grid: {
                    left: '1%',
                    right: '1%',
                    bottom: '5%',
                    top: '15%',
                    containLabel: true,
                },
                legendOptions: {
                    orient: 'horizontal',
                },
                legendTop: '1%',
                legendLeft: '70%',
                xAxisData: []
            },
            listDrainage: [
                {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.432755755,
                        30.452525076
                    ],
                    "wlevel": 4.265,
                    "q": 4.1234
                },
                {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.432755755,
                        30.452525076
                    ],
                    "wlevel": 4.265,
                    "q": 4.1234
                }, {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.432755755,
                        30.452525076
                    ],
                    "wlevel": 4.265,
                    "q": 4.1234
                }, {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.432755755,
                        30.452525076
                    ],
                    "wlevel": 4.265,
                    "q": 4.1234
                }, {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.432755755,
                        30.452525076
                    ],
                    "wlevel": 4.265,
                    "q": 4.1234
                },
                {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.432579421,
                        30.454064999
                    ],
                    "wlevel": 4.265,
                    "q": 5.4824
                },
                {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.432038747,
                        30.455509059
                    ],
                    "wlevel": 4.265,
                    "q": 7.0043
                },
                {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.431196589,
                        30.456877102
                    ],
                    "wlevel": 4.265,
                    "q": 12.617
                },
                {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.430385053,
                        30.458257812
                    ],
                    "wlevel": 4.265,
                    "q": 12.6087
                },
                {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.429671994,
                        30.459679297
                    ],
                    "wlevel": 4.265,
                    "q": 12.5991
                },
                {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.429101548,
                        30.46081647
                    ],
                    "wlevel": 4.265,
                    "q": 15.827
                },
                {
                    "time": "2025-06-12 08:00:00",
                    "coords": [
                        120.428531101,
                        30.46195363
                    ],
                    "wlevel": 4.265,
                    "q": 19.059
                }
            ],
            columnsDrainage: [
                { type: 'seq', title: '序号', width: 50 },
                {
                    title: '时间',
                    field: 'time',
                    minWidth: 180,
                    fixed: 'left',
                    showOverflow: 'tooltip',
                },
                {
                    title: '水位(m)',
                    field: 'wlevel',
                    minWidth: 180,
                    showOverflow: 'tooltip',
                },
                {
                    title: '流量(m³/s)',
                    field: 'q',
                    minWidth: 180,
                    showOverflow: 'tooltip',
                }
            ],
            currentTime: '', // 当前时间
            times: [],
            maxFlow: 0, // 最大流量
            POINTS: null,
            maxWlevel: null,
            maxQ: null,
            minWlevel: null,
            minQ: null,
            max: 0, // 最大
            min: 0, // 最小
            selectedShow: "wlevel", // 0:水位，1:流量
        }
    },
    props: {
        modelCode: { // 模型名称
            type: String,
            default: "",
            required: true,
        },
    },
    watch: {
        async modelCode(newVal) {
            if (!this.hedaoShpData) return;
            const tempShp = JSON.parse(JSON.stringify(this.hedaoShpData))
            for (let i = 0; i < tempShp.features.length; i++) {
                if (lineInverse.includes(tempShp.features[i].properties.id)) {
                    tempShp.features[i].geometry.coordinates.forEach(item => {
                        item.reverse()
                    })
                }
            }
            await this.refreshModelRes()
            tempShp.features = tempShp.features.filter(item => ('GQ' + item.properties.id) in this.hedaoData)
            this.mapIns.getSource('gq-line-source').setData(tempShp)
            let tempArr = []
            tempShp.features.forEach(item => {
                tempArr.push({
                    label: item.properties.object_name,
                    value: item.properties.id
                })
            })
            this.hedaoOptions = tempArr
            this.hedaoName = tempArr[0].value
            mapBoundGeo(tempShp, this.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
            this.mapIns.getSource('points').setData(JSON.parse(JSON.stringify(this.POINTS)))
        },
        hedaoName(newVal, oldVal) {
            if (newVal !== oldVal && this.currentTime) {
                this.refreshBottomChart(newVal)
            }
        },
        currentTime(newVal, oldVal) { // 时间轴改变时，重新获取数据
            if (newVal !== oldVal) {
                if (!this.mapIns) return
                this.mapIns.setPaintProperty('gq-area-points', 'icon-color', ['get', this.selectedShow + "Color-" + newVal])
                this.mapIns.setLayoutProperty('gq-area-points', 'icon-size', ['get', this.selectedShow + "Size-" + newVal])
                this.refreshBottomChart(this.hedaoName)
            }
        },
        selectedShow(newVal, oldVal) { // 时间轴改变时，重新获取数据
            if (newVal !== oldVal) {
                if (!this.mapIns) return
                this.mapIns.setPaintProperty('gq-area-points', 'icon-color', ['get', newVal + "Color-" + this.currentTime])
                this.mapIns.setLayoutProperty('gq-area-points', 'icon-size', ['get', newVal + "Size-" + this.currentTime])
                if (newVal == "wlevel") {
                    this.max = this.maxWlevel;
                    this.min = this.minWlevel;
                } else {
                    this.max = this.maxQ;
                    this.min = this.minQ;
                }
            }
        }
    },
    methods: {
        async refreshModelRes() {
            this.$emit('changeLoading', true)
            await axios.get(`${process.env.VUE_APP_MODEL_BASE}/${this.modelCode}.json`, {
                timeout: 60 * 1000
            })
                .then(res => {
                    this.hedaoData = res.data
                    let { times, POINTS, maxWlevel, maxQ, minWlevel, minQ } = extractData(res.data)
                    this.times = times;
                    this.POINTS = POINTS;
                    this.maxWlevel = maxWlevel;
                    this.maxQ = maxQ;
                    this.minWlevel = minWlevel;
                    this.minQ = minQ;
                    if (this.selectedShow == "wlevel") {
                        this.max = this.maxWlevel;
                        this.min = this.minWlevel;
                    } else {
                        this.max = this.maxQ;
                        this.min = this.minQ;
                    }

                    this.listDrainage = this.times.map(time => {
                        return {
                            time: time,
                            wlevel: parseFloat(this.POINTS.features[0].properties["wlevel-" + time]).toFixed(3),
                            q: parseFloat(this.POINTS.features[0].properties["q-" + time]).toFixed(3)
                        }
                    })
                    this.currentTime = this.times[0];
                }).finally(() => {
                    this.$emit('changeLoading', false)
                })
        },
        refreshBottomChart(newVal) {
            let { wlevel, q, stakes, maxWlevel, maxQ, minWlevel, minQ } = getChartsData("GQ" + newVal, this.currentTime, this.hedaoData)
            let data3 = []
            let data1 = []
            let data2 = []
            stakes.forEach((element, index) => {
                data3.push([stakes[index]])
                data1.push(+(wlevel[index]))
                data2.push(+(q[index]))
            });
            let res = [{
                name: '水位(m)',
                color: '#507EF7',
                yAxisIndex: 0,
                data: data1
            },
            {
                name: '流量(m³/s)',
                color: '#B5E241',
                yAxisIndex: 1,
                data: data2
            }]
            this.lineChartCustom.xAxisData = data3
            this.lineChartCustom.yMax0 = maxWlevel
            this.lineChartCustom.yMin0 = minWlevel
            this.lineChartCustom.yMax1 = maxQ
            this.lineChartCustom.yMin1 = minQ
            this.lineChartData = res
        },
        //添加以上两个图层之后 再添加这个函数，参数是动态线的图层配置
        addDashLayer(sourceLayerConfig) {
            const self = this
            const dashArraySequence = [
                [0, 4, 3],
                [0.5, 4, 2.5],
                [1, 4, 2],
                [1.5, 4, 1.5],
                [2, 4, 1],
                [2.5, 4, 0.5],
                [3, 4, 0],
                [0, 0.5, 3, 3.5],
                [0, 1, 3, 3],
                [0, 1.5, 3, 2.5],
                [0, 2, 3, 2],
                [0, 2.5, 3, 1.5],
                [0, 3, 3, 1],
                [0, 3.5, 3, 0.5]
            ];
            let step = 0;
            function animateDashArray(timestamp) {
                // Update line-dasharray using the next value in dashArraySequence. The
                // divisor in the expression `timestamp / 50` controls the animation speed.
                const newStep = parseInt(
                    (timestamp / 100) % dashArraySequence.length
                );

                if (newStep !== step) {
                    let layer = self.mapIns.getLayer(sourceLayerConfig.id); //获取图层
                    // debugger
                    if (layer) {
                        self.mapIns.setPaintProperty(
                            sourceLayerConfig.id,
                            'line-dasharray',
                            dashArraySequence[step]
                        );
                        step = newStep;
                    }
                }

                // Request the next frame of the animation.
                requestAnimationFrame(animateDashArray);
            }

            // start the animation
            animateDashArray(0);
        },
        async initMap(mapIns) {
            let hoveredPolylineId = null;
            const img = new Image();
            img.src = StationSVG;
            img.onload = () => {
                mapIns.addImage('stationSVG', img, { sdf: true });
            }
            const img1 = new Image();
            img1.src = arrow2;
            img1.onload = () => {
                mapIns.addImage('arrow2', img1, { sdf: true });
            }
            // 水系
            getOptions('main_canal').then(res => {
                axios(
                    // `${import.meta.env.VITE_GEOSERVER_BASE}/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=sthgq_vector:HP005`,
                    `https://www.sthzxgq.cn:11120/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=sthgq_vector:HP005`,
                ).then(resp => {

                    this.hedaoShpData = resp.data;
                    const arr = []
                    res.data.forEach(el => {
                        arr.push(...[+el.key, el.option1])
                    })
                    /**
                    * 搜集河道线要素 object_name 为名称 id 为编码
                    */
                    let tempShp = JSON.parse(JSON.stringify(this.hedaoShpData))
                    for (let i = 0; i < tempShp.features.length; i++) {
                        if (lineInverse.includes(tempShp.features[i].properties.id)) {
                            tempShp.features[i].geometry.coordinates.forEach(item => {
                                item.reverse()
                            })
                        }
                    }
                    tempShp.features = tempShp.features.filter(item => {
                        return ('GQ' + item.properties.id) in this.hedaoData
                    })
                    let tempArr = []
                    tempShp.features.forEach(item => {
                        tempArr.push({
                            label: item.properties.object_name,
                            value: item.properties.id
                        })
                    })
                    this.hedaoOptions = tempArr
                    this.hedaoName = tempArr[0].value

                    mapBoundGeo(tempShp, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
                    mapIns.addSource('gq-line-source', {
                        type: 'geojson',
                        data: tempShp, //区划的面数据
                        'generateId': true // 确保所有特征都有唯一的ID
                    })

                    mapIns.addLayer({
                        'id': 'gq-line1',
                        'type': 'line',
                        'slot': 'middle',
                        'source': 'gq-line-source',
                        'layout': {
                            'line-join': 'round',
                            'line-cap': 'round'
                        },
                        'paint': {
                            // "line-pattern": "arrowsdf",
                            'line-color': "rgba(255,255,255,0.7)",
                            'line-width': ['case',
                                ['boolean', ['feature-state', 'hover'], false],
                                5,
                                [
                                    'case',
                                    ['==', ['get', 'id'], 10004],
                                    5,
                                    ['==', ['get', 'id'], 10005],
                                    5,
                                    ['==', ['get', 'id'], 20005],
                                    6,
                                    3,
                                ]]
                        }
                    });

                    mapIns.addLayer({
                        'id': 'gq-line2',
                        'type': 'line',
                        'slot': 'middle',
                        'source': 'gq-line-source',
                        'layout': {
                            'line-join': 'round',
                            'line-cap': 'round'
                        },
                        'paint': {
                            "line-pattern": "arrow2",
                            "line-pattern-cross-fade": 0.5,
                            'line-color': "#09Acff",
                            "line-opacity": 0.8,
                            'line-width': ['case',
                                ['boolean', ['feature-state', 'hover'], false],
                                5,
                                [
                                    'case',
                                    ['==', ['get', 'id'], 10004],
                                    12,
                                    ['==', ['get', 'id'], 10005],
                                    12,
                                    ['==', ['get', 'id'], 20005],
                                    15,
                                    10,
                                ]],
                            "line-dasharray": [2, 30],
                            // 'line-parttern':'arrowsdf'
                        }
                    });

                    mapIns.addLayer({
                        'id': 'gq-line',
                        'type': 'line',
                        'slot': 'middle',
                        'source': 'gq-line-source',
                        'layout': {
                            "visibility": "visible"
                        },
                        'paint': {
                            'line-color': "#09Acff",
                            'line-width': ['case',
                                ['boolean', ['feature-state', 'hover'], false],
                                5,
                                [
                                    'case',
                                    ['==', ['get', 'id'], 10004],
                                    3,
                                    ['==', ['get', 'id'], 10005],
                                    3,
                                    ['==', ['get', 'id'], 20005],
                                    3,
                                    2,
                                ]]
                        }
                    });
                    this.addDashLayer({
                        'id': 'gq-line'
                    })
                    mapIns.addInteraction('gq-line-click-interaction', {
                        type: 'click',
                        target: { layerId: 'gq-line' },
                        handler: (e) => {
                            // Copy coordinates array.
                            const coordinates = e.lngLat;
                            const outerHtml = document.createElement('div');
                            outerHtml.className = 'outerPaiShuiLine';
                            const description = e.feature.properties.object_name;
                            this.hedaoName = this.hedaoOptions.find(item => item.label == description).value;
                            outerHtml.textContent = description;
                            // this.extractData(e.feature.properties.id)
                            new mapboxgl.Popup()
                                .setLngLat(coordinates)
                                .setDOMContent(outerHtml)
                                .addTo(mapIns);
                        }
                    });
                    mapIns.on('mousemove', 'gq-line', (e) => {
                        if (e.features.length > 0) {
                            if (hoveredPolylineId !== null) {
                                mapIns.setFeatureState(
                                    { source: 'gq-line-source', id: hoveredPolylineId },
                                    { hover: false }
                                );
                            }
                            hoveredPolylineId = e.features[0].id;
                            mapIns.setFeatureState(
                                { source: 'gq-line-source', id: hoveredPolylineId },
                                { hover: true }
                            );
                        }
                    });
                    mapIns.on('mouseleave', 'gq-line', () => {
                        if (hoveredPolylineId !== null) {
                            mapIns.setFeatureState(
                                { source: 'gq-line-source', id: hoveredPolylineId },
                                { hover: false }
                            );
                        }
                        hoveredPolylineId = null;
                    });
                })
            })
            mapIns.addSource('points', {
                type: 'geojson',
                data: JSON.parse(JSON.stringify(this.POINTS)), //区划的面数据
                'generateId': true // 确保所有特征都有唯一的ID
            })
            mapIns.addLayer({
                'id': 'gq-area-points',
                'type': 'symbol',
                'slot': 'top',
                'source': 'points',
                'layout': {
                    'text-field': ['get', 'name'],
                    'text-size': 11,
                    'text-font': ['Open Sans Semibold'],
                    'text-offset': [4, -1],
                    "icon-image": "stationSVG",
                    "icon-size": ['get', "wlevelSize-" + this.currentTime],
                    // "icon-rotate": ['get', 'bearing'],
                    "icon-allow-overlap": true,
                    "icon-ignore-placement": true,
                    // 'text-anchor': 'top',
                },
                'paint': {
                    // "circle-color": ['get', "wlevelColor-" + this.currentTime],
                    // "circle-radius": 7
                    "text-color": "#FFFFFF",
                    "icon-color": ['get', "wlevelColor-" + this.currentTime],
                    // 'icon-halo-color': 'rgba(255, 255, 255, 0.5)', // 图标的光晕颜色
                    // "icon-color-brightness-max":1,
                    // 'icon-halo-width': 0.03, // 图标的光晕宽度
                    // 'icon-halo-blur': 0.02 // 图标的光晕模糊宽度

                }
            });
            mapIns.addInteraction('gq-area-points-click-interaction', {
                type: 'click',
                target: { layerId: 'gq-area-points' },
                handler: (e) => {
                    // 获取当前点击的要素的坐标
                    const coordinates = e.feature.geometry.coordinates;
                    // 获取当前点击的要素的属性
                    const properties = e.feature.properties;
                    // 更新右侧表格数据
                    this.listDrainage = this.times.map(time => {
                        return {
                            time: time,
                            wlevel: parseFloat(properties["wlevel-" + time]).toFixed(3),
                            q: parseFloat(properties["q-" + time]).toFixed(3)
                        }
                    })

                    const wlevels = this.times.map(time => +properties["wlevel-" + time])
                    const qs = this.times.map(time => +properties["q-" + time])
                    const popup = new mapboxgl.Popup().setLngLat(coordinates)
                    const option = {
                        grid: {
                            left: '4%',
                            right: '6%',
                            bottom: '5%',
                            top: '15%',
                            containLabel: true,
                        },
                        tooltip: {
                            appendToBody: true,
                            confine: true,
                            position: (pos, params, dom, rect, size) => {
                                let obj = { bottom: size.viewSize[1] - pos[1] + 10 }
                                obj[['right', 'left'][+(pos[0] < size.viewSize[0] / 2)]] =
                                    pos[0] < size.viewSize[0] / 2 ? pos[0] + 10 : size.viewSize[0] - pos[0] + 10
                                return obj
                            },
                            trigger: 'axis',
                            // backgroundColor: 'rgba(0,0,0,0.6)', //通过设置rgba调节背景颜色与透明度
                            borderWidth: 0,
                            textStyle: {
                                color: '#000',
                            },
                            axisPointer: {
                                type: 'cross',
                                label: {
                                    backgroundColor: '#6a7985',
                                },
                            },
                        },
                        xAxis: {
                            data: this.times.length ? this.times : [],
                            name: '时间',
                            nameTextStyle: {
                                padding: [0, 0, 0, -5],
                                color: '#000',
                                fontSize: 12,
                                fontWeight: 400,
                            },
                            axisLabel: {
                                textStyle: {
                                    color: '#000',
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            z: 10,
                        },
                        yAxis: [
                            {
                                name: '水位(m)',
                                axisPointer: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        type: 'dashed',
                                        color: '#BBB',
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLabel: {
                                    textStyle: {
                                        color: '#000',
                                    },
                                },
                            },
                            {
                                name: '流量(m³/s)',
                                position: 'right',
                                axisPointer: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                splitLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLabel: {
                                    textStyle: {
                                        color: '#000',
                                    }
                                },
                            },
                        ],
                        legend: {
                            show: true,
                            icon: 'rect',
                            itemWidth: 20,
                            itemHeight: 3,
                            top: 5,
                            left: '50%',
                            textStyle: {
                                color: '#000',
                            },
                            orient: 'horizontal'
                        },
                        series: [
                            {
                                type: 'line',
                                showBackground: true,
                                smooth: false,
                                showSymbol: true,
                                symbolSize: 2,
                                symbol: 'circle',
                                name: "水位",
                                color: "#507EF7",
                                yAxisIndex: 0,
                                emphasis: {
                                    focus: 'series',
                                },
                                data: wlevels
                            },
                            {
                                type: 'line',
                                showBackground: true,
                                smooth: false,
                                showSymbol: true,
                                symbolSize: 2,
                                symbol: 'circle',
                                name: "流量",
                                color: "#B5E241",
                                yAxisIndex: 1,
                                emphasis: {
                                    focus: 'series',
                                },
                                data: qs
                            }
                        ]
                    }
                    const outerHtml1 = document.createElement('div');
                    outerHtml1.className = 'outerPaiShui1';
                    const innerTop1 = document.createElement('div');
                    innerTop1.className = 'innerTop';
                    innerTop1.textContent = "过程曲线"
                    const chart = document.createElement('div');
                    chart.className = 'chart';
                    outerHtml1.appendChild(innerTop1)
                    outerHtml1.appendChild(chart)
                    popup.setDOMContent(outerHtml1).addTo(mapIns);
                    const myChart = echarts.init(chart);
                    myChart.setOption(option);
                }
            });
            mapIns.resize();
        },
        onTimeChange(time) {
            this.currentTime = time
        },
        getColorByValue(value) { // 根据值获取颜色
            const maxValue = this.maxFlow // 假设最大值为100
            const index = Math.floor((value / maxValue) * (waterColors.length - 1)) // 计算索引
            return waterColors[index] // 返回对应的颜色
        },
        getSizeByValue(value) { // 根据值获取颜色
            const maxValue = this.maxFlow
            const size = Math.floor(value / maxValue) * 10 // 计算大小
            return size ? size : 0
        },
        // 表头样式
        headerCellClassName({ column }) {
            return 'col-blue'
        },
        // 行样式
        rowClassName() {
            return 'row-green'
        },
        async onMapMounted(mapIns, l7Scene) {
            await this.refreshModelRes() // 刷新模型数据
            this.mapIns = mapIns
            this.l7Scene = l7Scene
            this.initMap(mapIns, l7Scene)
        }
    },
}

</script>
<style lang="less" scoped>
::v-deep .mapboxgl-popup {
    max-width: 500px !important;

    .mapboxgl-popup-content {
        padding: 0px;

        .mapboxgl-popup-close-button {
            color: #fff;
        }
    }
}

::v-deep .outerPaiShuiLine {
    width: 150px;
    // height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    background-color: rgb(83, 132, 254);
}

::v-deep .outerPaiShui {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .innerTop {
        width: 100%;
        // height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        font-weight: 500;
        color: #fff;
        background-color: rgb(83, 132, 254);
    }

    .innerBottom {
        // height: 40px;
        // line-height: 40px;
        // background-color: red;
        text-align: center;
        font-size: 11px;
        font-weight: 350;
        color: #000000;
        margin-top: 5px;
        // margin-bottom: 5px;
    }

    .showChart {
        // background-color: red;
        color: rgb(83, 132, 254);
        // margin-top: 5px;
        font-size: 11px;
        font-weight: 350;
        margin-right: -50%;
        margin-bottom: 5px;
        cursor: pointer;
    }
}

::v-deep .outerPaiShui1 {
    .innerTop {
        width: 100%;
        // height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        font-weight: 500;
        color: #fff;
        background-color: rgb(83, 132, 254);
    }

    .chart {
        height: 300px;
        width: 500px;
        background-color: #fff;
    }
}
</style>
