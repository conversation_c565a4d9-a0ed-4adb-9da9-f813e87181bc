<template>
  <div style="margin-top: 12px; height: calc(100% - 12px); display: flex; background: #ffffff; position: relative">
    <div style="flex: 1; display: flex; flex-direction: column">
      <div style="flex: 1; position: relative; border-radius: 8px; overflow: hidden">
        <MapBox @onMapMounted="onMapMounted" />
        <MapStyle v-if="!!mapIns" v-show="false" :mapIns="mapIns" activeStyle="卫星图" ref="mapStyleRef" />

        <div class="curve-panel" v-if="!!activeProcess">
          <div class="left">
            <div class="header">
              <div class="icon">{{ activeProcess.ditch.projectName.slice(0, 1) }}</div>
              <div class="name">{{ activeProcess.projectName }}</div>
            </div>

            <div v-if="activeProcess.ditch.projectObj.objectCategoryCode === 'HP021'">
              <div class="indicator">
                <div class="label">坝前水位:</div>
                <div class="value">{{ activeProcess.damFrontWlv }}m</div>
              </div>
              <div class="indicator">
                <div class="label">过坝流量:</div>
                <div class="value">{{ activeProcess.damDischarge }}m³/s</div>
              </div>
            </div>

            <div v-else>
              <div class="indicator">
                <div class="label">上游水位:</div>
                <div class="value">{{ activeProcess.upWlv }}m</div>
              </div>
              <div class="indicator">
                <div class="label">下游水位:</div>
                <div class="value">{{ activeProcess.downWlv }}m</div>
              </div>
              <div v-if="activeProcess.type === 0 || activeProcess.type === 2">
                <div class="indicator">
                  <div class="label">过闸流量:</div>
                  <div class="value">{{ activeProcess.outFlow }}m³/s</div>
                </div>
                <div class="indicator">
                  <div class="label">闸门开度:</div>
                  <div class="value">{{ activeProcess.outFlow }}%</div>
                </div>
              </div>
              <div class="indicator" v-if="activeProcess.type === 1 || activeProcess.type === 4">
                <div class="label">抽水流量:</div>
                <div class="value">{{ activeProcess.inFlow }}m³/s</div>
              </div>
            </div>

            <div style="text-align: center; margin-bottom: 10px">
              <a-button type="primary" size="small" @click.stop="activeProcess = null">收起曲线</a-button>
            </div>
          </div>
          <div class="right">
            <ProcessChart :dataSource="activeProcess" />
          </div>
        </div>
      </div>
      <div style="height: 44px">
        <TimePlaySlider v-if="times.length && !!mapIns" :times="times" @onTimeChange="onTimeChange" />
      </div>
    </div>
    <div style="width: 464px; margin-left: 12px">
      <SimulationChart
        v-if="!!chartData && !!markLineXAxis1"
        :dataSource="chartData"
        :markLineXAxis1="markLineXAxis1"
        :markLineXAxis2="markLineXAxis1 + westMainCanalForkIndex"
      />
    </div>
  </div>
</template>

<script lang="jsx">
  import MapBox from '@/components/MapBox/index.vue'
  import MapStyle from '@/components/MapBox/MapStyle.vue'
  import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
  import { MapboxOverlay } from '@deck.gl/mapbox'
  import { getOptions } from '@/api/common'
  import { mapboxPopup } from './popup.js'
  import initLayer from './initLayer.js'
  import { getChSimResList } from '../../services.js'
  import ProcessChart from './components/ProcessChart.vue'
  import SimulationChart from './components/SimulationChart.vue'
  import { getValueByKey } from '@/api/common.js'

  export default {
    name: 'Simulation',
    props: ['chSimId', 'dataSource', 'mapData'],
    components: { MapBox, MapStyle, TimePlaySlider, ProcessChart, SimulationChart },
    data() {
      return {
        mapIns: null,
        mapOverlayIns: null,
        times: [],
        geojson: null,

        activeProcess: null,
        showPopupItem: [],
        currentTime: null,

        chartData: null,
        markLineXAxis1: null,
        westMainCanalForkIndex: null,
      }
    },
    computed: {},
    created() {
      this.times = [...new Set(this.mapData[0].resVOS.map(el => el.tm))]

      getOptions('scaleProjectCode').then(res => {
        this.showPopupItem = res.data.map(el => ({ projectCode: el.key, projectName: el.value }))

        this.onTimeChange(this.currentTime)
      })

      getValueByKey('westMainCanalForkIndex').then(res => {
        this.westMainCanalForkIndex = +res.data
      })
    },
    methods: {
      onMapMounted(mapIns) {
        this.mapIns = mapIns

        this.$nextTick(() => {
          this.mapIns.resize()
        })
        this.mapOverlayIns = new MapboxOverlay({
          id: 'deck-geojson-layer-overlay',
          layers: [],
        })
        this.mapIns.addControl(this.mapOverlayIns)
        this.dealLayers()
      },
      onTimeChange(time) {
        this.currentTime = time

        if (this.mapData.length > 0) {
          let arr = []
          this.mapData.forEach(el => {
            arr = arr.concat(el.resVOS.find(ele => ele.tm == time)?.records)
          })
          const factArr = arr.filter(el => !!+el?.longitude && !!+el?.latitude)
          if (factArr.length === 0) return
          this.geojson = {
            type: 'FeatureCollection',
            features: factArr.map(el => {
              return {
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [+el.longitude, +el.latitude],
                },
                properties: {
                  ...el,
                },
              }
            }),
          }

          if (!this.mapOverlayIns?._props?.layers?.length > 0) {
            this.dealLayers()
          }

          this.handleOpenPopup(factArr)

          if (!!this.activeProcess) {
            this.activeProcess = {
              chartData: this.activeProcess.chartData,
              ...factArr.find(el => el.projectId === this.activeProcess.projectId),
            }
          }
        }
        if (!!this.dataSource) {
          this.markLineXAxis1 = this.dataSource['GQ2701'].bottom.length - 1

          const stakes = this.dataSource['GQ2701'].stakes.concat(
            this.dataSource['**********'].stakes
              .slice(0, this.westMainCanalForkIndex)
              .map(el => el + this.dataSource['GQ2701'].stakes[this.dataSource['GQ2701'].stakes.length - 1]),
          )

          this.chartData = {
            chart1: [
              {
                name: '水位',
                color: '#72C5F5',
                data:
                  this.dataSource['GQ2701'].data[this.currentTime + ':00']?.wlevel
                    .concat(
                      this.dataSource['**********'].data[this.currentTime + ':00'].wlevel.slice(
                        0,
                        this.dataSource['**********'].bottom.length,
                      ),
                    )
                    .map((el, idx) => [idx, el]) || [],
              },
              {
                name: '渠底',
                color: '#D1CBA8',
                data: this.dataSource['GQ2701'].bottom
                  .concat(this.dataSource['**********'].bottom)
                  .map((el, idx) => [idx, el]),
              },
              {
                name: '流量',
                color: '#F7BA1E',
                yAxisIndex: 1,
                data:
                  this.dataSource['GQ2701'].data[this.currentTime + ':00']?.q
                    .concat(
                      this.dataSource['**********'].data[this.currentTime + ':00'].q.slice(
                        0,
                        this.dataSource['**********'].bottom.length,
                      ),
                    )
                    .map((el, idx) => [idx, el]) || [],
              },
            ],
            chart1Stakes: this.dataSource['GQ2701'].stakes.concat(
              this.dataSource['**********'].stakes.map(
                el => el + this.dataSource['GQ2701'].stakes[this.dataSource['GQ2701'].stakes.length - 1],
              ),
            ),
            chart2: [
              {
                name: '水位',
                color: '#72C5F5',
                data:
                  this.dataSource['GQ2701'].data[this.currentTime + ':00']?.wlevel
                    .concat(
                      this.dataSource['**********'].data[this.currentTime + ':00'].wlevel.slice(
                        0,
                        this.dataSource['**********'].bottom.length,
                      ),
                    )
                    .map((el, idx) => [idx, el]) || [],
              },
              {
                name: '渠底',
                color: '#D1CBA8',
                data: this.dataSource['GQ2701'].bottom
                  .concat(this.dataSource['**********'].bottom)
                  .map((el, idx) => [idx, el]),
              },
              {
                name: '流量',
                color: '#F7BA1E',
                yAxisIndex: 1,
                data:
                  this.dataSource['GQ2701'].data[this.currentTime + ':00']?.q
                    .concat(
                      this.dataSource['**********'].data[this.currentTime + ':00'].q.slice(
                        0,
                        this.dataSource['**********'].bottom.length,
                      ),
                    )
                    .map((el, idx) => [idx, el]) || [],
              },
            ],
            chart2Stakes: this.dataSource['GQ2701'].stakes.concat(
              this.dataSource['**********'].stakes.map(
                el => el + this.dataSource['GQ2701'].stakes[this.dataSource['GQ2701'].stakes.length - 1],
              ),
            ),
            chart3: [
              {
                name: '水位',
                color: '#72C5F5',
                data:
                  this.dataSource['GQ2701'].data[this.currentTime + ':00']?.wlevel
                    .concat(
                      this.dataSource['**********'].data[this.currentTime + ':00'].wlevel.slice(
                        0,
                        this.westMainCanalForkIndex,
                      ),
                    )
                    .concat(this.dataSource['**********'].data[this.currentTime + ':00'].wlevel)
                    .map((el, idx) => [idx, el]) || [],
              },
              {
                name: '渠底',
                color: '#D1CBA8',
                data: this.dataSource['GQ2701'].bottom
                  .concat(this.dataSource['**********'].bottom.slice(0, this.westMainCanalForkIndex))
                  .concat(this.dataSource['**********'].bottom)
                  .map((el, idx) => [idx, el]),
              },
              {
                name: '流量',
                color: '#F7BA1E',
                yAxisIndex: 1,
                data:
                  this.dataSource['GQ2701'].data[this.currentTime + ':00']?.q
                    .concat(
                      this.dataSource['**********'].data[this.currentTime + ':00'].q.slice(
                        0,
                        this.westMainCanalForkIndex,
                      ),
                    )
                    .concat(this.dataSource['**********'].data[this.currentTime + ':00'].q)
                    .map((el, idx) => [idx, el]) || [],
              },
            ],
            chart3Stakes: stakes.concat(this.dataSource['**********'].stakes.map(el => el + stakes[stakes.length - 1])),
          }
        }
      },
      handleOpenPopup(factArr) {
        if (this.showPopupItem.length > 0 && !!this.mapIns) {
          this.showPopupItem.forEach(el => {
            el?.popupIns?.remove()

            if (factArr.some(ele => ele.projectCode === el.projectCode)) {
              this.dealPopup(factArr.find(ele => ele.projectCode === el.projectCode))
            }
          })
        }
      },
      dealLayers() {
        if (!!this.mapIns && !!this.geojson) {
          initLayer(this.mapIns, this.mapOverlayIns, this.geojson, item => {
            if (this.showPopupItem.every(el => el.projectCode !== item.projectCode)) {
              this.dealPopup(item)
            }
          })
        }
      },

      dealPopup(curr) {
        const popupIns = mapboxPopup(this.mapIns, {
          ...curr,
          lngLat: [+curr.longitude, +curr.latitude],
          onPopupClose: item => {
            const index = this.showPopupItem.findIndex(el => el.projectCode === item.projectCode)
            this.showPopupItem[index].popupIns.remove()
            this.showPopupItem = this.showPopupItem.filter((el, i) => i !== index)
          },
          onProcessClick: item => {
            getChSimResList({ chSimId: this.chSimId, projectId: item.projectId }).then(res => {
              this.activeProcess = { ...item, chartData: res.data }
            })
          },
        })

        popupIns.getElement().style['z-index'] = '11'

        let index = this.showPopupItem.findIndex(el => el.projectCode === curr.projectCode)
        if (index === -1) {
          this.showPopupItem.push({ projectCode: curr.projectCode, popupIns })
        } else {
          this.showPopupItem[index] = { ...this.showPopupItem[index], popupIns }
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .curve-panel {
    position: absolute;
    z-index: 99999999;
    top: 8px;
    right: 8px;
    width: 600px;
    display: flex;
    border-radius: 4px;
    overflow: hidden;
    background: #ffffff;
    .left {
      border-right: 1px solid #e5e6eb;
      width: 150px;
      position: relative;
      display: flex;
      flex-direction: column;
      .header {
        background: #f2f3f5;
        font-weight: 600;
        color: #1d2129;
        line-height: 20px;
        padding: 6px 8px;
        display: flex;
        align-items: center;
        .icon {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #0d9c47;
          color: #fff;
          display: inline-block;
          text-align: center;
          line-height: 20px;
        }
        .name {
          flex: 1;
          margin: 0 0 0 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .indicator {
        display: flex;
        padding: 6px 8px;
        justify-content: space-between;

        .label {
          color: '#4E5969';
        }
        .value {
          color: #1d2129;
        }
      }
    }
    .right {
      flex: 1;
      padding-top: 10px;
    }
  }
</style>
