<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1300"
    @cancel="cancel"
    modalHeight="880"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <div class="header" v-if="!!dataSource">
        <div class="item">
          <div class="label">方案名称:&nbsp;</div>
          <div class="value">
            <a-tooltip>
              <template slot="title">{{ dataSource.caseName }}</template>
              {{ dataSource.caseName }}
            </a-tooltip>
          </div>
        </div>
        <div class="item" style="width: 20%">
          <div class="label">仿真类型:&nbsp;</div>
          <!-- <div class="value">{{ simulateTypeOptions.find(ele => ele.value == dataSource.simulateType)?.label }}</div> -->
          <div>人工模拟</div>
        </div>
        <div class="item" style="width: 20%">
          <div class="label">预报范围:&nbsp;</div>
          <div class="value">{{ fcstRangeOptions.find(ele => ele.value == dataSource.fcstRange)?.label }}</div>
        </div>
        <div class="item" style="width: 33%">
          <div class="label">仿真时段:&nbsp;</div>
          <div class="value">
            {{ `${dataSource.startTime} - ${dataSource.endTime}` }}
          </div>
        </div>
        <div class="item">
          <div class="label">方案编号:&nbsp;</div>
          <div class="value">{{ dataSource.caseCode }}</div>
        </div>
        <div class="item" style="width: 20%">
          <div class="label">调度类型:&nbsp;</div>
          <div class="value">{{ dispatchTypeOptions.find(ele => ele.value == dataSource.dispathType)?.label }}</div>
        </div>
        <div class="item" style="width: 20%">
          <div class="label">发起人:&nbsp;</div>
          <div class="value">{{ dataSource.createdUserName }}</div>
        </div>
        <div class="item" style="width: 33%">
          <div class="label">仿真生成时间:&nbsp;</div>
          <div class="value">{{ dataSource.saveTime }}</div>
        </div>
        <div class="item">
          <div class="label">模拟应用场景:&nbsp;</div>
          <div class="value">{{ sceneOptions.find(ele => ele.value == waterData.scene)?.label }}</div>
        </div>
        <div class="item">
          <div class="label">预报来水量:&nbsp;</div>
          <div class="value">
            <span style="font-size: 18px; font-weight: 700">{{ waterData.inWaterSum }}</span>
            万m³
          </div>
        </div>
      </div>

      <div style="flex: 1">
        <ResultPanel
          v-if="!!dataSource.chSimId"
          :chSimId="dataSource.chSimId"
          :fcstRange="dataSource.fcstRange"
          :modelId="dataSource.modelId"
          @getWaterData="val => (waterData = val)"
        />
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'
  import ResultPanel from '../ResultPanel/index.vue'
  import { sceneOptions } from '../../config.js'

  export default {
    name: 'FormDrawer',
    props: ['simulateTypeOptions', 'dispatchTypeOptions', 'fcstRangeOptions'],
    components: { AntModal, ResultPanel },
    data() {
      return {
        moment,
        sceneOptions,
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '详情',
        dataSource: {},
        waterData: {},
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleShow(row) {
        this.open = true
        this.dataSource = row
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
  .header {
    padding: 6px 16px;
    background: #e8f3ff;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    .item {
      display: flex;
      width: 25%;
      height: 26px;
      line-height: 26px;
      .label {
        color: #4e5969;
        white-space: nowrap;
      }
      .value {
        color: #1d2129;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
