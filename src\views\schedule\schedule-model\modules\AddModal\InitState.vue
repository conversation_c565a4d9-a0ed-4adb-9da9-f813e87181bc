<template>
  <div style="height: 100%; display: flex; gap: 16px">
    <div style="flex: 1; margin-top: 10px">
      <MapBox @onMapMounted="onMapMounted" />
      <MapStyle v-if="!!mapIns" v-show="false" :mapIns="mapIns" activeStyle="卫星图" ref="mapStyleRef" />
      <MapLayer
        v-if="!!mapIns && !!dataSource.length"
        :mapIns="mapIns"
        :dataSource="dataSource"
        @handleClickLine="handleClickLine"
      />
    </div>
    <div style="width: 550px; margin-top: 10px; display: flex; flex-direction: column">
      <a-select
        v-model="river"
        style="width: 240px; margin: 10px 0 16px"
        :options="dataSource"
        placeholder="请选择"
        allowClear
      />
      <div style="flex: 1">
        <VxeTable
          :key="tableKey"
          size="small"
          :columns="columns"
          :tableData="tableList"
          :loading="loading"
          :isDrop="false"
          :tablePage="false"
          :isShowTableHeader="false"
        />
      </div>
      <div style="height: 260px; margin-top: 10px; display: flex; flex-direction: column">
        <BaseEchart :height="'100%'" style="height: 100%; width: 100%" :option="chartOptions" />
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import MapBox from '@/components/MapBox/index.vue'
  import MapStyle from '@/components/MapBox/MapStyle.vue'
  import VxeTable from '@/components/VxeTable/index.vue'
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import { getChWaterList } from '../../services'
  import MapLayer from './components/MapLayer.vue'
  import Vue from 'vue'
  import PopupContent from './components/PopupContent.vue'
  import mapboxgl from 'mapbox-gl'
  import * as _ from 'lodash'
  import guanquGeoJson from './guanquGeoJson.js'

  export default {
    name: 'InitState',
    components: { MapBox, MapStyle, VxeTable, BaseEchart, MapLayer },
    props: ['baseInfo'],
    data() {
      return {
        river: undefined,
        mapIns: null,
        loading: false,

        dataSource: [],

        activeLine: null,
        maxPopupIndex: 9,
        mapPopupInsList: [],

        columns: [],
        tableKey: 0,
      }
    },
    computed: {
      tableList() {
        return this.river ? this.dataSource.find(el => el.newProjectName === this.river).sites : []
      },
      chartOptions() {
        if (!this.river) {
          return {}
        }

        const chartData =
          this.dataSource.find(el => el.newProjectName === this.river)?.sites.map(el => [el.mileage, el.wlv]) || []

        const d = this.dataSource.find(el => el.newProjectName === this.river)
        if (d?.sites[0]?.mileage !== d?.startMileage) {
          chartData.unshift([d.startMileage, d.sites[0].wlv])
        }

        if (d?.sites[d?.sites.length - 1]?.mileage !== d?.endMileage) {
          chartData.push([d.endMileage, d.sites[d.sites.length - 1].wlv])
        }

        return {
          grid: {
            left: '4%',
            right: '12%',
            bottom: '5%',
            top: '16%',
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            textStyle: {
              fontSize: 12,
            },
          },
          xAxis: {
            type: 'value',
            name: '河道断面',
            nameTextStyle: {
              padding: [0, 0, 0, -5],
              fontSize: 12,
            },
            axisLabel: {
              textStyle: {
                color: '#000',
                // color: 'transparent',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
              lineStyle: {
                type: 'dashed',
                color: '#BBB',
              },
            },
            min: d?.startMileage,
            max: d?.endMileage,
          },
          yAxis: {
            type: 'value',
            name: '水位(m)',
            nameTextStyle: {
              padding: [0, 0, 0, -5],
              fontSize: 12,
            },
            axisPointer: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#BBB',
              },
            },
            axisTick: {
              show: false,
            },
          },
          series: {
            type: 'line',
            showSymbol: true,
            symbolSize: 2,
            symbol: 'circle',
            data: chartData,
          },
        }
      },
    },
    watch: {
      tableList: {
        handler(newVal) {
          this.tableKey += 1
        },
        deep: true,
      },
      dataSource: {
        handler(newVal) {
          this.dealPopup()
        },
        deep: true,
      },
      river() {
        this.$nextTick(() => {
          this.dealColumns()
        })
      },
    },
    created() {},
    mounted() {},
    activated() {
      if (this.dataSource.length > 0) return

      getChWaterList({ fcstRange: this.baseInfo.fcstRange }).then(res => {
        this.dataSource = res.data.map(el => ({ ...el, label: el.projectName, value: el.newProjectName }))
      })
    },
    methods: {
      onMapMounted(mapIns) {
        this.mapIns = mapIns

        this.mapIns.addLayer({
          id: 'guanqu',
          type: 'line',
          source: {
            type: 'geojson',
            data: guanquGeoJson,
          },
          layout: {},
          paint: {
            'line-color': '#94bbfa',
            'line-width': 2,
          },
        })

        this.dealPopup()
      },
      dealColumns() {
        const dataIndex = this.dataSource.findIndex(el => el.newProjectName === this.river)
        this.columns = [
          {
            title: '监测点',
            field: 'siteName',
          },
          {
            title: '水位(m)',
            slots: {
              default: ({ row, rowIndex }) => (
                <div class='cell-box'>
                  <a-input-number
                    size='small'
                    step={0.01}
                    min={0}
                    value={this.dataSource[dataIndex].sites[rowIndex].wlv}
                    onChange={_.debounce(val => {
                      this.dataSource[dataIndex].sites[rowIndex].wlv = val
                    }, 500)}
                  />
                  <a
                    style='margin-left: 5px'
                    onClick={() =>
                      this.dataSource[dataIndex].sites.forEach((item, index) => {
                        if (index > rowIndex) {
                          item.wlv = this.dataSource[dataIndex].sites[rowIndex].wlv
                        }
                      })
                    }
                  >
                    向下填充
                  </a>
                </div>
              ),
            },
          },
        ]
        this.tableKey += 1
      },
      dealPopup() {
        if (!this.mapIns && !this.dataSource.length) {
          return
        }

        this.mapPopupInsList.forEach(el => {
          el.remove()
        })
        this.mapPopupInsList = []

        this.dataSource.forEach(el => {
          el.sites
            .filter(ele => +ele.longitude && +ele.latitude)
            .forEach(site => {
              // 处理气泡飘窗
              const popupIns = new mapboxgl.Popup({
                closeOnClick: false,
                closeButton: false,
                offset: [0, 0],
              })
              const PopupContentItem = Vue.extend(PopupContent)
              const popupTemp = new PopupContentItem({
                propsData: {
                  item: {
                    ...site,
                    onClick: () => {
                      this.maxPopupIndex += 1
                      popupIns.getElement().style.zIndex = this.maxPopupIndex
                    },
                    onChangeWlv: (item, val) => {
                      const arr = JSON.parse(JSON.stringify(this.dataSource))
                      arr.forEach(el => {
                        el.sites.forEach(ele => {
                          if (ele.siteId === item.siteId) {
                            ele.wlv = val
                          }
                        })
                      })
                      this.dataSource = arr
                    },
                  },
                },
              }).$mount().$el

              popupIns.setLngLat([+site.longitude, +site.latitude]).setDOMContent(popupTemp).addTo(this.mapIns)

              popupIns.getElement().style['z-index'] = '9'
              this.mapPopupInsList.push(popupIns)
            })
        })
      },
      handleClickLine(item) {
        this.activeLine = item

        this.river = item.name
      },
      save() {
        this.$emit('saveData', this.dataSource)
      },
    },
  }
</script>

<style lang="less" scoped>
  .cell-box {
    a {
      display: none;
    }
    &:hover {
      a {
        display: inline;
      }
    }
  }
  ::v-deep .ant-input-number-handler-wrap {
    display: none;
  }
</style>
