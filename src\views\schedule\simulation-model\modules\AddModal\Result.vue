<template>
  <div style="flex: 1">
    <div v-if="loading" style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
      <a-spin></a-spin>
    </div>

    <div
      v-else-if="!!errorInfo"
      style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center"
    >
      <a-result status="error" :sub-title="errorInfo"></a-result>
    </div>

    <div v-else-if="!!chSimId" style="height: 100%">
      <ResultPanel :chSimId="chSimId" :modelId="modelId" />
    </div>
  </div>
</template>

<script lang="jsx">
  import { forecast, getInWater } from '../../services'
  import { SocketClient } from '@/utils/sockClient.js'
  import ResultPanel from '../ResultPanel/index.vue'

  export default {
    name: 'Result',
    props: ['baseInfo', 'rainfall', 'projectFlows'],
    components: {
      ResultPanel,
    },
    data() {
      return {
        loading: false,
        socketIns: null,
        errorInfo: null,
        chSimId: undefined,
        modelId: undefined,
      }
    },
    computed: {},
    created() {
      this.loading = true
      this.$emit('update:isDisabledBtn', true)
      this.errorInfo = null

      // setTimeout(() => {
      //   this.chSimId = 27
      //   this.$emit('update:isDisabledBtn', false)
      //   this.loading = false
      // }, 500)
      // return

      forecast({ ...this.baseInfo, rains: this.rainfall, projectFlows: this.projectFlows }).then(res => {
        this.socketIns = new SocketClient()

        this.socketIns.connect('/topic/model/result', response => {
          if (response.code == 200) {
            if (res.data == response.data.id && response.data.modelType === 'JXFZ') {
              this.chSimId = response.data.id

              getInWater({ inWaterId: this.chSimId }).then(res => {
                this.modelId = res.data.modelId
              })
            }
            this.$emit('update:isDisabledBtn', false)
            this.loading = false
          } else {
            this.$emit('update:isDisabledBtn', false)
            this.loading = false
            this.errorInfo = response.message || '模型计算异常了'
          }
        })
      })
    },
    mounted() {},
    // activated() {
    //   this.socketIns.connect('/topic/model/result', response => {})
    // },
    // deactivated() {
    //   this.socketIns.disconnect()
    // },
    beforeDestroy() {
      this.socketIns.disconnect()
    },
    methods: {
      save() {
        this.$emit('saveData', this.chSimId)
      },
    },
  }
</script>

<style lang="less" scoped></style>
