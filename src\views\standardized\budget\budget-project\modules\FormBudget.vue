<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="220"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="年份" prop="year">
              <a-date-picker
                allow-clear
                :value="form.year"
                format="YYYY"
                valueFormat="YYYY"
                mode="year"
                @panelChange="
                  val => {
                    form.year = val
                  }
                "
                placeholder="请选择"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="预算金额(元)" prop="budgetAmount">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.budgetAmount"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addBudgetManage, editBudgetManage, getBudgetManageById } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  import moment from 'moment'

  export default {
    name: 'FormBudget',
    components: { AntModal },
    props: ['expenditureType'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',

        form: {
          budgetAmount: undefined,
          projectBudgetId: undefined,
          projectId: undefined,
          year: undefined,
        },
        open: false,
        rules: {
          year: [{ required: true, message: '年份不能为空', trigger: 'change, blur' }],
          budgetAmount: [{ required: true, message: '预算金额不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handleFormBudget(projectId, row) {
        this.open = true
        this.formTitle = '新增'
        this.form.projectId = projectId
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getBudgetManageById({ projectBudgetId: row?.projectBudgetId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              this.form.year = moment(res?.data?.year, 'YYYY')
            }
            this.modalLoading = false
          })
        }
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const params = {
              ...this.form,
              year: moment(this.form.year).format('YYYY'),
            }
            if (this.form?.projectBudgetId == null) {
              addBudgetManage(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editBudgetManage(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped></style>
