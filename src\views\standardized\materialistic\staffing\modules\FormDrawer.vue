<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="物业单位" prop="deptId">
              <a-select
                show-search
                placeholder="请输入"
                v-model="form.deptId"
                option-filter-prop="children"
                @change="handleChangeDept"
                :disabled="isEdit"
              >
                <a-select-option v-for="(d, index) in deptOptions" :key="index" :value="d.deptId">
                  {{ d.deptName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="姓名" prop="chargeId">
              <a-select
                show-search
                placeholder="请输入"
                @change="handleChangeUser"
                v-model="form.chargeId"
                option-filter-prop="children"
                :disabled="isEdit"
              >
                <a-select-option v-for="(d, index) in chargeOptions" :key="index" :value="d.userId">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="手机号码">
              <a-input v-model="form.mobile" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工程名称" prop="projectId">
              <a-select
                show-search
                placeholder="请输入"
                v-model="form.projectId"
                option-filter-prop="children"
                @change="handleChangeProject"
                :disabled="isEdit"
              >
                <a-select-option v-for="(d, index) in projectOptions" :key="index" :value="d.projectId">
                  {{ d.projectName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="原负责人">
              <a-input v-model="form.chargeName" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.attaches"
                :disabled="isEdit"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24" v-if="isEdit">
            <a-form-model-item label="创建人">
              <a-input v-model="form.createdUserName" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" v-if="isEdit">
            <a-form-model-item label="创建时间">
              <a-input v-model="form.createdTime" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" v-if="isEdit && form.status !== 0">
            <a-form-model-item label="审核人">
              <a-input v-model="form.checkName" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" v-if="isEdit && form.status !== 0">
            <a-form-model-item label="审核时间">
              <a-input v-model="form.checkDate" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addPropertyMgr, getPropertyMgrById, getProjectList } from '../services'
  import { getOptions, getSysUserPage } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'
  import { validEmail } from '@/utils/validate'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['userOptions', 'deptOptions'],
    data() {
      return {
        chargeOptions: [],
        projectOptions: [],
        isEdit: false,

        formTitle: '',
        loading: false,
        modalLoading: false,
        form: {
          attaches: [],
          chargeId: null,
          deptId: null,
          projectId: null,
          mobile: null,
          chargeName: null,
          checkName: '',
          checkDate: null,
        },
        open: false,
        propertyInProject: [],
        rules: {
          projectId: [{ required: true, message: '工程名称不能为空', trigger: 'change' }],
          deptId: [{ required: true, message: '物业单位不能为空', trigger: 'change' }],
          chargeId: [{ required: true, message: '	负责人不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          this.isEdit = true
          getPropertyMgrById({
            id: row.id,
          }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                attaches: res.data.attaches?.map(el => el.attachUrl),
              }
              this.getProjectById(this.form.deptId)
              this.getUserById(this.form.deptId)
              this.form.mobile = this.form.mobile
              this.form.chargeName = this.form.oldChargeName
              this.form.chargeId = this.form.newChargeId
              this.form.checkName = row.checkName
              this.form.checkDate = row.checkDate
            }
            this.modalLoading = false
          })
        }
      },
      //选择部门
      handleChangeDept(value) {
        this.getProjectById(value)
        this.getUserById(value)
      },
      handleChangeUser(value) {
        this.form.mobile = this.chargeOptions?.find(el => el.userId == value)?.mobile
      },

      handleChangeProject(value) {
        //原负责人
        this.form.chargeName = this.projectOptions?.find(el => el.projectId == value)?.chargeName
      },
      //选择部门下的项目
      getProjectById(val) {
        getProjectList({
          deptId: val,
        }).then(response => {
          this.projectOptions = response.data
        })
      },
      //选择部门下的用户
      getUserById(val) {
        getSysUserPage({
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          isDisabled: 0,
          deptId: val,
        }).then(response => {
          this.chargeOptions = response.data?.data
        })
      },

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            // this.form.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
            if (this.form.id == null) {
              addPropertyMgr(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              this.open = false
              this.$emit('ok')
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
