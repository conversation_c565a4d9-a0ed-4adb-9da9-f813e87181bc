import request from '@/utils/request'

export function getPlanList(params) {
  return request({
    url: '/custom/water/backbone/planList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

export function getYear() {
  return request({
    url: '/custom/water/backbone/page',
    method: 'post',
    data,
  })
}

// 增加
export function addWaterBackbone(data) {
  return request({
    url: '/custom/water/backbone/add',
    method: 'post',
    data,
  })
}
// 详情
export function getWaterBackboneById(params) {
  return request({
    url: '/custom/water/backbone/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editWaterBackbone(data) {
  return request({
    url: '/custom/water/backbone/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteWaterBackbone(params) {
  return request({
    url: '/custom/water/backbone/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
