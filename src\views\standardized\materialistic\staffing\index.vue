<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="姓名">
        <a-input v-model="queryParam.name" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="手机号码">
        <a-input v-model="queryParam.mobile" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="物业单位">
        <a-select
          show-search
          placeholder="请选择"
          allow-clear
          v-model="queryParam.deptId"
          option-filter-prop="children"
        >
          <a-select-option v-for="item in deptOptions" :key="item.deptId" :value="item.deptId">
            {{ item.deptName }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="审核状态">
        <a-select
          show-search
          placeholder="请选择"
          allow-clear
          v-model="queryParam.status"
          option-filter-prop="children"
        >
          <a-select-option v-for="item in auditStatusOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          :isInModal="isDetail"
          :modalHeight="modalHeight"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              管辖变动申请
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :userOptions="userOptions"
          :deptOptions="deptOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <!-- <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :inspectionTypeOptions="inspectionTypeOptions"
          :projectOptions="projectOptions"
          :propertyList="propertyList"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        /> -->
        <FormExamine
          v-if="showFormExamine"
          ref="formExamineRef"
          @ok="onOperationComplete"
          @close="showFormExamine = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree, getSysUserPage } from '@/api/common'
  import { getPropertyMgrPage, deletePropertyMgr, getPropertyMgrList } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import FormExamine from './modules/FormExamine.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'DailyMaintenance',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
      FormExamine,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        auditStatusOptions: [],
        auditStatuses: [],
        deptOptions: [],
        userOptions: [],
        isChecked: false,

        showForm: false,
        showFormDetails: false,
        showFormExamine: false,

        list: [],
        tableTitle: '人员配备',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          projectId: this.projectId,
          deptId: undefined,
          mobile: '',
          name: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
          status: undefined,
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
          },
          {
            title: '姓名',
            field: 'newChargeName',
          },
          {
            title: '手机号码',
            field: 'mobile',
          },
          {
            title: '物业单位',
            field: 'deptName',
            minWidth: 120,
            showOverflow: true,
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 120,
            showOverflow: true,
          },
          {
            title: '原负责人',
            field: 'oldChargeName',
          },
          // {
          //   title: '新负责人',
          //   field: 'newChargeName',
          // },
          {
            title: '审核状态',
            field: 'status',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.auditStatuses[row.status]?.value
              },
            },
          },
          {
            title: '创建人',
            field: 'createdUserName',
          },

          {
            title: '创建时间',
            field: 'createdTime',
            minWidth: 100,
            showOverflow: true,
          },

          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 190,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>查看</a>
                    {!this.isDetail && (
                      <span>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete(row)}>删除</a>
                        {row.status === 0 && (
                          <span>
                            <a-divider type='vertical' />
                            <a
                              v-permission={`${this.$route.meta.menuId}-audit`}
                              onClick={() => this.handleExamine(row)}
                            >
                              审核
                            </a>
                          </span>
                        )}
                      </span>
                    )}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
      getSysUserPage({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        isDisabled: 0,
        deptId: null,
      }).then(res => {
        this.userOptions = res.data.data
      })

      getPropertyMgrList({ keywords: '' }).then(res => {
        this.deptOptions = res?.data || []
      })
      getOptions('auditStatus').then(res => {
        this.auditStatusOptions = res.data
        this.auditStatuses = getFlatTreeMap(this.auditStatusOptions, 'key')
      })
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getPropertyMgrPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
        // this.names = valObj.records.map(item => item.planName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,

          deptId: undefined,
          mobile: '',
          name: '',
          status: undefined,

          pageNum: 1,
          sort: [],
        }
        this.planTimes = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /* 审核 */
      handleExamine(record) {
        this.showFormExamine = true
        this.$nextTick(() => this.$refs.formExamineRef.handle(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deletePropertyMgr({ ids: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
