<template>
  <a-form-model
    style="margin-top: 24px"
    ref="form"
    :model="form"
    :rules="rules"
    layout="horizontal"
    v-bind="{
      labelCol: { span: 6 },
      wrapperCol: { span: 8 },
    }"
  >
    <a-form-model-item label="方案名称" prop="caseName">
      <a-input v-model="form.caseName" placeholder="请输入" allow-clear />
    </a-form-model-item>
    <a-form-model-item label="片区" prop="fcstRange">
      <a-radio-group v-model="form.fcstRange" :options="fcstRangeOptions" style="margin-top: 5px" />
    </a-form-model-item>
    <a-form-model-item label="模型应用场景" prop="scene">
      <a-radio-group
        v-model="form.scene"
        :options="sceneOptions.map(el => ({ ...el, label: `${el.label}场景` }))"
        style="margin-top: 5px"
        @change="changeScene"
      />
    </a-form-model-item>
    <a-form-model-item label="开始时间" prop="startTime">
      <a-date-picker
        v-model="form.startTime"
        :disabled="form.scene === 2"
        format="YYYY-MM-DD HH:mm"
        valueFormat="YYYY-MM-DD HH:00"
        :show-time="{ format: 'HH:mm' }"
        :disabled-date="disabledStartDate"
      />
    </a-form-model-item>
    <a-form-model-item label="结束时间" prop="endTime">
      <a-date-picker
        v-model="form.endTime"
        format="YYYY-MM-DD HH:00"
        valueFormat="YYYY-MM-DD HH:00"
        :show-time="{ format: 'HH:00' }"
        :disabled-date="disabledEndDate"
      />
    </a-form-model-item>
    <a-form-model-item label="调度类型" prop="dispathType">
      <a-radio-group v-model="form.dispathType" :options="dispatchTypeOptions" style="margin-top: 5px" />
    </a-form-model-item>
  </a-form-model>
</template>

<script>
  import moment from 'moment'
  import { dispatchTypeOptions } from '../../config'

  export default {
    name: 'BasicInfo',
    props: ['inWaterEchoData', 'fcstRangeOptions', 'sceneOptions'],
    data() {
      return {
        dispatchTypeOptions,

        form: {
          caseName: undefined,
          scene: this.sceneOptions[0].value,
          dispathType: dispatchTypeOptions[0].value,
          fcstRange: this.fcstRangeOptions[0].value,
          startTime: undefined,
          endTime: undefined,
        },
        rules: {
          caseName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          scene: [{ required: true, message: '模型应用场景不能为空', trigger: 'change' }],
          dispathType: [{ required: true, message: '调度类型不能为空', trigger: 'change' }],
          fcstRange: [{ required: true, message: '预报范围不能为空', trigger: 'change' }],
          startTime: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],
          endTime: [{ required: true, message: '结束时间不能为空', trigger: 'change' }],
        },
      }
    },
    computed: {},
    watch: {
      inWaterEchoData: {
        handler(val) {
          if (!!val) {
            this.form = {
              caseName: undefined,
              startTime: this.inWaterEchoData.startTime,
              endTime: this.inWaterEchoData.endTime,
              dispathType: this.inWaterEchoData.dispathType,
              fcstRange: this.inWaterEchoData.fcstRange,
            }
          }
        },
      },
    },
    created() {},
    methods: {
      changeScene(val) {
        if (val.target.value === 2) {
          this.form.startTime = moment().add(1, 'hours').format('YYYY-MM-DD HH:mm')
          this.form.endTime = undefined
          this.$refs.form.validateField('startTime')
        } else {
          this.form.startTime = undefined
          this.form.endTime = undefined
        }
      },
      disabledStartDate(current) {
        if (this.form.scene === 1) {
          if (this.form.endTime) {
            return (
              current < moment(this.form.endTime).subtract(15, 'days') ||
              current > moment(this.form.endTime) ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          return current < moment().subtract(1, 'days')
        }
      },
      disabledEndDate(current) {
        if (this.form.scene === 1) {
          if (this.form.startTime) {
            return (
              current < moment(this.form.startTime) ||
              current > moment(this.form.startTime).add(15, 'days') ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          return current < moment().subtract(1, 'days') || current > moment().add(3, 'days')
        }
      },

      save() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.$emit('saveData', {
              ...this.form,
            })
          } else {
            this.$emit('saveData', false)
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped></style>
