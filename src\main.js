import Vue from 'vue'
import App from './App.vue'
import router from './router'
import { setPermissionDirective } from './directive/permission'
import store from './store/'
import '@/assets/icons/index.js'
import { VueAxios } from './utils/request'
import TableCellOverflow from '@/components/TableCellOverflow'
import themePluginConfig from '../config/themePluginConfig'

import '@/assets/styles/antv-theme.less'

import bootstrap from './core/bootstrap'
// import './core/lazy_use' // use lazy load components
import './permission' // permission control
import './utils/filter' // global filter
import draggable from '@/utils/drag'
import './global.less' // global style
import Global from '@/utils/global' // global variable and global constant
import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  download,
  downloadTask,
  downloadByPath,
  handleTree,
  appendTreeNode,
  removeTreeNode,
  expandTree,
  selectSysDictLabel,
  selectMenuLabel,
} from '@/utils/aidex'
import SvgIcon from '@/components/SvgIcon/index.vue'

// 定制npm组件
import 'hhzk-vue2-components/lib/hhzk-vue2-components.css'

import VxeTable from 'vxe-table'
import 'vxe-table/lib/style.css'

import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'

import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'

import QueryString from 'query-string'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import storage from 'store'

const searchObj = QueryString.parse(decodeURIComponent(location.search))
if (searchObj?.token) {
  storage.set(ACCESS_TOKEN, searchObj.token, 7 * 24 * 60 * 60 * 1000)
}

// 全局方法挂载
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectMenuLabel = selectMenuLabel
Vue.prototype.selectSysDictLabel = selectSysDictLabel
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.downloadTask = downloadTask
Vue.prototype.downloadByPath = downloadByPath
Vue.prototype.handleTree = handleTree
Vue.prototype.appendTreeNode = appendTreeNode
Vue.prototype.removeTreeNode = removeTreeNode
Vue.prototype.expandTree = expandTree
Vue.config.productionTip = false

Vue.prototype.GLOBAL = Global //挂载到Vue实例上面
// mount axios to `Vue.$http` and `this.$http`

setPermissionDirective(Vue)

Vue.use(VueAxios)
Vue.component('TableCellOverflow', TableCellOverflow)
Vue.component('SvgIcon', SvgIcon)

Vue.use(draggable)
Vue.use(VxeUI)
Vue.use(VxeTable)
Vue.use(Antd)

window.umi_plugin_ant_themeVar = themePluginConfig.theme
new Vue({
  router,
  store,
  // init localstorage, vuex
  created: bootstrap,
  render: h => h(App),
}).$mount('#app')
