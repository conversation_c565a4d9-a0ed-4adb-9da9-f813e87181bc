<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="工程名称">
        <a-input v-model="queryParam.keywords" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isHover: true }"
          :tablePage="false"
          :expand-config="{ accordion: true }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getDispatchProjectList, deleteDispatchProject, getProjectOption } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import ExpandTable from './modules/ExpandTable.vue'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'DispatchProject',
    components: {
      VxeTable,
      VxeTableForm,
      FormDrawer,
      ExpandTable,
    },
    data() {
      return {
        projectOptions: [],
        showForm: false,
        list: [],
        tableTitle: '工程列表',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          keywords: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 60, align: 'center' },
          {
            type: 'expand',
            width: 30,
            fixed: 'left',
            slots: {
              content: ({ row, rowIndex }) => {
                return <ExpandTable row={row} />
              },
            },
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 260,
            showOverflow: 'tooltip',
          },
          {
            title: '创建时间',
            field: 'createdTime',
            minWidth: 170,
            align: 'center',
          },
          {
            title: '操作',
            field: 'operate',
            width: 70,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      // 组织机构
      getProjectOption().then(res => {
        this.projectOptions = res.data.map(el => ({ label: el.projectName, value: el.projectId }))
      })
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.loading = true
        this.selectChange({ records: [] })
        getDispatchProjectList(this.queryParam).then(response => {
          this.list = response?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      handleChange(value) {
        this.queryParam.unitId = value
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.dispatchProjectId)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          keywords: '',
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.dispatchProjectId ? [row?.dispatchProjectId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteDispatchProject({ dispatchProjectIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
