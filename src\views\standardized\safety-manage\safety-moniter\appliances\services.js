import request from '@/utils/request'

// 列表分页查询
export function getToolPage(data) {
  return request({
    url: '/prjstd/tool/page',
    method: 'post',
    data,
  })
}
// 增加
export function addTool(data) {
  return request({
    url: '/prjstd/tool/add',
    method: 'post',
    data,
  })
}
// 详情
export function getTool(params) {
  return request({
    url: '/prjstd/tool/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function updateTool(data) {
  return request({
    url: '/prjstd/tool/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteTool(params) {
  return request({
    url: '/prjstd/tool/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
