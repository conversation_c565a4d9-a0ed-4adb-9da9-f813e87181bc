<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工程名称" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                :disabled="isEdit"
                tree-default-expand-all
                @change="handleProjectChange"
              ></a-tree-select>
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工程类型" prop="planType">
              <a-input v-model="form.projectType" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="年度" prop="year">
              <a-date-picker
                :disabled="isEdit"
                mode="year"
                format="YYYY"
                v-model="form.year"
                placeholder="请选择"
                allow-clear
                :open="yearShowOne"
                style="width: 100%"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
              ></a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="合同金额(元)" prop="capital">
              <a-input-number
                :disabled="isEdit"
                v-model="form.capital"
                :precision="2"
                style="width: 100%"
                placeholder="请输入"
                allow-clear
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="维修类型" prop="specialType">
              <a-select
                :disabled="isEdit"
                show-search
                placeholder="请输入"
                v-model="form.specialType"
                option-filter-prop="children"
              >
                <a-select-option v-for="item in specialTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="上报人" prop="contractAmount">
              <a-input v-model="form.userName" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="维修进展" prop="specialProgress">
              <a-select
                show-search
                :disabled="isEdit"
                placeholder="请输入"
                v-model="form.specialProgress"
                option-filter-prop="children"
              >
                <a-select-option v-for="item in specialProgressOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="维修进度(%)" prop="specialSchedule">
              <a-input-number
                v-model="form.specialSchedule"
                :precision="2"
                :disabled="isEdit"
                style="width: 100%"
                placeholder="请输入"
                allow-clear
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="维修内容" prop="content">
              <a-textarea
                style="width: 100%"
                :disabled="isEdit"
                v-model="form.content"
                placeholder="请输入"
                allow-clear
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">相关附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :disabled="isEdit"
                :fileUrl.sync="form.positionAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">图片</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :disabled="isEdit"
                :fileUrl.sync="form.phoneAttaches"
                :multiple="true"
                listType="picture-card"
                folderName="safety-manage"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24" v-if="!isEdit">
            <div class="title">审批流程</div>
            <div>
              <a-button @click="addItem">
                <a-icon type="plus" />
                新增审批人
              </a-button>
            </div>
            <div class="item-group" v-if="form.processes?.length > 0">
              <a-steps progress-dot :current="form.processes?.length" direction="vertical">
                <a-step
                  title="审批人"
                  style="position: relative; margin-bottom: 5px"
                  v-for="(item, index) in form.processes"
                  :key="index"
                >
                  <template slot="description">
                    <!-- :disabled="isEdit" -->
                    <div style="position: absolute; right: 110px; top: 1px">
                      <a-select
                        show-search
                        placeholder="请输入"
                        style="width: 160px"
                        v-model="item.userId"
                        option-filter-prop="children"
                        @change="handleChange"
                      >
                        <!-- filteredUserList chargeOptions-->
                        <a-select-option
                          v-for="(d, index) in filteredUserList"
                          :key="index"
                          :disabled="d.disabled"
                          :value="d.userId"
                        >
                          {{ d.name }}
                        </a-select-option>
                      </a-select>
                    </div>
                    <span style="position: absolute; right: 10px; top: 6px" @click="delItem(item, index)">
                      <a-icon type="delete" theme="twoTone" two-tone-color="#EB3435" />
                    </span>
                  </template>
                </a-step>
              </a-steps>
            </div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24" v-if="isEdit">
            <div class="title">审批流程</div>
            <div class="item-group" v-if="form.processes?.length > 0">
              <a-steps progress-dot :current="form.processes?.length" direction="vertical">
                <a-step title="上报人" style="position: relative; margin-bottom: 5px">
                  <template slot="description">
                    <span style="position: absolute; right: 10px; top: 1px">{{ form.createdTime }}</span>
                    <span>{{ form.createdUserName }}</span>
                  </template>
                </a-step>
                <a-step
                  title="审批人"
                  style="position: relative; margin-bottom: 5px"
                  v-for="(item, index) in form.processes"
                  :key="index"
                >
                  <template slot="description">
                    <!-- :disabled="isEdit" -->
                    <!-- <a-select show-search placeholder="请输入" v-model="item.userId" option-filter-prop="children">
                      <a-select-option v-for="(d, index) in chargeOptions" :key="index" :value="d.userId">
                        {{ d.name }}
                      </a-select-option>
                    </a-select> -->
                    <span style="position: absolute; right: 10px; top: 1px">
                      {{ item.approveDate }}
                    </span>
                    <span>
                      {{ item.userName }}
                      <span
                        :style="{
                          color:
                            item.state === 1
                              ? '#1890ff'
                              : item.state === 2
                              ? '#52c41a'
                              : item.state === 3
                              ? '#f5222d'
                              : '#1890ff',
                        }"
                      >
                        {{
                          item.state === 1
                            ? '审批中'
                            : item.state === 2
                            ? '审批通过'
                            : item.state === 3
                            ? '审批不通过'
                            : '审批中'
                        }}
                      </span>
                    </span>
                    <!-- <a-input v-model="item.userName"  disabled placeholder="请输入" allow-clear /> -->
                  </template>
                </a-step>
              </a-steps>
            </div>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addSpecial, editSpecial, getSpecialById } from '../services'
  import { getOptions, getSysUserPage } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'specialTypeOptions', 'specialProgressOptions', 'statusOptions'],
    data() {
      return {
        isEdit: false,
        yearShowOne: false, //年份打开关闭状态
        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          capital: null,
          content: '',
          phoneAttaches: [],
          positionAttaches: [],
          processes: [],
          // processes: [
          //   {
          //     sort: 0,
          //     userId: 0,
          //   },
          // ],
          projectId: null,
          projectType: '',
          userName: JSON.parse(localStorage.getItem('user'))?.name,
          specialId: null,
          specialProgress: '',
          specialSchedule: '',
          specialType: '',
          year: null,
        },
        open: false,
        rules: {
          // inspectionType: [{ required: true, message: '检查类型不能为空', trigger: 'change' }],

          projectId: [{ required: true, message: '工程名称不能为空', trigger: 'change' }],
          year: [{ required: true, message: '年度不能为空', trigger: 'change' }],
          capital: [{ required: true, message: '合同金额不能为空', trigger: 'blur' }],
          specialSchedule: [{ required: true, message: '维修进度不能为空', trigger: 'blur' }],
          content: [{ required: true, message: '维修内容不能为空', trigger: 'blur' }],
          specialType: [{ required: true, message: '维修类型不能为空', trigger: 'change' }],
          specialProgress: [{ required: true, message: '维修进展不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      getSysUserPage({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        isDisabled: 0,
        deptId: null,
      }).then(response => {
        this.chargeOptions = response.data?.data?.map(user => ({
          ...user,
          disabled: false,
        }))
      })
    },
    mounted() {},
    computed: {
      filteredUserList() {
        let list = []
        this.chargeOptions.forEach(user => {
          user.disabled = false
        })

        list = this.chargeOptions.map(user => {
          if (this.form.processes.some(item => item.userId == user.userId)) {
            return { ...user, disabled: true }
          }
          return user
        })

        return list
      },
    },
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          this.isEdit = true
          getSpecialById({ specialId: row.specialId }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                positionAttaches: res.data.positionAttaches?.map(el => el.attachUrl),
                phoneAttaches: res.data.phoneAttaches?.map(el => el.attachUrl),
              }
              // this.form.specialType = String(this.form.specialType)
              this.form.year = moment(`${res.data.year}-01-01`)
              this.form.userName = JSON.parse(localStorage.getItem('user'))?.name
              this.form.processes = res.data?.specialLogs
              this.modalLoading = false
            }
          })
        }
      },

      handleChange(value) {},
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 检查项增加
      addItem() {
        this.form.processes.push({
          userId: '',
          sort: this.form.processes.length + 1,
        })
      },
      // 检查项删除
      delItem(item, index) {
        this.form.processes.splice(index, 1)
      },
      handleProjectChange(value) {
        // this.form.projectId = value
        // this.form.projectType = this.projectOptions.find(item => item.projectId == value)?.objectCategoryId
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            // this.form.startTime = moment(this.form.startTime).format('YYYY-MM-DD')

            if (!this.form.processes.length) {
              this.$message.info('审批人不能为空', 3)
              this.loading = false
              return
            } else if (this.form.processes.some(process => process.userId === null || process.userId === '')) {
              this.$message.info('审批人不能为空', 3)
              this.loading = false
              return
            }

            if (this.form.specialId == null) {
              addSpecial(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              this.open = false
              this.$emit('ok')
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
  .item-group {
    margin-top: 10px;

    .item {
      margin-top: 10px;
      padding: 10px;
      background: #f2f2f2;
      .item-title {
        font-size: 14px;
        font-weight: 700;
      }
    }
  }
</style>
