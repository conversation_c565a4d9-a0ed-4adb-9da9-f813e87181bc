<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="发票号" prop="invoiceNo">
              <a-input v-model="form.invoiceNo" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="支付内容">
              <a-textarea v-model="form.payContent" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="汇入单位">
              <a-input v-model="form.receivingUnit" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="支出类型">
              <a-select allowClear v-model="form.payType" placeholder="请选择" :options="expenditureType"></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="支付金额(元)" prop="payAmount">
              <a-input-number
                allow-clear
                style="width: 100%"
                placeholder="请输入"
                v-model="form.payAmount"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="经办人">
              <a-input v-model="form.operator" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="报送人">
              <a-input v-model="form.sender" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="支付日期" prop="payDate">
              <a-date-picker
                allow-clear
                style="width: 200px"
                v-model="form.payDate"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="备注">
              <a-textarea v-model="form.remark" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addBudgetPayment, editBudgetPayment, getBudgetPaymentById } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormPayment',
    components: { AntModal },
    props: ['expenditureType'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          invoiceNo: undefined,
          operator: undefined,
          payAmount: undefined,
          payContent: undefined,
          payDate: undefined,
          payType: undefined,
          projectId: undefined,
          receivingUnit: undefined,
          remark: undefined,
          sender: undefined,
        },
        open: false,
        rules: {
          invoiceNo: [{ required: true, message: '发票号不能为空', trigger: 'blur' }],
          payAmount: [{ required: true, message: '支付金额不能为空', trigger: 'blur' }],
          payDate: [{ required: true, message: '支付日期不能为空', trigger: 'blur,change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handleFormPayment(projectId, row) {
        this.open = true
        this.formTitle = '新增'
        this.form.projectId = projectId
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getBudgetPaymentById({ projectPayId: row?.projectPayId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
            }
            this.modalLoading = false
          })
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.form?.projectPayId == null) {
              addBudgetPayment(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editBudgetPayment(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped></style>
