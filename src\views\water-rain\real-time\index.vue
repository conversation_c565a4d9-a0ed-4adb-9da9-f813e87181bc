<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="站点类型">
          <a-tree-select
            v-model="queryParam.siteCategoryId"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :tree-data="sideTreeData"
            show-search
            treeNodeFilterProp="title"
            allowClear
            placeholder="请选择"
            :replaceFields="{
              children: 'children',
              title: 'name',
              value: 'key',
            }"
            tree-default-expand-all
          ></a-tree-select>
        </a-form-item>

        <a-form-item label="站点名称">
          <a-input
            v-model="queryParam.siteName"
            placeholder="请输入站点名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <template #table>
          <VxeTable
            border="full"
            :row-config="rowConfig"
            ref="vxeTableRef"
            tableTitle="实时水雨情"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="false"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" icon="sort-ascending" @click="handleStartSort" v-if="!isSort">排序</a-button>
              <a-button @click="handleSort" v-if="isSort">完成</a-button>
              <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>

    <RainWaterLineModal v-if="showWaterRainModal" ref="waterRainModalRef" @close="showWaterRainModal = false" />
  </div>
</template>

<script lang="jsx">
  import { getTreeByCode, getRealTimeList, getSideTreeByCode, updateRealTimeSort, setRealTimeTop } from './services'
  import { getDistrictTree } from '@/api/common'
  import TreeGeneral from '@/components/TreeGeneral'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import RainWaterLineModal from './modules/RainWaterLineModal.vue'
  import { dealNumber, getFixedNum } from '@/utils/dealNumber.js'
  import * as XLSX from 'xlsx-js-style'
  import MergeCell from '@/utils/mergeCell'
  import moment from 'moment'

  export default {
    name: 'RealTime',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      RainWaterLineModal,
    },
    data() {
      return {
        exportLoading: false,

        showWaterRainModal: false,

        sideTreeData: [],
        timer: undefined,

        list: [],
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          siteCategoryId: undefined,
          siteName: undefined,
          riverSystemCategoryId: undefined,
          districtCode: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        districtTypes: {},
        isSort: false,
        rowConfig: {
          drag: false,
          keyField: 'siteId',
        },
        columns: [
          { type: 'seq', title: '序号', width: 60, dragSort: true },
          {
            title: '站点',
            field: 'siteName',
            minWidth: 140,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <a-button type='link' onClick={() => this.handleSite(row)}>
                    {row.siteName}
                  </a-button>
                )
              },
            },
          },
          {
            title: '时间',
            field: 'dateTime',
            minWidth: 170,
          },
          {
            title: '镇乡街道',
            field: 'name',
            minWidth: 200,
            showOverflow: 'tooltip',
          },

          {
            title: '降雨量（mm）',
            children: [
              { field: `rain.hourRain`, title: '时雨量', minWidth: 90, maxWidth: 110 },
              { field: `rain.dayRain`, title: '日雨量', minWidth: 90, maxWidth: 110 },
              { field: `rain.yesterdayRain`, title: '昨日雨量', minWidth: 90, maxWidth: 110 },
            ],
          },
          {
            title: '水位信息（m）',
            children: [
              { field: `waterLevel.eightWaterLevel`, title: '8时水位', minWidth: 90, maxWidth: 110 },
              {
                field: `waterLevel.thisWaterLevel`,
                title: '当前水位',
                minWidth: 90,
                maxWidth: 110,
                slots: {
                  default: ({ row, rowIndex }) => {
                    if (
                      row?.waterLevel?.lastWaterLevel == row?.waterLevel?.thisWaterLevel ||
                      (row?.waterLevel?.lastWaterLevel == null && row?.waterLevel?.thisWaterLevel != null)
                    ) {
                      return row.waterLevel.thisWaterLevel
                    } else {
                      return (
                        row?.waterLevel && (
                          <span>
                            {row?.waterLevel?.thisWaterLevel > row.waterLevel.lastWaterLevel && (
                              <span>
                                {row.waterLevel.thisWaterLevel}
                                <SvgIcon style='margin-left: 3px' iconClass='water-rain-rise' />
                              </span>
                            )}
                            {row?.waterLevel?.thisWaterLevel < row.waterLevel.lastWaterLevel && (
                              <span>
                                {row.waterLevel.thisWaterLevel}
                                <SvgIcon style='margin-left: 3px' iconClass='water-rain-fall' />
                              </span>
                            )}
                          </span>
                        )
                      )
                    }
                  },
                },
              },
            ],
          },
          {
            title: '操作',
            field: 'operate',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleTopUp(row)}>置顶</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getSideTreeByCode({labels: '1'}).then(res => {
        this.sideTreeData = res.data
      })
    },
    mounted() {
      this.handleQuery()
      this.timer = setInterval(() => {
        this.handleQuery()
      }, 60 * 1000)
    },
    beforeDestroy() {
      clearInterval(this.timer)
    },
    methods: {
      /** 查询列表 */
      getList(isExport, callback) {
        if (!isExport) {
          this.showFormDrawer = false
          this.loading = true
          this.selectChange({ records: [] })
        }
        getRealTimeList(this.queryParam).then(response => {
          const list = (response.data || []).map(el => {
            return {
              ...el,
              waterLevel: {
                ...el.waterLevel,
                eightWaterLevel: getFixedNum(el.waterLevel?.eightWaterLevel, 2),
                thisWaterLevel: getFixedNum(el.waterLevel?.thisWaterLevel, 2),
              },
              rain: {
                ...el.rain,
                hourRain: getFixedNum(el.rain?.hourRain, 1),
                dayRain: getFixedNum(el.rain?.dayRain, 1),
                yesterdayRain: getFixedNum(el.rain?.yesterdayRain, 1),
              },
            }
          })
          if (isExport) {
            callback(list)
          } else {
            this.list = list
            this.loading = false
          }
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          siteCategoryId: undefined,
          siteName: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.riverSystemId)
        this.names = valObj.records.map(item => item.siteName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 点击站点
      handleSite(row) {
        this.showWaterRainModal = true
        this.$nextTick(() => {
          this.$refs.waterRainModalRef.handleShow(row)
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        this.getList(true, data => {
          this.exportLoading = false

          const { headerValue, headerMerge, bodyValue, deep, dealBorderAndCenter } = new MergeCell(
            this.columns.slice(1, this.columns.length),
            data,
          )
          const header = dealBorderAndCenter(headerValue)
          const body = dealBorderAndCenter(bodyValue)

          const ws = XLSX.utils.json_to_sheet([])
          ws['!merges'] = headerMerge
          ws['!cols'] = Object.keys(bodyValue[0]).map((el, i) => ({ wpx: i < 3 ? 160 : 90 }))

          XLSX.utils.sheet_add_aoa(ws, header, { origin: 'A1' }) // 将js数据转换为工作表
          XLSX.utils.sheet_add_aoa(ws, body, { origin: `A${deep + 2}` }) // 将js数据转换为工作表

          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

          XLSX.writeFile(wb, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}.xlsx`)
        })
      },

      //
      handleStartSort() {
        this.isSort = true
        this.rowConfig.drag = true
      },
      handleSort() {
        const $grid = this.$refs.vxeTableRef.$refs.vxeTableRef.getTableData().fullData
        if ($grid) {
          const sortArr = []
          $grid.forEach((el, i) => {
            sortArr.push({
              realSort: i,
              siteId: el.siteId,
            })
          })
          updateRealTimeSort(sortArr).then(res => {
            this.$message.success('排序成功', 3)
            this.getList()
          })
          this.isSort = false
          this.rowConfig.drag = false
        }
      },
      handleTopUp(row) {
        setRealTimeTop({ isTop: 1, siteId: row.siteId }).then(res => {
          this.$message.success('置顶成功', 3)
          this.getList()
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
