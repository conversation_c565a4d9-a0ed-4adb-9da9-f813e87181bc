<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="400"
    @cancel="cancel"
    modalHeight="240"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="审核" prop="status">
              <a-radio-group v-model="form.status" style="width: 100%">
                <a-radio :value="1">通过</a-radio>
                <a-radio :value="2">不通过</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="备注" prop="remark">
              <a-textarea
                v-model="form.remark"
                placeholder="请输入"
                allow-clear
                :auto-size="{ minRows: 4, maxRows: 6 }"
              />
            </a-form-model-item>
          </a-col> -->
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { checkPropertyMgr } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: [],
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          id: null,
          status: 1,
          remark: undefined,
        },
        open: false,
        rules: {
          status: { required: true, message: '请选择审核结果', trigger: 'change' },
        },
      }
    },
    created() {
      // this.loading = false
    },
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handle(row) {
        this.open = true
        this.formTitle = '审核'

        this.form.id = row.id
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            checkPropertyMgr(this.form)
              .then(res => {
                if (res.code == 200) {
                  this.$message.success('审核完成', 3)
                  this.open = false
                  this.loading = false
                  this.$emit('ok')
                }
              })
              .catch(() => (this.loading = false))
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped></style>
