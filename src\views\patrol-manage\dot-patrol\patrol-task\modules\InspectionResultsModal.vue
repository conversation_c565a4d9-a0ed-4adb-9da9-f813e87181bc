<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="931"
    modalHeight="602"
    @cancel="cancel"
    ref="lineParamSetModalRef"
  >
    <div slot="content" class="result-container">
      <div class="header">
        <p class="tabs">
          <span class="tab" :class="{ 'tab-active': currentTab == 1 }" @click="handleTab(1)">基本信息</span>
          <span class="tab" :class="{ 'tab-active': currentTab == 2 }" @click="handleTab(2)">巡检结果</span>
        </p>
        <div class="right-count-wrapper" v-if="currentTab == 2">
          <a-button
            type="primary"
            :disabled="taskInfo[0]?.taskStatus != 3"
            class="download"
            icon="download"
            :loading="exportLoading"
            @click="handleExport"
          >
            导出
          </a-button>
          <img
            class="status-img"
            v-if="basicInformation?.taskStatus == 2"
            src="@/assets/images/during-inspection.png"
          />
          <img class="status-img" v-else-if="basicInformation?.taskStatus == 3" src="@/assets/images/completed.png" />
          <span class="count-body">
            <span class="text">总数</span>
            <span class="count">{{ basicInformation?.objectTotalCount }}</span>
            <i class="line"></i>
            <span class="text">未检</span>
            <span class="count no-detection">
              {{ basicInformation?.objectTotalCount - basicInformation?.checkedCount }}
            </span>
            <i class="line"></i>
            <span class="text">完成</span>
            <span class="count finish">{{ basicInformation?.checkedCount }}</span>
            <i class="line"></i>
            <span class="text">异常</span>
            <span class="count abnormal">{{ basicInformation?.abnormalCount }}</span>
            <i class="line"></i>
            <span class="text">漏检</span>
            <span class="count missed-detection">{{ basicInformation?.incompleteCount }}</span>
          </span>
        </div>
      </div>

      <div class="info-result-wrapper" v-if="currentTab == 1">
        <img
          class="info-result-status"
          v-if="basicInformation?.taskStatus == 2"
          src="@/assets/images/during-inspection.png"
        />
        <img
          class="info-result-status"
          v-else-if="basicInformation?.taskStatus == 3"
          src="@/assets/images/completed.png"
        />
        <p class="info-result-row">
          <label class="label">任务类型</label>
          <span class="value" :title="basicInformation?.patrolType == 1 ? '点位巡检' : '路线巡检'">
            {{ basicInformation?.patrolType == 1 ? '点位巡检' : '路线巡检' }}
          </span>
        </p>
        <p class="info-result-row">
          <label class="label">任务名称</label>
          <span class="value" :title="basicInformation?.taskName">{{ basicInformation?.taskName }}</span>
        </p>
        <p class="info-result-row">
          <label class="label">巡检路线</label>
          <span class="value" :title="basicInformation?.lineName">{{ basicInformation?.lineName }}</span>
        </p>
        <p class="info-result-row">
          <label class="label">巡检班次</label>
          <span class="value">{{ basicInformation?.shiftName }}</span>
        </p>
        <p class="info-result-row">
          <label class="label">巡检班组</label>
          <span class="value">{{ basicInformation?.groupName }}</span>
        </p>
        <p class="info-result-row">
          <label class="label">巡检人</label>
          <span class="value" :title="basicInformation?.patrolUserName">
            {{ basicInformation?.patrolUserName }}
          </span>
        </p>
        <p class="info-result-row">
          <label class="label">实际开始时间</label>
          <span class="value">{{ basicInformation?.taskStartTime }}</span>
        </p>
        <p class="info-result-row">
          <label class="label">实际结束时间</label>
          <span class="value">{{ basicInformation?.taskEndTime || '-' }}</span>
        </p>
        <p class="info-result-row">
          <label class="label">任务耗时</label>
          <span class="value">{{ basicInformation?.patrolDuration }}</span>
        </p>
        <p class="info-result-row">
          <label class="label">巡检轨迹长度</label>
          <span class="value">
            {{ basicInformation?.patrolMileage === null ? '-' : basicInformation?.patrolMileage }} km
          </span>
          <a
            class="track-playback"
            v-if="basicInformation?.patrolType == 2 && basicInformation?.taskStatus == 3"
            @click="toTrackPlayback"
          >
            轨迹回放
          </a>
        </p>
      </div>
      <div class="result-look-at" v-else-if="currentTab == 2">
        <div class="info-result-wrapper inspection-results">
          <div v-for="item in inspectionObjects" :key="item.id">
            <p class="info-result-row result-name" :title="item.objectName">{{ item.objectName }}</p>
            <p
              class="info-result-row"
              v-for="child in item?.children"
              @click="onObjectItemClick(child)"
              :class="{ 'row-active': selectedRow == child.objectId }"
            >
              <label class="label">{{ child.objectCode }}</label>
              <span class="value" :title="child.objectName">
                {{ child.objectName }}
                <SvgIcon v-if="child.isAbnormal == 1" slot="square" iconClass="icon-error" class="object-status-icon" />
                <SvgIcon
                  v-if="child.isIncomplete == 1"
                  slot="square"
                  iconClass="icon-lack"
                  class="object-status-icon"
                />
                <SvgIcon
                  v-if="child.isChecked == 1 && child.isIncomplete == 0 && child.isAbnormal == 0"
                  slot="square"
                  iconClass="icon-finish"
                  class="object-status-icon"
                />
              </span>
            </p>
          </div>
        </div>
        <div class="inspection-table">
          <div class="info-result-wrapper">
            <a-table :dataSource="inspectionDetails" rowKey="itemId" :columns="columns" :pagination="false" />
          </div>
          <div class="img-group">
            <p class="img-title">现场照片</p>

            <UploadFile
              :key="imgKey"
              style="margin-top: 3px; height: 46px"
              :fileUrl="inspectionAttaches.map(el => el.attachUrl)"
              :multiple="true"
              listType="picture-card"
              :onlyView="true"
              disabled
            />
            <span class="img-empty" v-if="inspectionAttaches.length == 0">暂无照片</span>
          </div>
        </div>
      </div>
    </div>

    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import { getPatrolTaskProfile, getMyTaskObjectList, getTaskContentDetails, exportById } from '../services'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'InspectionResultsModal',
    components: {
      AntModal,
      UploadFile,
    },
    data() {
      return {
        taskInfo: [],
        exportLoading: false,
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '',
        currentTab: 2,
        imgKey: 1,

        electedTaskId: null,
        selectedTaskCode: null,
        selectedObjectName: '',
        bigSelectedAttachUrl: '',
        selectedRow: null,
        showPreview: false,
        basicInformation: {},
        inspectionObjects: [],
        inspectionDetails: [],
        inspectionAttaches: [],
        itemStatusOptions: [
          { key: 1, value: '未检' },
          { key: 2, value: '正常' },
          { key: 3, value: '异常' },
          { key: 4, value: '漏检' },
        ],

        columns: [
          {
            title: '巡检项',
            dataIndex: 'itemName',
            key: 'itemName',
          },
          {
            title: '巡检状态',
            //巡检项状态(1-未检 2-正常 3-异常 4-漏检)
            dataIndex: 'itemStatus',
            key: 'itemStatus',
            scopedSlots: { customRender: 'itemStatus' },
            customRender: (_, record, i) => {
              if (record.itemStatus == 1) {
                return <span class='no-detection'>未检</span>
              } else if (record.itemStatus == 2) {
                return <span class='finish'>正常</span>
              } else if (record.itemStatus == 3) {
                return <span class='abnormal'>异常</span>
              } else if (record.itemStatus == 4) {
                return <span class='missed-detection'>漏检</span>
              }
            },
          },
          {
            title: '巡检结果',
            dataIndex: 'itemValue',
            key: 'itemValue',
          },
        ],
      }
    },
    methods: {
      handleResults(record) {
        this.taskInfo = [record]
        this.modalTitle = '巡检结果'
        this.open = true
        this.electedTaskId = record.taskId
        this.selectedTaskCode = record.taskCode
        this.getInto(record.taskId)
      },
      handleTab(num) {
        this.currentTab = num
      },
      getInto(taskId) {
        getPatrolTaskProfile(taskId).then(res => {
          this.basicInformation = res?.data
        })
        getMyTaskObjectList(taskId).then(res => {
          this.inspectionObjects = res?.data || []
          this.onObjectItemClick(res?.data[0]?.children[0])
          this.selectedRow = res?.data[0]?.children[0]?.objectId
        })
      },
      //
      onObjectItemClick(child) {
        this.selectedObjectName = child.objectName
        this.selectedRow = child.objectId
        this.itemStatus = getFlatTreeMap(this.itemStatusOptions, 'key')
        getTaskContentDetails({ objectId: child.objectId, taskId: this.electedTaskId }).then(res => {
          this.inspectionDetails = res?.data?.items || []
          this.inspectionAttaches = res?.data?.attaches || []
          this.imgKey += 1
        })
      },
      handleImgPreview(imgUrl) {
        this.bigSelectedAttachUrl = imgUrl
        this.showPreview = true
      },
      closePreview() {
        this.showPreview = false
      },
      // 导出
      handleExport() {
        this.exportLoading = true
        const currentData = this.taskInfo
        if (currentData) {
          const query = {
            taskId: currentData[0].taskId,
          }
          exportById(query).then(res => {
            this.exportLoading = false
            const link = document.createElement('a')
            link.href = res.data
            // 设置下载的文件名
            // link.download = "word.docx";
            // 模拟点击<a>标签进行下载
            link.click()
          })
        }
      },
      //轨迹回放
      toTrackPlayback() {
        let str = window?.location?.pathname
        let index = str.lastIndexOf('/')
        let result = str.slice(0, index)
        const routeUrl = this.$router.resolve({
          path: result + '/track-replay',
          query: { taskCode: this.selectedTaskCode },
        })
        window.open(routeUrl.href, '_blank')
      },
      cancel() {
        this.open = false
        this.$emit('close')
      },
    },
  }
</script>

<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';

  ::v-deep .ant-modal-body {
    padding: 15px 20px;
  }
  .result-container {
    width: 100%;
    height: 100%;
    background: #fff;
    position: relative;
    .big-img-wrapper {
      position: absolute;
      top: 0;
      left: 0;
      width: 891px;
      height: 476px;
      z-index: 999;
      background: #fff;
      .big-img-head {
        display: flex;
        .anticon-close {
          margin-left: auto;
        }
      }
      .big-attach-url {
        width: 891px;
        height: 433px;
        border-radius: 10px;
      }
    }

    .header {
      width: 100%;
      display: flex;
      .tabs {
        display: inline-flex;
        .tab {
          width: 88px;
          height: 32px;
          cursor: pointer;
          text-align: center;
          line-height: 32px;
          margin-right: 10px;
          background: #f2f3f5;
          border-radius: 2px;
          display: inline-block;
        }
        .tab-active {
          color: #fff;
          background: #165dff;
        }
      }
      .right-count-wrapper {
        margin-left: auto;
        .download {
          // width: 78px;
          margin-right: 10px;
        }
        .status-img {
          width: 54px;
          height: 54px;
        }
        .count-body {
          color: #4e5969;
          font-size: 14px;
          .line {
            width: 1px;
            height: 12px;
            display: inline-block;
            margin: 0 8px;
            background: #e5e6eb;
          }
          .text,
          .count {
            margin-left: 3px;
            color: #4e5969;
          }
        }
      }
    }
    .info-result-wrapper {
      width: 100%;
      overflow-x: auto;
      border-radius: 10px;
      padding: 0 10px;
      position: relative;
      border: 1px solid #f2f3f5;
      .info-result-status {
        width: 100px;
        height: 100px;
        position: absolute;
        top: 6px;
        right: 6px;
      }
      .info-result-row {
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding: 0 15px;
        margin-bottom: 0;
        border-bottom: 1px solid #f2f3f5;
        display: inline-flex;
        .label {
          width: 120px;
          color: #4e5969;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .value {
          color: #1d2129;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .track-playback {
          margin-left: 20px;
          color: #165dff;
        }
        &.last-child {
          border-bottom: none;
        }
      }
      .result-name {
        color: #86909c;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .result-look-at {
      display: flex;
      height: 419px;
      .info-result-row {
        cursor: pointer;
        .label {
          width: 90px;
          display: inline-block;
        }
        &:first-child {
          cursor: default;
          background: #f2f3f5;
        }
        .value {
          flex: 1;
        }
        &.row-active {
          background: rgba(59, 90, 252, 0.1);
        }
      }
      .inspection-results {
        width: 307px;
        padding: 0;
      }
      .object-status-icon {
        width: 16px;
        height: 16px;
        margin: 12px 0 0 3px;
      }
      .inspection-table {
        width: 568px;

        margin-left: auto;
        .info-result-wrapper {
          padding: 0;
        }
        ::v-deep .ant-table-wrapper {
          margin: 0;
        }
        ::v-deep .ant-table-default .ant-table-thead > tr > th,
        ::v-deep .ant-table-default .ant-table-tbody > tr > td {
          padding: 10.6px 6px;
        }
        ::v-deep .ant-table-default .ant-table-thead > tr > th {
          background: #f2f3f5;
        }
      }
      .img-group {
        .img-title {
          margin: 15px 0 5px 0;
          color: #4e5969;
        }
        .attach-url {
          width: 173px;
          height: 131px;
          margin-right: 10px;
        }
        .img-empty {
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }
    .no-detection {
      color: #86909c !important;
    }
    .finish {
      color: #00b42a !important;
    }
    .abnormal {
      color: #f53f3f !important;
    }
    .missed-detection {
      color: #ff7d00 !important;
    }
  }
</style>
