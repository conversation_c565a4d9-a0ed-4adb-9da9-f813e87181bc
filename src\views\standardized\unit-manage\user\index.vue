<template>
  <div class="tree-table-page">
    <div class="tree-table-tree-panel">
      <!-- v-if="treeOptions.dataSource.length" -->
      <TreeGeneral
        style="width: 220px"
        ref="treeGeneralRef"
        :treeOptions="treeOptions"
        @onTreeMounted="onTreeMounted"
        @select="clickTreeNode"
      />
    </div>
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="姓名">
          <a-input
            v-model="queryParam.name"
            placeholder="请输入姓名"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="关键字">
          <a-input
            v-model="queryParam.keywords"
            placeholder="请输入用户名/手机号码"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="状态">
          <a-select placeholder="请选择状态" v-model="queryParam.isDisabled" style="width: 100%" allow-clear>
            <a-select-option v-for="(d, index) in statusOptions" :key="index" :value="d.key">
              {{ d.value }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="身份证号">
          <a-input
            v-model="queryParam.idNo"
            placeholder="请输入身份证号"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <!-- <span class="table-page-search-submitButtons" style="float: right">
                    <a-button type="primary" @click="handleQuery">
                      <a-icon type="search" />
                      查询
                    </a-button>
                    <a-button style="margin-left: 8px" @click="resetQuery">
                      <a-icon type="redo" />
                      重置
                    </a-button>
                    <a @click="toggleAdvanced" style="margin-left: 8px">
                      {{ advanced ? '收起' : '展开' }}
                      <a-icon :type="advanced ? 'up' : 'down'" />
                    </a>
                  </span> -->
        <template #table>
          <!-- :otherHeight="30" -->
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @selectChange="selectChange"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">
                导出
              </a-button>
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
      <!-- 增加修改 -->
      <sys-user-add-form
        v-if="showAddModal"
        ref="sysUserAddForm"
        :statusOptions="statusOptions"
        :sexOptions="sexOptions"
        :educationOptions="educationOptions"
        :workExperienceOptions="workExperienceOptions"
        :deptOptions="deptOptions"
        :roleOptions="roleOptions"
        :postOptions="postOptions"
        :defalutExpandedKeys="expandedKeys"
        :deptCheckedValue="deptCheckedValue"
        @select-tree="getTreeselect"
        @ok="getList"
        @close="showAddModal = false"
      />
      <!-- 编辑用户,单独封装了组件 -->
      <sys-user-edit-form
        v-if="showEditModal"
        ref="sysUserEditForm"
        :statusOptions="statusOptions"
        :sexOptions="sexOptions"
        :educationOptions="educationOptions"
        :workExperienceOptions="workExperienceOptions"
        :deptOptions="deptOptions"
        :roleOptions="roleOptions"
        :postOptions="postOptions"
        :defalutExpandedKeys="expandedKeys"
        @select-tree="getTreeselect"
        @ok="getList"
        @close="showEditModal = false"
      />
      <!-- 修改密码抽屉 -->
      <reset-password v-if="showResetPassword" ref="resetPassword" @close="showResetPassword = false" />
      <!-- 修改头像 showUpdateAvatar -->
      <UpdateAvatar v-if="showUpdateAvatar" ref="updateAvatar" @close="showUpdateAvatar = false" />
      <select-user selectModel="multi" v-model="selectedUser" v-show="false" ref="selectUserRef" />
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import {
    listUser,
    delUser,
    exportUser,
    userStatus,
    resetRandomPwd,
    bindDingUser,
    unbindDingUser,
    listRole,
    listPost,
    deptTree,
    getDeptTree,
  } from './services.js'
  import ResetPassword from './modules/ResetPassword'
  import UpdateAvatar from './modules/UpdateAvatar'
  import SysUserAddForm from './modules/SysUserAddForm'
  import SysUserEditForm from './modules/SysUserEditForm'
  import DeptTree from './modules/DeptTree'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import Split from '@/components/pt/split/Index'
  import SelectUser from '@/components/pt/selectUser/SelectDingUser'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'
  import TreeGeneral from '@/components/TreeGeneral'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'User',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      ResetPassword,
      UpdateAvatar,
      SysUserEditForm,
      AdvanceTable,
      SysUserAddForm,
      Split,
      DeptTree,
      SelectUser,
    },
    data() {
      return {
        treeOptions: {
          getDataApi: getDeptTree,
          // dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'deptName',
            key: 'id',
            value: 'deptId',
          },
        },
        isChecked: false,
        exportLoading: false,
        showAddModal: false,
        showEditModal: false,
        showResetPassword: false,
        showUpdateAvatar: false,

        list: [],
        colorList: ['#F38709', '#813AFD', '#00C4AA', '#4B7AEE'],
        sexOptions: [],
        educationOptions: [],
        workExperienceOptions: [],
        tableTitle: '用户管理',
        selectedRowKeys: [],
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        ids: [],
        names: [],
        userNames: [],
        expandedKeys: [],
        selectedUser: '',
        loading: false,

        total: 0,
        // 状态数据字典
        statusOptions: [],
        deptCheckedValue: '',
        // 部门树选项
        deptOptions: [],
        roleOptions: [], //角色下拉选项
        postOptions: [], //岗位下拉选项
        bindUserId: '',
        // 日期范围
        dateRange: [],
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        queryParam: {
          sort: [],
          pageNum: 1,
          deptId: 0,
          idNo: '',
          isDisabled: '',
          keywords: '',
          name: '',
          pageSize: 10,
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          {
            title: '姓名',
            field: 'name',
            width: 110,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <div>
                    {/* <a-avatar shape='square'
                      style={{ color: '#fff', marginRight: '5px', backgroundColor: this.getRandomColor(rowIndex) }}
                    >
                      {this.getShowName(row.name)}
                    </a-avatar> */}
                    {row.name}
                  </div>
                )
              },
            },
            fixed: 'left',
          },
          {
            title: '用户名',
            field: 'username',
            width: 100,
            fixed: 'left',
          },
          {
            title: '手机号',
            field: 'mobile',
            minWidth: 120,
          },
          {
            title: '邮箱',
            field: 'email',
            width: 160,
          },
          {
            title: '性别',
            field: 'sex',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.sexOptions.find(entry => entry.dictKey == row.sex)?.dictValue
              },
            },
            width: 60,
          },
          {
            title: '部门',
            field: 'deptName',
            width: 200,
            maxWidth: 220,
            showOverflow: 'tooltip',
          },
          {
            title: '角色',
            field: 'roleNames',
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return row.roleNames ? (
                  row.roleNames.includes(',') ? (
                    row.roleNames.split(',').map(item => <a-tag color='blue'>{item}</a-tag>)
                  ) : (
                    <div>
                      <a-tag color='blue'>{row.roleNames}</a-tag>
                    </div>
                  )
                ) : (
                  <div></div>
                )
              },
            },
            width: 270,
          },
          {
            title: '岗位',
            field: 'postNames',
            slots: {
              default: ({ row, rowIndex }) => {
                return row.postNames ? (
                  row.postNames.includes(',') ? (
                    row.postNames.split(',').map(item => <a-tag color='blue'>{item}</a-tag>)
                  ) : (
                    <div>
                      <a-tag color='blue'>{row.postNames}</a-tag>
                    </div>
                  )
                ) : (
                  <div></div>
                )
              },
            },
            width: 270,
          },
          {
            title: '学历',
            field: 'education',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.educationOptions.find(entry => entry.dictKey == row.education)?.dictValue
              },
            },
            width: 80,
          },
          {
            title: '工作经验',
            field: 'workExperience',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.workExperienceOptions.find(entry => entry.dictKey == row.workExperience)
                  ?.dictValue
              },
            },
            width: 100,
          },
          {
            title: '备注',
            field: 'remark',
            width: 120,
          },
          {
            title: '身份证号',
            field: 'idNo',
            minWidth: 180,
          },
          {
            title: '最后登录IP',
            field: 'loginIp',
            width: 130,
          },
          {
            title: '最后登录时间',
            field: 'loginTime',
            width: 170,
          },
          {
            title: '创建时间',
            field: 'createdTime',
            width: 170,
          },
          // {
          //   title: '钉钉',
          //   dataIndex: 'ddUserId',
          //   scopedSlots: { customRender: 'ddUserId' },
          //   width: 80,
          //   fixed: 'right'
          // },
          {
            title: '用户状态',
            field: 'isDisabled',
            width: 80,
            fixed: 'right',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <a-switch
                    style={{ backgroundColor: row.isDisabled ? '#52c41a' : '#fc011a' }}
                    size='small'
                    checked-children='正常'
                    un-checked-children='停用'
                    v-model={row.isDisabled}
                    onClick={() => this.onChangeStatus(row)}
                  ></a-switch>
                )
              },
            },
          },
          {
            title: '操作',
            field: 'operation',
            width: 130,
            align: 'left',
            fixed: 'right',
            slots: {
              default: ({ row, rowIndex }) => {
                let nowDate = new Date()
                let endDate = new Date(row.planEndDate)
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row, undefined)}>修改</a>

                    <a-divider type='vertical' />

                    <a-dropdown>
                      <a>
                        更多
                        <a-icon type='down' />
                      </a>
                      <a-menu slot='overlay'>
                        <a-menu-item>
                          <a onClick={() => this.handleDelete(row)}>删除</a>
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item>
                          <a onClick={() => this.handleUpdateAvatar(row)}>修改头像</a>
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item>
                          <a onClick={() => this.handleResetPwd(row)}>重置密码</a>
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item>
                          <a onClick={() => this.handleRandomPwd(row)}>随机密码</a>
                        </a-menu-item>
                      </a-menu>
                    </a-dropdown>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    filters: {},
    created() {
      // getDeptTree().then(response => {
      //   this.treeOptions.dataSource = this.changeIconState(response.data)
      // })
      this.getList()
      this.getTreeselect()
      getOptions('isDisabled').then(response => {
        this.statusOptions = response.data
      })
      getOptions('sex').then(response => {
        this.sexOptions = response.data
      })
      getOptions('education').then(response => {
        this.educationOptions = response.data
      })
      getOptions('workExperience').then(response => {
        this.workExperienceOptions = response.data
      })
      // this.sexOptions = this.GLOBAL.SEX
    },
    computed: {},
    watch: {
      multiple: {
        deep: true,
        handler: function (newValue, oldValue) {
          // console.log('multiple changed:', this.multiple, newValue, oldValue)
        },
      },
      selectedUser(val) {
        let ddUnionId = val.unionIds
        let ddUserId = val.userIds
        if (!ddUnionId) {
          return
        } else if (!ddUserId) {
          return
        }
        bindDingUser(this.bindUserId, ddUnionId, ddUserId).then(response => {
          this.getList()
          this.$message.success('当前用户钉钉绑定成功', 3)
        })
      },
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = !pageSize ? 9 : pageSize
        this.getList()
      },
      // 树加载完成后
      onTreeMounted(data) {
        this.tableTitle = data[0].name
        this.queryParam.deptId = data[0].key //data[0].key.substr(1, data[0].key.length)
        this.getList()
      },
      clickTreeNode(node) {
        const key = node.$options.propsData.dataRef.key
        this.queryParam.deptId = key //key.substr(1, key.length)
        this.tableTitle = node.$options.propsData.dataRef.title

        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 查询部门下拉树结构 */
      getTreeselect() {
        deptTree('0').then(response => {
          this.deptOptions = this.changeIconState(response.data)
        })
        listRole().then(response => {
          this.roleOptions = response.data
        })
        listPost().then(response => {
          this.postOptions = response.data
        })
      },
      changeIconState(data) {
        for (let i in data) {
          data[i].slots = {
            icon: data[i].type == 0 ? 'org' : data[i].type == 1 ? 'company' : data[i].type == 2 ? 'dept' : '',
          }
          if (data[i].children) {
            this.changeIconState(data[i].children)
          }
        }
        return data
      },
      getExpandedKeys(nodes, expandLevel) {
        if (expandLevel > 1) {
          // 最后一层不展开
          nodes.forEach(node => {
            this.expandedKeys.push(node.id)
            expandLevel = expandLevel - 1
            return this.getExpandedKeys(node.children, expandLevel)
          })
        }
      },
      statusFormat(row) {
        return this.selectDictLabel(this.statusOptions, row.status)
      },
      /** 查询定时任务列表 */
      getList() {
        this.showAddModal = false
        this.showEditModal = false
        this.loading = true
        this.expandedKeys.push(0)
        listUser(this.queryParam).then(response => {
          this.list = response.data.data?.map(item => ({
            ...item,
            isDisabled: item.isDisabled === 0 ? true : false,
          }))
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.queryParam = {
          ...this.queryParam,
          pageNum: 1,
          deptId: 0,
          idNo: '',
          isDisabled: '',
          keywords: '',
          name: '',
          sort: [],
        }
        this.handleQuery()
      },

      //分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.userId)
        this.names = valObj.records.map(item => item.name)
        this.isChecked = !!valObj.records.length
      },
      toggleAdvanced() {
        this.advanced = !this.advanced
      },
      clickDeptNode(node) {
        this.queryParam.deptId = node.$options.propsData.eventKey
        this.deptCheckedValue = node.$options.propsData.eventKey
        this.tableTitle = node.$options.propsData.label
        this.getList()
      },
      sexFormat(row) {
        return this.selectDictLabel(this.sexOptions, row.sex)
      },
      /* 新增用户信息 */
      handleAdd() {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.sysUserAddForm.handleAdd())
      },
      /* 修改用户信息 */
      handleUpdate(record, ids) {
        this.showEditModal = true
        this.$nextTick(() => this.$refs.sysUserEditForm.handleUpdate(record, ids))
      },
      // 导出
      handleExport() {
        this.exportLoading = true
        listUser({ ...this.queryParam, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
          this.exportLoading = false

          const columnsList = [
            {
              title: '姓名',
              field: 'name',
              minWidth: 120,
            },
            {
              title: '用户名',
              field: 'username',
              minWidth: 130,
            },
            {
              title: '手机号',
              field: 'mobile',
              minWidth: 100,
            },
            {
              title: '邮箱',
              field: 'email',
              minWidth: 140,
            },
            {
              title: '性别',
              field: 'sex',
              minWidth: 60,
            },
            {
              title: '部门',
              field: 'deptName',
              minWidth: 140,
            },
            // {
            //   title: '角色',
            //   field: 'roleNames',
            //   minWidth: 140,
            // },
            {
              title: '岗位',
              field: 'postNames',
              minWidth: 140,
            },
            {
              title: '备注',
              field: 'remark',
              minWidth: 140,
            },
            {
              title: '身份证号',
              field: 'idNo',
              minWidth: 140,
            },
            {
              title: '职称',
              field: 'title',
              minWidth: 140,
            },
            {
              title: '生日',
              field: 'birthday',
              minWidth: 80,
            },
            {
              title: '创建时间',
              field: 'createdTime',
              minWidth: 130,
            },
          ]

          const data = (res.data?.data || []).map(el => ({
            ...el,
            sex: this.sexOptions.find(entry => entry.dictKey == el.sex)?.dictValue,
          }))
          excelExport(columnsList, data, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
      /* 用户密码重置 */
      handleResetPwd(record) {
        this.showResetPassword = true
        this.$nextTick(() => this.$refs.resetPassword.handleResetPwd(record))
      },
      handleUpdateAvatar(record) {
        this.showUpdateAvatar = true
        this.$nextTick(() => this.$refs.updateAvatar.handleUpdateAvatar(record))
      },
      handleRandomPwd(row) {
        var that = this
        const userIds = row.userId || this.ids
        const userNames = row.name || this.userNames

        this.$confirm({
          title: '确认重置所选中密码数据?',
          content: '当前选中名称为"' + userNames + '"的数据',
          onOk() {
            return resetRandomPwd(userIds).then(() => {
              that.getList()
              that.$message.success('重置成功', 3)
            })
          },
          onCancel() {},
        })
      },
      /** 更新用户钉钉绑定状态 (0-绑定 1-解绑)**/
      onChangeDDStatus(e, record) {
        var that = this
        const userId = record.userId
        const username = record.name
        that.bindUserId = record.userId
        let recordIsDisabled = record.ddUserId || record.ddUnionId ? 1 : 2
        let isDisabledName = recordIsDisabled == 1 ? '解绑' : '绑定'

        if (recordIsDisabled == 1) {
          this.$confirm({
            title: '是否"' + isDisabledName + '"所选中数据?',
            content: '当前选中的数据:' + username,
            onOk() {
              return unbindDingUser(userId).then(res => {
                that.getList()
                that.$message.success('当前用户钉钉解绑成功！', 5)
              })
            },
            onCancel() {},
          })
        } else if (recordIsDisabled == 2) {
          this.$nextTick(() => this.$refs.selectUserRef.showSelectUser())
        }
      },
      /** 更新菜单状态 (0-正常 1-停用)**/
      onChangeStatus(record) {
        var that = this
        const userId = record.userId
        let isUserDisabled = 0
        let recordIsDisabled = !record.isDisabled
        isUserDisabled = recordIsDisabled ? 1 : 0
        let isDisabledName = isUserDisabled == 1 ? '停用' : '启用'
        this.$confirm({
          title: '是否"' + isDisabledName + '"所选中数据?',
          content: '当前选中的数据',
          onOk() {
            return userStatus(isUserDisabled, userId).then(res => {
              if (res.code == 200 && res.success == true) {
                that.getList()
              } else if (res.code != 200) {
                that.$message.success('"' + isDisabledName + '"失败:', res.message)
              } else {
              }
            })
          },
          onCancel() {
            that.getList()
          },
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const userIds = row.userId || this.ids
        const userNames = row.name || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + userNames + '"的数据',
          onOk() {
            return delUser(userIds).then(() => {
              that.selectedRowKeys = []
              that.multiple = true
              that.$message.success('删除成功', 3)
              that.getList()
            })
          },
          onCancel() {},
        })
      },

      getRandomColor(index) {
        return this.colorList[index % 4]
      },
      getShowName(name) {
        if (name.length > 2) {
          name = name.substring(name.length - 2)
        }
        return name
      },
      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },
      setDataOptionInfo(treeDataOption) {
        this.deptOptions = treeDataOption
      },
      // handleTableChange(pagination, filters, sorter) {
      //   this.getList()
      // },
      handleTableChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped src="./custom.less"></style>
