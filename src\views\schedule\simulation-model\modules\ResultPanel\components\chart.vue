<template>
  <BaseEchart :option="option" width="100%" height="190px" />
</template>

<script>
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import * as echarts from 'echarts/core'
  //hex -> rgba
  function hexToRgba(hex, opacity) {
    return (
      'rgba(' +
      parseInt('0x' + hex.slice(1, 3)) +
      ',' +
      parseInt('0x' + hex.slice(3, 5)) +
      ',' +
      parseInt('0x' + hex.slice(5, 7)) +
      ',' +
      opacity +
      ')'
    )
  }

  export default {
    name: 'Chart',
    components: { BaseEchart },
    props: ['dataSource'],
    data() {
      return {
        option: null,
      }
    },
    watch: {
      dataSource: {
        handler(newVal, oldVal) {
          this.option = {
            title: {
              top: 10,
              left: 10,
              textAlign: 'left',
              textStyle: {
                color: '#000',
                fontSize: 14,
                fontWeight: 400,
              },
            },
            grid: {
              left: '4%',
              right: '4%',
              bottom: 20,
              top: 40,
              containLabel: true,
            },
            tooltip: {
              appendToBody: true,
              confine: true,
              position: (pos, params, dom, rect, size) => {
                let obj = { bottom: size.viewSize[1] - pos[1] + 10 }
                obj[['right', 'left'][+(pos[0] < size.viewSize[0] / 2)]] =
                  pos[0] < size.viewSize[0] / 2 ? pos[0] + 10 : size.viewSize[0] - pos[0] + 10
                return obj
              },
              trigger: 'axis',
              // backgroundColor: 'rgba(0,0,0,0.6)', //通过设置rgba调节背景颜色与透明度
              borderWidth: 0,
              textStyle: {
                color: '#000',
              },
              formatter: params => {
                let str = `<div>${this.$attrs[`chart${this.$attrs.type}Stakes`][params[0].dataIndex]}</div>`
                params.forEach(item => {
                  str += `<div>
                      ${item.marker}
                      <span>${item.seriesName}: ${item.value[1]}</span>
                    </div>`
                })
                return str
              },
            },
            xAxis: {
              data: newVal.length ? newVal[0].data.map(i => i[0]) : [],
              name: '',
              nameTextStyle: {
                padding: [0, 0, 0, -5],
                color: '#000',
                fontSize: 12,
                fontWeight: 400,
              },
              axisLabel: {
                textStyle: {
                  color: '#000',
                },
                formatter: value => {
                  return this.$attrs[`chart${this.$attrs.type}Stakes`][+value]
                },
              },
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              z: 10,
            },
            yAxis: [
              {
                name: '水位(m)',
                nameGap: 20,
                axisPointer: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    type: 'dashed',
                    color: '#BBB',
                  },
                },

                axisTick: {
                  show: false,
                },
                axisLabel: {
                  textStyle: {
                    color: '#000',
                  },
                },
                min: this.$attrs.minWL || 0,
                max: this.$attrs.maxWL || 120,
              },
              {
                name: '流量(m³/s)',
                nameGap: 20,
                position: 'right',
                axisPointer: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  textStyle: {
                    color: '#000',
                  },
                },
              },
            ],
            legend: {
              show: false,
            },
            series: newVal.map((item, i) => {
              return {
                type: 'line',
                showBackground: true,
                smooth: true,
                showSymbol: true,
                symbolSize: 2,
                symbol: 'circle',
                name: item.name,
                color: item.color,
                yAxisIndex: item?.yAxisIndex || 0,
                areaStyle: {
                  opacity: item.name === '流量' ? 0 : 1,
                  color: hexToRgba(item.color, 1),
                },
                emphasis: {
                  focus: 'series',
                },
                markLine: {
                  animation: false,
                  lineStyle: {
                    width: 2,
                    color: '#6AA1FF',
                  },
                  label: {
                    show: true,
                    position: 'start',
                    formatter: '狮山湾分水闸',
                    color: '#32383B',
                    padding: [18, 12, 0, 12],
                  },
                  silent: true, // 鼠标悬停事件, true悬停不会出现实线
                  symbol: 'none', // 去掉箭头
                  data:
                    i === 0
                      ? [
                          {
                            xAxis: this.$attrs.markLineXAxis1,
                            lineStyle: { color: '#6AA1FF' },
                            label: {
                              position: 'start',
                              formatter: this.$attrs.type === '1' ? '狮山湾分水闸' : '子良岩村分水闸',
                              color: '#32383B',
                              padding: [18, 12, 0, 12],
                            },
                          },
                          {
                            xAxis: parseInt(this.$attrs.markLineXAxis1 / 2),
                            lineStyle: { color: 'transparent' },
                            label: {
                              position: 'end',
                              formatter: '总干渠',
                              color: '#6AA1FF',
                              padding: [18, 12, 0, 12],
                            },
                          },
                          {
                            xAxis: parseInt(this.$attrs.markLineXAxis1 / 2),
                            lineStyle: { color: 'transparent' },
                            label: {
                              position: 'end',
                              formatter: '总干渠',
                              color: '#6AA1FF',
                              padding: [18, 12, 0, 12],
                            },
                          },
                        ]
                          .concat(
                            this.$attrs.type === '3'
                              ? [
                                  {
                                    xAxis: this.$attrs.markLineXAxis2,
                                    lineStyle: { color: '#6AA1FF' },
                                    label: {
                                      position: 'start',
                                      formatter: '陈家冲节制闸',
                                      color: '#32383B',
                                      padding: [18, 12, 0, 12],
                                    },
                                  },
                                  {
                                    xAxis: parseInt((this.$attrs.markLineXAxis1 + this.$attrs.markLineXAxis2) / 2),
                                    lineStyle: { color: 'transparent' },
                                    label: {
                                      position: 'end',
                                      formatter: '西干渠',
                                      color: '#6AA1FF',
                                      padding: [18, 12, 0, 12],
                                    },
                                  },
                                  {
                                    xAxis: parseInt((this.$attrs.markLineXAxis2 + newVal[0].data.length) / 2),
                                    lineStyle: { color: 'transparent' },
                                    label: {
                                      position: 'end',
                                      formatter: '沾溪干渠',
                                      color: '#6AA1FF',
                                      padding: [18, 12, 0, 12],
                                    },
                                  },
                                ]
                              : [],
                          )
                          .concat(
                            this.$attrs.type === '1' || this.$attrs.type === '2'
                              ? [
                                  {
                                    xAxis: parseInt((this.$attrs.markLineXAxis1 + newVal[0].data.length) / 2),
                                    lineStyle: { color: 'transparent' },
                                    label: {
                                      position: 'end',
                                      formatter: this.$attrs.type === '1' ? '东干渠' : '西干渠',
                                      color: '#6AA1FF',
                                      padding: [18, 12, 0, 12],
                                    },
                                  },
                                ]
                              : [],
                          )
                      : null,
                },
                data: item.data,
              }
            }),
          }
        },
        deep: true,
        immediate: true,
      },
    },
    computed: {},
    created() {},
    methods: {},
  }
</script>

<style lang="less" scoped></style>
