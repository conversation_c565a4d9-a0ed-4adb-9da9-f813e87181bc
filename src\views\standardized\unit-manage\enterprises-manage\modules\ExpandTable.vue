<template>
  <div style="height: 100%">
    <VxeTable
      ref="vxeTableRef"
      :tableKey="tableKey"
      :tableTitle="tableTitle"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="true"
      :rowConfig="{ isHover: false }"
      min-height="82px"
      max-height="280px"
      size="small"
      @refresh="getList"
      @selectChange="selectChange"
    >
      <div class="table-operations" slot="button">
        <a-button type="primary" @click="handleAdd()">
          <a-icon type="plus" />
          新增
        </a-button>
        <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
          <a-icon type="delete" />
          删除
        </a-button>
        <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
      </div>
    </VxeTable>
    <ProjectModal
      v-if="isShowModal"
      :row="row"
      ref="projectModalRef"
      @ok="onOperationComplete"
      @close="isShowModal = false"
    />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import { getProjectList, deleteProject } from '../services'
  // import SolutionModal from './SolutionModal.vue'
  import ProjectModal from './ProjectModal.vue'

  export default {
    name: 'ExpandTable',
    components: {
      VxeTable,
      ProjectModal,
    },
    props: ['row'],
    data() {
      return {
        isShowModal: false,

        tableTitle: ' ',
        tableKey: 1,
        loading: true,
        list: [],
        selectIds: [],
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', align: 'center', width: 50 },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 260,
            showOverflow: 'tooltip',
          },
          {
            title: '工程类型',
            field: 'objectCategoryName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '行政区划',
            field: 'districtName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '时间',
            field: 'addedTime',
            align: 'center',
            minWidth: 170,
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.tableTitle = this.row.deptName
      this.getList()
    },
    mounted() {},
    methods: {
      getList() {
        this.loading = true
        this.selectChange({ records: [] })
        getProjectList({
          deptId: this.row.deptId,
        }).then(response => {
          this.list = response.data
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
      },

      handleAdd() {
        this.isShowModal = true
        this.$nextTick(() => this.$refs.projectModalRef.handle())
      },
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteProject({ ids: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      onOperationComplete() {
        this.isShowModal = false
        this.getList()
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 10px;
    }
  }

  ::v-deep .vxe-table--render-default .vxe-table--body-wrapper {
    height: auto !important;
  }
</style>
