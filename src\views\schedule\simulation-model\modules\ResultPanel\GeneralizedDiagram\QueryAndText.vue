<template>
  <div class="absolute">
    <Query
      :searchType="searchType"
      @change-Type="onChangeType"
      :showDescribe="showDescribe"
      @change-describe="onChangeDescribe"
      @change-reset="onChangeReset"
    />
    <Describe v-if="showDescribe" @closeDescribe="closeDescribe" />

    <div class="left-data">
      <div class="flex top">
        <span>监测数据</span>
        <span class="ml-auto pointer" @click="handleToggleAlert">
          {{ showAlert ? '收起' : '展开' }}
        </span>
      </div>

      <div class="card-box" :style="{ display: showAlert ? 'block' : 'none' }">
        <div class="flex">
          <label>工程名称</label>
          <span class="ml-auto">{{ searchType == 1 ? '水位' : '流量' }}</span>
        </div>
        <div class="flex" v-for="item in projectList" :key="item.projectId">
          <label>{{ item.projectName }}</label>
          <span class="ml-auto">
            {{
              searchType == 1
                ? item.upWlv + 'm'
                : item.type === 0 || item.type === 2
                  ? item.outFlow + 'm³/s'
                  : item.inFlow + 'm³/s'
            }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="jsx">
  import Describe from './Describe.vue'
  import Query from './Query.vue'

  export default {
    name: 'QueryAndText',
    props: ['projectList', 'searchType'],
    components: { Describe, Query },
    data() {
      return {
        showDescribe: false,
        geojson: null,
        curTime: null,
        showAlert: false,
      }
    },
    computed: {},
    created() {},

    methods: {
      onChangeType(val) {
        this.$emit('change-type', val)
      },

      onChangeDescribe() {
        this.showDescribe = !this.showDescribe
      },
      closeDescribe() {
        this.showDescribe = false
      },

      onChangeReset() {
        this.$emit('reset-map')
      },
      handleToggleAlert() {
        this.showAlert = !this.showAlert
      },
    },
  }
</script>

<style lang="less" scoped>
  .absolute {
    position: absolute;
    top: 6px;
    width: 100%;
  }
  .left-data {
    width: 160px;
    position: absolute;
    left: 6px;
    color: #fff;
    border-radius: 6px;
    padding: 6px;
    border: 1px solid #315375;
    z-index: 9999;
    background: linear-gradient(180deg, #36628d 0%, rgba(54, 107, 141, 0) 100%), rgba(20, 71, 105, 0.59);
    .top {
      color: #03ffcd;
    }
    .ml-auto {
      margin-left: auto;
    }
    .flex {
      display: flex;
    }
  }
  .card-box {
    font-size: 13px;
  }
  .pointer {
    cursor: pointer;
  }
</style>
