import request from '@/utils/request'

// 工程管理_维修养护_维养计划-列表分页查询
export function getPropertyMgrPage(data) {
  return request({
    url: '/custom/property-mgr/prj-owner-change/page',
    method: 'post',
    data,
  })
}
// 增加
export function addPropertyMgr(data) {
  return request({
    url: '/custom/property-mgr/prj-owner-change/add',
    method: 'post',
    data,
  })
}
// 详情
export function getPropertyMgrById(params) {
  return request({
    url: '/custom/property-mgr/prj-owner-change/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
// export function editArchives(data) {
//   return request({
//     url: '/custom/archives/update',
//     method: 'post',
//     data,
//   })
// }
// 删除
export function deletePropertyMgr(params) {
  return request({
    url: '/custom/property-mgr/prj-owner-change/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 审核
export function checkPropertyMgr(params) {
  return request({
    url: '/custom/property-mgr/prj-owner-change/check',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}


// 物业单位列表
export function getPropertyMgrList(params) {
  return request({
    url: '/custom/property-mgr/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 单位下的工程列表
export function getProjectList(params) {
  return request({
    url: '/custom/property-mgr/project/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
