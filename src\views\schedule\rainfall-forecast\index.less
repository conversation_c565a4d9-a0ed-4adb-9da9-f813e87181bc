.header {
  display: flex;
  align-items: center;
  padding: 4px 6px;
  // background: #e8f3ff;
  border-radius: 4px;
  border-bottom: 1px solid #f2f3f5;
  .tabs {
    width: 166px;
    height: 24px;
    // background: #f2f3f8;
    display: inline-block;
    margin-left: 10px;
    border-radius: 2px 2px 2px 2px;
    font-size: 16px;
    a {
      width: 80px;
      height: 22px;
      display: inline-block;
      text-align: center;
      color: #4a5568;
      margin: 1px;
    }
    .tab-active {
      // background: #ffffff;
      color: #438efc;
      border-radius: 2px 2px 2px 2px;
    }
  }
}
.flood-tabs {
  display: flex;
  // background: #f2f3f8;
  .name {
    font-size: 14px;
    color: #1d2129;
    color: #4a5568;
    // font-weight: 600;
  }
  .tabs {
    width: 178px;
    height: 24px;
    // background: #f2f3f8;
    // display: inline-block;
    line-height: 24px;
    margin-left: 10px;
    border-radius: 2px 2px 2px 2px;
    display: flex;
    a {
      width: 58px;
      // height: 22px;
      height: 24px;
      display: inline-block;
      text-align: center;
      color: #4a5568;
      // border: 1px solid rgba(74, 85, 104, 0.2);
      border: 1px solid #f2f3f5;
      border-radius: 3px;
      // margin: 1px;
      margin-left: 5px;
    }
    .tab-active {
      // background: #ffffff;
      // color: #438efc;
      background: #438efc;
      color: #ffffff;
      border-radius: 2px 2px 2px 2px;
    }
  }
}

.chart-title {
  font-size: 16px;
  color: #1d2129;
  font-weight: 600;
  margin-bottom: 10px;
  // background: #f2f3f8;
  text-align: center;
}

.forecast-tabs {
  display: flex;
  // background: #f2f3f8;
  .name {
    font-size: 14px;
    color: #1d2129;
    color: #4a5568;
    height: 24px;
    line-height: 24px;
    // font-weight: 600;
  }
  .tabs {
    width: 298px;
    height: 24px;
    line-height: 24px;
    // background: #f2f3f8;
    // display: inline-block;
    margin-left: 10px;

    border-radius: 2px 2px 2px 2px;
    display: flex;
    a {
      width: 92px;
      height: 24px;
      display: inline-block;
      text-align: center;
      color: #4a5568;
      border: 1px solid #f2f3f5;
      border-radius: 3px;
      margin-left: 5px;
    }
    .tab-active {
      background: #438efc;
      color: #ffffff;
      border-radius: 3px;
    }
  }
}
.flood-box {
  width: 100%;
  height: 100%;
  flex: 1;
  padding-top: 10px;
  // border-top: 1px solid #f2f3f5;
  display: flex;
  flex-direction: column;

  .flood-table-box {
    width: 100%;
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    .top {
      width: 100%;
      height: 48px;
      padding: 0 10px;
      line-height: 48px;
      background: rgba(229, 230, 235, 0.5);
      border-radius: 6px;
      margin-bottom: 10px;
      .last-time {
        margin-left: 6px;
      }
      .next-time {
        font-weight: 700;
        font-size: 20px;
        color: #1d2129;
      }
    }
    .remainder-icon {
      width: 22px;
      height: 22px;
      display: inline-block;
      vertical-align: middle;
      margin-left: 20px;
      background: url('@/assets/images/remainder-icon.png') 0 0 no-repeat;
      background-size: 100%;
    }
  }
}

@font-face {
  font-family: 'AlimamaDaoLiTi';
  src: url('@/assets/font/AlimamaDaoLiTi.ttf');
}

.legend {
  width: 130px;
  position: absolute;
  z-index: 99999999;
  left: 12px;
  bottom: 12px;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px 12px;
  border-radius: 8px 8px 8px 8px;
  .legend-item:nth-child(n):not(:last-child) {
    margin-bottom: 6px;
  }
  .legend-item {
    font-size: 13px;
    padding: 0px;
    // display: inline-block;
    display: flex;
  }
}
.progress {
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 80%;
  height: 44px;
}

:deep(.ant-select-selection) {
  width: 140px !important;
}
