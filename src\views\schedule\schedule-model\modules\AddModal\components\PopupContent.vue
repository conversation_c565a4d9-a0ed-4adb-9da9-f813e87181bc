<template>
  <div class="container" @click.stop="item.onClick(item)">
    <div class="header">
      <div class="name">{{ item?.siteName }}</div>
      <!-- <a-icon type="close" style="cursor: pointer" @click="item.onPopupClose(item)" /> -->
    </div>

    <div class="indicator">
      <div class="label">水位:&nbsp;</div>
      <a-input-number
        v-if="isMounted"
        :autoFocus="false"
        size="small"
        :step="0.01"
        :min="0"
        v-model="waterLevel"
        @change="onChangeWlv"
      />
      &nbsp;m
    </div>
  </div>
</template>
<script>
  import * as _ from 'lodash'

  export default {
    name: 'PopupContent',
    props: ['item'],
    data() {
      return {
        waterLevel: this.item.wlv,
        isMounted: false,
      }
    },
    mounted() {
      this.isMounted = true
    },
    methods: {
      onChangeWlv: _.debounce(function (val) {
        if (this.waterLevel === val && this.item.wlv !== val && this.isMounted) {
          this.item.onChangeWlv(this.item, val)
        }
      }, 500),
    },
  }
</script>
<style lang="less">
  .mapboxgl-popup-content {
    padding: 0;
  }
</style>
<style lang="less" scoped>
  .container {
    width: 124px;
    max-height: 106px;
    position: relative;
    display: flex;
    flex-direction: column;
    .header {
      background: #f2f3f5;
      font-weight: 600;
      color: #1d2129;
      padding: 3px 6px;
      display: flex;
      align-items: center;
      .name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .indicator {
      display: flex;
      align-items: center;
      padding: 6px 6px;
      .label {
        white-space: nowrap;
        color: '#4E5969';
      }
    }
  }
  ::v-deep .ant-input-number-handler-wrap {
    display: none;
  }
</style>
