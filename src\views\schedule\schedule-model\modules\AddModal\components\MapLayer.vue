<template></template>

<script setup>
  import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
  import { MapboxOverlay } from '@deck.gl/mapbox'
  import { GeoJsonLayer } from '@deck.gl/layers'
  import { hexToRgb } from '@/utils/toDeckglRgb.js'
  // import { mapBoundGeo } from '@/utils/mapBounds.js'
  import * as turf from '@turf/turf'
  import axios from 'axios'
  import { getValueByKey } from '@/api/common'

  const props = defineProps(['mapIns', 'activeLine', 'dataSource'])
  const emits = defineEmits(['handleClickLine'])
  const stateRef = ref({
    deckOverlay: null,
    geojson: null,
    activeLine: null,
  })

  stateRef.value.deckOverlay = new MapboxOverlay({
    id: 'deck-geojson-layer-overlay',
    layers: [],
  })
  props.mapIns.addControl(stateRef.value.deckOverlay)

  const updateProps = () => {
    const pointGeojson = {
      type: 'FeatureCollection',
      features: [],
    }

    props.dataSource.forEach(el => {
      el.sites
        .filter(ele => +ele.longitude && +ele.latitude)
        .forEach(site => {
          pointGeojson.features.push({
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [+site.longitude, +site.latitude],
            },
            properties: site,
          })
        })
    })

    stateRef.value.deckOverlay.setProps({
      layers: [
        new GeoJsonLayer({
          id: 'geojson-layer-polyline',
          data: stateRef.value.geojson,

          // filled: false,
          pickable: true,

          stroked: true,
          getLineWidth: 4,
          lineWidthMaxPixels: 26, //线条的最大宽度（以像素为单位）
          lineWidthMinPixels: 4, //线条的最小宽度（以像素为单位）
          getLineColor: d => {
            if (stateRef.value.activeLine?.name === d.properties.name) {
              return [...hexToRgb('#EEFF00'), 255]
            }
            return [...hexToRgb('#95f202'), 255]
          },

          // pointType: 'text',
          // getText: d => {
          //   // if (attrs.zoom <= 13) return ''
          //   return d.properties?.name
          // },
          // getTextColor: [29, 33, 41, 255],
          // textCharacterSet: 'auto',
          // getTextSize: 12,
          // textOutlineColor: [255, 255, 255, 255],
          // textOutlineWidth: 7,
          // textFontSettings: { sdf: true, smoothing: 0.3 },

          onClick(opt) {
            if (stateRef.value.activeLine?.name === opt.object.properties.name) {
              // stateRef.value.activeLine = null
              // emits('handleClickLine', null)
            } else {
              stateRef.value.activeLine = opt.object.properties
              emits('handleClickLine', opt.object.properties)
            }
          },
        }),
        new GeoJsonLayer({
          id: 'geojson-layer-point',
          data: pointGeojson,
          filled: true,
          pointRadiusMinPixels: 5,
          pointRadiusMaxPixels: 5,
          getFillColor: d => {
            return [...hexToRgb('#0084FF'), 255]
          },
          pointType: 'circle',
        }),
      ],
    })
  }

  getValueByKey('sthgq_gghw').then(resp => {
    axios
      .get(`${process.env.VUE_APP_GEOSERVER_BASE}${process.env.VUE_APP_GEOSERVER_URL}&typeName=${resp.data}`)
      .then(res => {
        stateRef.value.geojson = {
          ...res.data,
          features: res.data.features.filter(el =>
            props.dataSource.some(ele => ele.newProjectName === el.properties.name),
          ),
        }

        stateRef.value.activeLine = stateRef.value.geojson.features.find(
          el => el.properties.name === props.dataSource[0].newProjectName,
        ).properties

        emits('handleClickLine', stateRef.value.activeLine)

        nextTick(() => {
          updateProps()
        })
      })
  })

  watch(
    () => stateRef.value.activeLine,
    newVal => {
      stateRef.value.deckOverlay.setProps({ layers: [] })
      setTimeout(() => {
        nextTick(() => {
          updateProps()
        })
      }, 50)
    },
  )
</script>

<style scoped lang="less"></style>
