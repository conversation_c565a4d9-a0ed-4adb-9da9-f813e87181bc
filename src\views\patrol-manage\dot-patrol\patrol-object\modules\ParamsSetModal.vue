<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="modalTitle"
      :loading="modalLoading"
      modalWidth="1200"
      @cancel="cancel"
      modalHeight="800"
    >
      <div slot="content">
        <div class="table-panel" layout="vertical">
          <!-- 左侧树 -->
          <div class="tree-panel">
            <a-tabs type="card" v-model="treeTabKey" @change="key => (treeTabKey = key)">
              <a-tab-pane key="1" tab="工程">
                <TreeGeneral
                  v-if="treeTabKey === '1'"
                  :key="1"
                  hasTab
                  style="width: 220px"
                  ref="treeGeneralRef"
                  :treeOptions="projectTreeOptions"
                  @onTreeMounted="onTreeMounted"
                  @select="node => clickTreeNode(node, 'HP')"
                />
              </a-tab-pane>
              <a-tab-pane key="2" tab="监测站点">
                <TreeGeneral
                  v-if="treeTabKey == '2'"
                  :key="2"
                  hasTab
                  ref="treeGeneralRef"
                  :treeOptions="treeOptions"
                  @onTreeMounted="onTreeMounted"
                  @select="node => clickTreeNode(node, 'MS')"
                />
              </a-tab-pane>
              <a-tab-pane key="3" tab="水系">
                <TreeGeneral
                  v-if="treeTabKey === '3'"
                  :key="3"
                  hasTab
                  style="width: 220px"
                  ref="treeGeneralRef"
                  :treeOptions="riverTreeOptions"
                  @onTreeMounted="onTreeMounted"
                  @select="node => clickTreeNode(node, 'RL')"
                />
              </a-tab-pane>
            </a-tabs>
          </div>

          <!-- 筛选栏 -->
          <div class="table-panel">
            <ProjectTransfer
              ref="ProjectTransfer"
              v-bind="$attrs"
              v-if="isShowTransferMS"
              :dataOptions="dataSource"
              @selected-change="handleSelectedChange"
              :propsTargetKeys="propsTargetKeys"
              :dataTargetKeys="targetKeys"
              :transferLeftColumns="leftColumnsMS"
              :transferRightColumns="rightColumnsMS"
            ></ProjectTransfer>

            <ProjectTransfer
              ref="ProjectTransfer"
              v-bind="$attrs"
              v-if="isShowTransferRL"
              :dataOptions="dataSource"
              @selected-change="handleSelectedChange"
              :propsTargetKeys="propsTargetKeys"
              :dataTargetKeys="targetKeys"
              :transferLeftColumns="leftColumnsRL"
              :transferRightColumns="rightColumnsRL"
            ></ProjectTransfer>

            <ProjectTransfer
              ref="ProjectTransfer"
              v-bind="$attrs"
              v-if="isShowTransferHP"
              :dataOptions="dataSource"
              @selected-change="handleSelectedChange"
              :propsTargetKeys="propsTargetKeys"
              :dataTargetKeys="targetKeys"
              :transferLeftColumns="leftColumnsHP"
              :transferRightColumns="rightColumnsHP"
            ></ProjectTransfer>
          </div>
        </div>
      </div>

      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="confirm" :loading="loading" :disabled="checkedList.length == 0">确定</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import {
    getPatrolObjectList,
    addPatrolObject,
    getPatrolObjectSiteList,
    getPatrolObjectRiverSystemList,
    getPatrolObjectProjectList,
    getTreeByCode,
    getTreeByRiverCode,
    getTreeByProjectCode,
    getPatrolObjectPage,
  } from '../services'
  import draggable from 'vuedraggable'
  import cloneDeep from 'lodash.clonedeep'
  import { getOptions } from '@/api/common'

  import difference from 'lodash/difference'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import TreeGeneral from '@/components/TreeGeneral'
  import ProjectTransfer from '@/components/ProjectTransfer'

  export default {
    name: 'TreeTransfer',
    props: {},
    components: {
      ProjectTransfer,
      TreeGeneral,
      AntModal,
      AdvanceTable,
      draggable,
    },
    data() {
      return {
        loading: false,
        modalLoading: false,
        treeOptions: {
          getDataApi: getTreeByCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
          },
        },
        riverTreeOptions: {
          getDataApi: getTreeByRiverCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
          },
        },
        projectTreeOptions: {
          getDataApi: getTreeByProjectCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
          },
        },

        selectList: [],
        dataSource: [],
        targetKeys: [],
        disabled: false,
        showSearch: true,

        isShowTransferMS: false,
        isShowTransferHP: false,
        isShowTransferRL: false,
        propsTargetKeys: [],
        treeTabKey: '1',
        checkedList: [],
        objectCategoryId: null,

        open: false,
        modalTitle: '',
        rowInfo: {},

        reqType: 'add',

        monitoringIndexOptions: [],
        tableTitle: ' ',
        selectedRowKeys: [],
        selectedRows: [],
        ids: [],
        single: true,
        multiple: true,
        list: [],

        leftColumnsMS: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            align: 'center',
            width: '50px',
          },
          {
            title: '编码',
            dataIndex: 'siteCode',
            align: 'center',
            width: '130px',
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'siteName',
            align: 'center',
            ellipsis: true,
          },
          // {
          //   title: "类型",
          //   dataIndex: "objectType",
          //   align: "center",
          //   width: "110px",
          //   ellipsis: true,
          // },
        ],
        rightColumnsMS: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            align: 'center',
            width: '50px',
          },
          {
            title: '编码',
            dataIndex: 'siteCode',
            align: 'center',
            width: '130px',
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'siteName',
            align: 'center',
            ellipsis: true,
          },
          // {
          //   title: "类型",
          //   dataIndex: "objectType",
          //   align: "center",
          //   width: "110px",
          //   ellipsis: true,
          // },
        ],

        leftColumnsRL: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            align: 'center',
            width: '50px',
          },
          {
            title: '编码',
            dataIndex: 'riverSystemCode',
            align: 'center',
            width: '130px',
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'riverSystemName',
            align: 'center',
            ellipsis: true,
          },
        ],
        rightColumnsRL: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            align: 'center',
            width: '50px',
          },
          {
            title: '编码',
            dataIndex: 'riverSystemCode',
            align: 'center',
            width: '130px',
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'riverSystemName',
            align: 'center',
            ellipsis: true,
          },
        ],

        leftColumnsHP: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            align: 'center',
            width: '50px',
          },
          {
            title: '编码',
            dataIndex: 'projectCode',
            align: 'center',
            width: '130px',
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'projectName',
            align: 'center',
            ellipsis: true,
          },
        ],
        rightColumnsHP: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            align: 'center',
            width: '50px',
          },
          {
            title: '编码',
            dataIndex: 'projectCode',
            align: 'center',
            width: '130px',
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'projectName',
            align: 'center',
            ellipsis: true,
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    methods: {
      getList(type) {
        this.dataSource = []
        let paramObject = {
          lineId: null,
          objectCategoryId: null,
          objectIds: [],
          objectName: '',
          objectType: null,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          patrolType: this.$route.meta.query.patrolType,
          sort: [],
          taskId: null,
        }
        switch (type) {
          case 'MS': //监测站点
            let paramMS = {
              districtCode: '',
              objectCategoryId: this.objectCategoryId,
              pageNum: 1,
              pageSize: Number.MAX_SAFE_INTEGER,
              patrolType: this.$route.meta.query.patrolType,
              siteCode: '',
              siteIds: [],
              siteName: '',
              sort: [],
            }
            getPatrolObjectSiteList(paramMS).then(res => {
              let objArr = res?.data?.data.map((obj, index) => ({
                ...obj,
                key: obj.siteId + `#` + obj.objectCategoryId,
                title: obj.siteName,
                disabled: false,
              }))
              paramObject.objectType = 2
              let selectKeys = []
              getPatrolObjectPage(paramObject).then(res => {
                let orgList = res?.data?.data
                let selectList = orgList.map(obj => obj.objectCode)
                this.dataSource = objArr
                selectKeys = objArr.filter(obj => selectList.includes(obj.siteCode))

                selectKeys.forEach(item => {
                  item.disabled = true
                })
              })
              this.modalLoading = false
              this.isShowTransferMS = true
              this.isShowTransferRL = false
              this.isShowTransferHP = false
            })
            break
          case 'RL': //水系
            let paramRL = {
              districtCode: '',
              objectCategoryId: this.objectCategoryId,
              pageNum: 1,
              pageSize: Number.MAX_SAFE_INTEGER,
              patrolType: this.$route.meta.query.patrolType,
              riverIds: [],
              riverSystemCode: '',
              riverSystemName: '',
              sort: [],
            }
            getPatrolObjectRiverSystemList(paramRL).then(res => {
              let objArr = res?.data?.data.map((obj, index) => ({
                ...obj,
                key: String(index),
                title: String(index),
                disabled: false,
              }))

              paramObject.objectType = 3
              let selectKeys = []
              getPatrolObjectPage(paramObject).then(res => {
                let orgList = res?.data?.data
                let selectList = orgList.map(obj => obj.objectCode)
                this.dataSource = objArr
                selectKeys = objArr.filter(obj => selectList.includes(obj.siteCode))
                selectKeys.forEach(item => {
                  item.disabled = true
                })
              })

              this.modalLoading = false
              this.isShowTransferMS = false
              this.isShowTransferRL = true
              this.isShowTransferHP = false
            })
            break
          case 'HP': //工程
            let paramHP = {
              districtCode: '',
              objectCategoryId: this.objectCategoryId,
              pageNum: 1,
              pageSize: Number.MAX_SAFE_INTEGER,
              patrolType: this.$route.meta.query.patrolType,
              projectCode: '',
              projectIds: [],
              projectName: '',
              sort: [],
            }
            getPatrolObjectProjectList(paramHP).then(res => {
              let objHPArr = res?.data?.data.map((obj, index) => ({
                ...obj,
                key: String(index),
                title: obj.projectName,
                disabled: false,
              }))

              paramObject.objectType = 1
              let selectHPKeys = []
              getPatrolObjectPage(paramObject).then(res => {
                let orgHPList = res?.data?.data
                let selectHPList = orgHPList.map(obj => obj.objectCode)
                this.dataSource = objHPArr
                selectHPKeys = objHPArr.filter(obj => selectHPList.includes(obj.projectCode))
                selectHPKeys.forEach(item => {
                  item.disabled = true
                })
              })

              this.modalLoading = false
              this.isShowTransferMS = false
              this.isShowTransferRL = false
              this.isShowTransferHP = true
            })
            break
          default:
            break
        }
        this.$forceUpdate()
      },
      handleSelectedChange(newVal, oldVal) {
        this.checkedList = newVal
      },
      async typeTargetKeys(Arr) {
        this.propsTargetKeys = Arr
      },
      // 树加载完成后
      onTreeMounted(data) {
        if (this.treeTabKey == '2') {
          this.objectCategoryId = data[0].objectCategoryId
          this.tableTitle = data[0].objectCategoryName
          this.getList('MS')
        }
        if (this.treeTabKey == '3') {
          this.objectCategoryId = data[0].objectCategoryId
          this.tableTitle = data[0].objectCategoryName
          this.getList('RL')
        }
        if (this.treeTabKey == '1') {
          this.objectCategoryId = data[0].objectCategoryId
          this.tableTitle = data[0].objectCategoryName
          this.getList('HP')
        }
      },
      /* 树点击事件 */
      clickTreeNode(node, type) {
        if (type == 'MS') {
          this.objectCategoryId = node.$options.propsData.eventKey
          this.getList('MS')
        }
        if (type == 'RL') {
          this.objectCategoryId = node.$options.propsData.eventKey
          this.getList('RL')
        }
        if (type == 'HP') {
          this.objectCategoryId = node.$options.propsData.eventKey
          this.getList('HP')
        }

        this.tableTitle = node.$options.propsData.dataRef.title
      },
      /* 设备配置 */
      getRowSelection({ disabled, selectedKeys, itemSelectAll, itemSelect }) {
        return {
          getCheckboxProps: item => ({ props: { disabled: disabled || item.disabled } }),
          onSelectAll(selected, selectedRows) {
            const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({ key }) => key)
            const diffKeys = selected
              ? difference(treeSelectedKeys, selectedKeys)
              : difference(selectedKeys, treeSelectedKeys)
            itemSelectAll(diffKeys, selected)
          },
          onSelect({ key }, selected) {
            itemSelect(key, selected)
          },
          selectedRowKeys: selectedKeys,
        }
      },

      triggerDisable(disabled) {
        this.disabled = disabled
      },

      triggerShowSearch(showSearch) {
        this.showSearch = showSearch
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.treeTabKey = '1'
        this.targetKeys = []
        this.$emit('ok')
        this.$emit('close')
      },
      /* 设备配置 */
      handleShow(record) {
        this.open = true
        this.modalTitle = '巡检对象选择'
        this.modalLoading = true
        this.rowInfo = record
        this.treeTabKey = '1'
        this.targetKeys = []
      },

      /** 提交按钮 */
      confirm() {
        let objectArr = []
        this.loading = true
        for (let i = 0; i < this.checkedList.length; i++) {
          objectArr.push({
            address: '',
            objectCategoryId: this.checkedList[i].objectCategoryId,
            objectCode:
              this.treeTabKey == '2'
                ? this.checkedList[i].siteCode
                : this.treeTabKey == '3'
                ? this.checkedList[i].riverSystemCode
                : this.treeTabKey == '1'
                ? this.checkedList[i].projectCode
                : '',
            objectName:
              this.treeTabKey == '2'
                ? this.checkedList[i].siteName
                : this.treeTabKey == '3'
                ? this.checkedList[i].riverSystemName
                : this.treeTabKey == '1'
                ? this.checkedList[i].projectName
                : '',
            objectRelId:
              this.treeTabKey == '2'
                ? this.checkedList[i].siteId
                : this.treeTabKey == '3'
                ? this.checkedList[i].riverSystemId
                : this.treeTabKey == '1'
                ? this.checkedList[i].projectId
                : '',
            objectType: this.treeTabKey == '2' ? 2 : this.treeTabKey == '3' ? 3 : this.treeTabKey == '1' ? 1 : null,
            parentId: null,
            patrolType: this.$route.meta.query.patrolType,
          })
        }

        if (objectArr.length != 0) {
          addPatrolObject(objectArr)
            .then(res => {
              this.$message.success('新增成功', 3)
              this.open = false
              this.$emit('ok')
              this.$emit('close')
            })
            .catch(() => (this.loading = false))
        } else {
          this.loading = false
          // this.$emit('ok')
          // this.$emit('close')
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';

  // 多余滚动条
  ::v-deep .advanced-table {
    overflow-y: visible;
  }

  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background-color: transparent;
  }

  ::v-deep .table-panel {
    display: flex;
    height: 820px;
    width: 100%;
    .tree-panel {
      background-color: #fff;
      margin-right: 10px;
      border-radius: 2px;
      border: 1px solid #d9d9d9;
      // height: calc(100vh - 110px);
      height: 700px;
      // padding: 16px 10px;

      .tab-tree-panel-box {
        margin: 7px 0;
        overflow-y: auto;
        overflow-x: hidden;
        height: 580px;
        // height: calc(100vh - 207px);
      }
    }
    .table-panel {
      flex: 1;
      width: 1160px;
      height: 700px;
    }
  }

  ::v-deep .ant-input-affix-wrapper .ant-input {
    margin-top: -8px !important;
    margin-left: 10px !important;
    z-index: 9;
    width: 200px !important;
    position: relative;
    text-align: inherit;
  }

  ::v-deep .ant-transfer-list-header {
    color: #fff;
    background: #1890ff;
  }
  ::v-deep .ant-modal-body {
    width: 100% !important;
    height: 740px !important;
    max-height: 740px !important;
    overflow: hidden !important;
  }

  ::v-deep .ant-transfer-list-body-customize-wrapper {
    height: 660px !important;
    overflow-y: hidden;
    overflow-x: hidden;
  }
  ::v-deep .ant-table-wrapper {
    max-height: 760px !important;
    overflow-y: auto;
    overflow-x: hidden;
  }
  ::v-deep .modal-content {
    height: 760px !important;
    width: 100% !important;
  }
  ::v-deep .ant-modal-content {
    height: 860px;
    width: 100%;
    overflow-x: hidden;
  }

  ::v-deep .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
    height: 40px;
    margin-right: 2px;
    margin-top: 0px;
    line-height: 40px;
  }

  ::v-deep .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab:last-child {
    height: 40px;
    margin-right: 0px;
    margin-top: 0px;
    line-height: 40px;
  }
  ::v-deep .ant-table-placeholder {
    height: 600px !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
