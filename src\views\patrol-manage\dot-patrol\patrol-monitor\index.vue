<template>
  <div class="common-table-page">
    <div class="table-panel" ref="form" :model="form" layout="vertical">
      <!-- 左侧 -->
      <div class="left-panel">
        <div class="title">巡检监控</div>
        <div class="search">
          任务名称：
          <a-input
            v-model="queryParam.taskName"
            style="width: 140px"
            placeholder="请输入任务名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
          <a-button type="primary" class="plus" @click="handleQuery">查询</a-button>
        </div>

        <a-divider />
        <div class="list-content">
          <div
            :class="['item']"
            v-for="(item, index) of list"
            :key="index"
            :style="{ border: item.isChecked ? '1px solid #537cf5' : '1px solid #fff' }"
          >
            <div class="itemTitle" @click="getDetails(item)" :title="item.taskName">任务名称:{{ item.taskName }}</div>
            <div @click="getDetails(item)">
              <div class="itemStyle" style="height: 36px">
                起止时间:
                <div style="margin-left: 68px; margin-top: -22px">
                  {{ item.planStartTime }}
                  <br />
                  ~{{ item.planEndTime }}
                </div>
              </div>
              <div class="itemStyle">
                线路类型:
                <div style="margin-left: 68px; margin-top: -22px">
                  {{ lineTypeName(item.lineType) }}
                </div>
              </div>
              <div class="itemStyle">
                巡检班次:
                <div style="margin-left: 68px; margin-top: -22px">
                  {{ item.shiftName }}
                </div>
              </div>
              <div class="itemStyle">
                巡检班组:
                <div style="margin-left: 68px; margin-top: -22px">
                  {{ item.groupName }}
                </div>
              </div>
              <div class="itemStyle">
                巡检人:
                <div style="margin-left: 68px; margin-top: -22px">
                  {{ item.patrolUserName }}
                </div>
              </div>
              <div class="itemStyle">
                实际开始时间:
                <div style="margin-left: 98px; margin-top: -22px">
                  {{ item.taskStartTime }}
                </div>
              </div>

              <div class="itemStyle" style="margin-top: 20px; justify-content: flex-end">
                <a-button
                  style="float: right; margin-right: 20px; margin-top: -20px; background-color: red; color: #f2f2f2"
                  @click="taskStop(item)"
                >
                  强制结束
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧 统计 折叠面板 -->
      <div class="right-table-panel">
        <!-- 统计数 -->
        <div class="project-statistics">
          <div class="project-data">
            <p class="project-title">巡检对象数</p>
            <p class="project-num">{{ objectCount }}个</p>
          </div>
          <div class="project-data">
            <p class="project-title">设备数</p>
            <p class="project-num">{{ deviceCount }}个</p>
          </div>
          <div class="project-data">
            <p class="project-title">巡检项数</p>
            <p class="project-num">{{ itemCount }}个</p>
          </div>
        </div>
        <!-- 对象折叠 -->
        <div class="project-table">
          <div class="steps">
            <div class="step" v-for="(item, index) in selectObjects" :key="index">
              <div
                class="step-circle"
                :style="{
                  backgroundColor: item.isChecked ? '#62DF1C' : '#1890ff',
                }"
              ></div>
              <div class="step-title" :title="item.objectName">
                {{ item.objectName }}
              </div>
            </div>
          </div>
          <a-collapse
            v-model="activeKey"
            class="collapsePanel"
            @change="switchObject"
            :expand-icon-position="expandIconPosition"
          >
            <a-collapse-panel v-for="(item, index) in selectObjects" :key="String(index)">
              <template slot="header">
                <span
                  style="
                    position: absolute;
                    height: 20px;
                    width: 20px;
                    text-align: center;
                    background-color: #1890ff;
                    color: #f2f2f2;
                  "
                >
                  {{ index + 1 }}
                </span>
                <span
                  style="
                    margin-left: 30px;
                    display: inline-block;
                    width: 188px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  "
                  :title="item.objectName"
                >
                  {{ item.objectName }}
                </span>
                <span style="margin-left: 120px">
                  巡检项：总数{{ item.totalCount }}个 未检{{ item.unCheckCount }}个 正常{{ item.normalCount }}个 异常{{
                    item.expcetionCount
                  }}个 漏检{{ item.forgetCount }}个
                </span>
              </template>
              <a-table
                :columns="tableColumns"
                :rowKey="item => `${item.objectId}#${item.itemId}`"
                :data-source="item.childObject"
                :pagination="false"
                :loading="loading"
              ></a-table>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getPatrolTaskPage, forceEndPatrolTask, cancelPatrolTask, runDetailsPatrolTask } from './services'
  import { getWorkShiftList, getWorkGroupList, getPatrolLineList } from '../patrol-plan/services'
  import { getOptions } from '@/api/common'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'PatrolPlan',
    components: {
      VxeTable,
      VxeTableForm,
    },
    data() {
      return {
        showFormDrawer: false,
        showParamsSetModal: false,
        configTreeTypes: [],
        configTreeOptions: [],

        workShiftList: [],
        workGroupList: [],
        lineOptions: [],
        taskStatus: [],
        lineTypeOptions: [],

        activeStep: null,
        taskOptions: [
          { key: 1, value: '未检' },
          { key: 2, value: '巡检中' },
          { key: 3, value: '已检' },
          { key: 9, value: '已作废' },
        ],
        objectTypeOptions: [
          //巡检对象类型(1-水利工程 2-监测站点 3-江河湖泊 9-工程设备 )
          { key: 1, value: '水利工程' },
          { key: 2, value: '监测站点' },
          { key: 3, value: '江河湖泊' },
          { key: 9, value: '工程设备' },
        ],
        itemStatusOptions: [
          { key: 1, value: '未检' },
          { key: 2, value: '正常' },
          { key: 3, value: '异常' },
          { key: 4, value: '漏检' },
        ],
        objectTypes: {},
        itemStatus: {},
        lineTypes: {},
        list: [],
        selectObjects: [],
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,
        objectCount: 0,
        deviceCount: 0,
        itemCount: 0,

        selectListTotal: 21,
        unSelectListTotal: 13,
        labelCol: { span: 4 },
        wrapperCol: { span: 16 },
        expandIconPosition: 'right',
        activeKey: [],
        selectedRowKeys: [],

        queryParam: {
          patrolUserName: '',
          patrolUserStatus: '',
          patrolType: this.$route.meta.query.patrolType,
          taskName: '',
        },
        form: {},

        tableColumns: [
          {
            title: '序号',
            dataIndex: 'key',
            customRender: (text, record, index) => `${index + 1}`, //此处为重点
          },
          {
            title: '对象类型',
            dataIndex: 'objectType',
            scopedSlots: { customRender: 'objectType' },
            customRender: (_, record, i) => {
              return this.objectTypes[record.objectType]?.value || ''
            },
          },
          { title: '对象编码', dataIndex: 'objectCode' },
          { title: '对象名称', dataIndex: 'objectName' },
          { title: '巡检项', dataIndex: 'itemName' },
          {
            title: '巡检状态',
            dataIndex: 'itemStatus',
            scopedSlots: { customRender: 'itemStatus' },
            width: 80,
            // key: 'key',
            customRender: (_, record, i) => {
              return this.itemStatus[record.itemStatus]?.value || ''
            },
          },
          {
            title: '巡检结果',
            dataIndex: 'itemValue',
            width: 250,
            ellipsis: true,
            customRender: (_, record, i) => {
              return <TableCellOverflow content={record.itemValue} />
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    mounted() {
      this.refreshData = setInterval(() => {
        this.getList()
        this.getData()
      }, 1000 * 60 * 5)
    },
    beforeDestroy() {
      clearInterval(this.refreshData)
      this.refreshData = null
    },
    created() {
      this.taskStatus = getFlatTreeMap(this.taskOptions, 'key')
      this.getList()
      this.getData()
    },
    methods: {
      lineTypeName(data) {
        let name = this.lineTypeOptions.filter(obj => obj.key == String(data))[0].value

        return name
      },
      getDetails(item) {
        runDetailsPatrolTask({ taskId: item.taskId }).then(res => {
          let objArr = res?.data

          this.list.forEach((obj, index) => {
            if (obj.taskId == item.taskId) {
              obj.isChecked = true
            } else {
              obj.isChecked = false
            }
          })
          let deviceArr = []
          let itemNewArr = []
          let targetArr = []

          objArr.forEach((el, idx) => {
            targetArr.push({ ...el, childObject: [] })
            el.childObject.forEach((ele, ibjIndex) => {
              deviceArr.push({ id: ele.objectId, name: ele.objectName })
              ele.patrolItemCheckeds?.forEach((item, itemIndex) => {
                if (item?.itemName) {
                  targetArr[idx].childObject.push({
                    ...ele,
                    ...item,
                    patrolItemCheckeds: null,
                  })
                  itemNewArr.push({
                    id: item.itemId,
                    name: item.itemName,
                    objId: ele.objectId,
                  })
                }
              })
            })
          })

          deviceArr = [...new Set(deviceArr.map(item => item.id + item.name))]
          itemNewArr = [...new Set(itemNewArr.map(item => item.id + item.name + item.objId))]

          let dataSourceArr = []
          targetArr.forEach((el, index) => {
            let totalCount = el.childObject ? el.childObject.length : 0

            let unCheckCount = el.childObject ? el.childObject.filter(obj => obj.itemStatus == 1).length : 0
            let checkCount = totalCount - unCheckCount
            let normalCount = el.childObject ? el.childObject.filter(obj => obj.itemStatus == 2).length : 0
            let expcetionCount = el.childObject ? el.childObject.filter(obj => obj.itemStatus == 3).length : 0
            let forgetCount = el.childObject ? el.childObject.filter(obj => obj.itemStatus == 4).length : 0

            dataSourceArr.push({
              ...el,
              isChecked: totalCount > 0 && totalCount == checkCount,
              totalCount,
              checkCount,
              unCheckCount,
              normalCount,
              expcetionCount,
              forgetCount,
            })
          })

          this.selectObjects = dataSourceArr
          this.objectCount = this.selectObjects.length
          this.deviceCount = deviceArr ? deviceArr.length : 0
          this.itemCount = itemNewArr ? itemNewArr.length : 0
        })
      },
      onSelectChange(selectedRowKeys) {
        this.selectedRowKeys = selectedRowKeys
      },
      selectObject() {},
      switchObject(e) {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.selectedRowKeys = []
        }, 1000)
      },
      getData() {
        getOptions('patrolLineType').then(res => {
          this.lineTypeOptions = res?.data || []
        })
        let paramWorkShift = {
          deptId: null,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          shiftCode: '',
          shiftName: '',
          sort: [],
        }
        getWorkShiftList(paramWorkShift).then(res => {
          this.workShiftList = res?.data?.data
        })
        getWorkGroupList().then(res => {
          this.workGroupList = res?.data[0]?.groups
        })
        let paramPatrolLine = {
          lineName: '',
          lineType: '',
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          patrolType: this.$route.meta.query.patrolType,
          sort: [],
        }
        getPatrolLineList(paramPatrolLine).then(res => {
          this.lineOptions = res?.data?.data
        })
        //线路类型
      },

      /** 查询列表 */
      getList() {
        this.objectTypes = getFlatTreeMap(this.objectTypeOptions, 'key')
        this.itemStatus = getFlatTreeMap(this.itemStatusOptions, 'key')

        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getPatrolTaskPage(this.queryParam).then(response => {
          let list = response?.data
          this.list = list.map((obj, index) => ({
            ...obj,
            createValue: [obj.startTime, obj.endTime],
            isChecked: false,
          }))
          // this.selectObjects = []
          this.loading = false
          this.getDetails(this.list[0])
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          patrolType: this.$route.meta.query.patrolType,
          patrolUserName: '',
          patrolUserStatus: '',
          taskName: '',
        }
        this.handleQuery()
      },

      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.lineId)
        this.names = valObj.records.map(item => item.lineName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.getList()
      },

      /** 删除按钮操作 */
      taskStop(row) {
        var that = this
        const taskIds = row.taskId ? [row.taskId] : this.ids
        const names = row.taskName || this.names

        this.$confirm({
          title: '确认强制结束所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return forceEndPatrolTask({ taskId: taskIds.join(',') }).then(res => {
              that.$message.success(`成功强制结束该条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';

  // 多余滚动条
  ::v-deep .advanced-table {
    overflow-y: visible;
  }

  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background-color: transparent;
  }

  ::v-deep .main .ant-pro-grid-content {
    overflow: hidden !important;
  }

  ::v-deep .table-panel {
    display: flex;
    background-color: #f0f0f0;
    width: 100%;
    height: 100%;
    // height: calc(100% - 10px);

    .left-panel {
      // display: flex;
      position: relative;
      // width: 18%;
      width: 280px;
      border-radius: 2px;
      height: 100%;
      // overflow-y: auto;
      // overflow-x: hidden;
      background-color: #fff;
    }

    .right-table-panel {
      // flex: 1;
      position: relative;
      // width: 82%;
      width: calc(100% - 10px);
      height: 100%;
      margin-left: 10px;
      background-color: #fff;

      .project-statistics {
        position: absolute;
        display: flex;
        width: 100%;
        // display: grid;
        margin-top: 20px;
        // margin-left: 10px;
        // margin-right: 10px;
        // grid-template-columns: repeat(3, 1fr);
        display: flex;
        // flex-direction: column;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;

        .project-data {
          flex: 1;
          width: 30%;
          background-color: #1890ff;
          color: #fff;
          height: 60px;

          // margin: 0 20px;
          margin-right: 80px;

          .project-title {
            float: left;
            margin-left: 15px;
            margin-top: 20px;
          }

          .project-num {
            float: right;
            margin-right: 15px;
            margin-top: 20px;
          }
        }

        .project-data:first-child {
          margin-left: 20px; /* 设置最后一个box元素的margin-right为0 */
        }
        .project-data:last-child {
          margin-right: 20px; /* 设置最后一个box元素的margin-right为0 */
        }
      }

      .project-table {
        position: absolute;
        top: 100px;
        margin-left: 20px;
        margin-right: 20px;
        width: calc(100% - 40px);
        background-color: #fff;
        // height: 100%;
      }
    }
  }

  .collapsePanel {
    height: calc(100vh - 280px);
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #f0f0f0;
  }

  ::v-deep .ant-form-item-label {
    width: 90px !important;
  }

  // ::v-deep .ant-table-thead{
  //   height: 30px !important;
  // }

  ::v-deep .ant-transfer-list-header {
    color: #fff;
    background: #1890ff;
  }

  ::v-deep .ant-modal-body {
    width: 1440px !important;
    height: 740px !important;
    max-height: 740px !important;
    overflow: hidden !important;
  }

  ::v-deep .ant-transfer-list-body-customize-wrapper {
    height: 640px !important;
    overflow-y: hidden;
    overflow-x: hidden;
  }

  ::v-deep .ant-table-wrapper {
    // height: 600px !important;
    overflow-y: auto;
    overflow-x: hidden;
  }

  ::v-deep .modal-content {
    height: 760px !important;
    width: 1380px !important;
  }

  ::v-deep .ant-modal-content {
    margin-left: -160px;
    height: 860px;
    width: 1460px;
    overflow-x: hidden;
  }

  ::v-deep .step {
    /* 其他样式 */
    color: blue; /* 默认蓝色 */
  }
  ::v-deep .step.active {
    color: orange; /* 激活状态的步骤显示为橙色 */
  }

  .title {
    background-color: @primary-color;
    border-radius: 2px 2px 0 0;
    line-height: 20px;
    color: #fff;
    padding: 10px;
  }
  .search {
    display: flex;
    align-items: center;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;
    // padding: 10px;
    white-space: nowrap;
  }

  .ant-divider-horizontal {
    margin: 10px 0;
  }

  .itemTitle {
    display: inline-block;
    overflow: hidden; /*内容会被修剪，并且其余内容是不可见的*/
    text-overflow: ellipsis; /*显示省略符号来代表被修剪的文本。*/
    white-space: nowrap; /*文本不换行*/
    height: 40px;
    width: 100%;
    text-align: left;
    background-color: #1890ff;
    color: #f2f2f2;
    padding: 10px;
  }
  .itemStyle {
    margin-top: 6px;
    margin-left: 10px;
    height: 26px;
  }

  .list-content {
    // top: 40px;width: 100%;height: 210px;background-color: #fff;
    padding: 0 10px;
    // background: #1890ff;
    height: calc(100% - 80px);
    overflow: auto;

    .item {
      // padding-bottom: 10px;
      box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 4px;
      margin: 10px 0;
      border-radius: 2px;
      cursor: pointer;
      transition: all 0.3s;
      overflow: hidden;
      margin-bottom: 10px;
    }

    .checked {
      // color: #537cf5;
      border: 1px solid #537cf5;
    }

    .item:active {
      // padding-bottom: 10px;
      border: 1px solid #537cf5;
      box-shadow: rgba(0, 0, 255, 0.5) 0px 0px 4px;
      margin: 10px 0;
      border-radius: 2px;
      cursor: pointer;
      transition: all 0.3s;
      overflow: hidden;
      margin-bottom: 10px;
    }
  }

  .steps {
    width: 100%;
    display: flex;
  }

  .step {
    width: 280px; /* 每个步骤占据父级div的三分之一 */
    height: 50px; /* 步骤的高度 */
    text-align: left; /* 文本居中 */
    position: relative; /* 将步骤相对定位 */

    .step-circle {
      display: inline-block;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      // background-color: #1890ff;
    }
    .step-title {
      display: inline-block;
      width: 120px;
      overflow: hidden; /*内容会被修剪，并且其余内容是不可见的*/
      text-overflow: ellipsis; /*显示省略符号来代表被修剪的文本。*/
      white-space: nowrap; /*文本不换行*/
      // vertical-align: middle;
      color: #000;
      margin-top: -5px;
    }
  }

  .step::before,
  .step::after {
    content: ''; /* 伪元素不包含任何内容 */
    position: absolute; /* 将伪元素绝对定位 */
    top: 0; /* 伪元素顶部与步骤顶部对齐 */
  }

  /* 调整第一个步骤的伪元素样式，使其不显示线段 */
  .step::before {
    left: -120px; /* 伪元素左侧线段位置 */
    width: 100px; /* 伪元素左侧线段的宽度 */
    top: 10px;
    height: 1px; /* 伪元素左侧线段的高度 */
    background-color: #000; /* 伪元素左侧线段的背景色 */
  }

  /* 调整第一个步骤的伪元素样式，使其不显示线段 */
  .step:first-child::before {
    display: none; /* 隐藏最后一个步骤的伪元素左侧线段 */
    background-color: transparent; /* 设置背景色为透明，以隐藏伪元素左侧线段 */
    left: -20px; /* 设置伪元素左侧线段的位置 */
    width: 60px; /* 设置伪元素左侧线段的宽度 */
    height: 1px; /* 设置伪元素左侧线段的高度 */
    position: absolute; /* 将伪元素绝对定位 */
    top: 0; /* 将伪元素顶部与步骤顶部对齐 */
  }
  /* 调整最后一个步骤的伪元素样式，使其不显示线段 */
  .step:last-child::after {
    display: none; /* 隐藏最后一个步骤的伪元素右侧线段 */
    background-color: transparent; /* 设置背景色为透明，以隐藏伪元素右侧线段 */
    right: -20px; /* 设置伪元素右侧线段的位置 */
    width: 60px; /* 设置伪元素右侧线段的宽度 */
    height: 1px; /* 设置伪元素右侧线段的高度 */
    position: absolute; /* 将伪元素绝对定位 */
    top: 0; /* 将伪元素顶部与步骤顶部对齐 */
  }
</style>
