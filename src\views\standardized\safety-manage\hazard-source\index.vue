<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-model-item label="危险源名称">
        <a-input
          v-model="queryParam.riskName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-model-item>
      <a-form-model-item label="危险类型">
        <a-select
          placeholder="请输入"
          allow-clear
          v-model="queryParam.riskType"
          :options="riskTypeOptions"
        ></a-select>
      </a-form-model-item>
      <a-form-model-item label="危险等级">
        <a-select
          placeholder="请输入"
          allow-clear
          v-model="queryParam.riskLevel"
          :options="riskLevelOptions"
        ></a-select>
      </a-form-model-item>
      <a-form-model-item label="所属工程">
        <a-tree-select
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :riskTypeOptions="riskTypeOptions"
          :riskLevelOptions="riskLevelOptions"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :riskTypeOptions="riskTypeOptions"
          :riskLevelOptions="riskLevelOptions"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree, getSysUserPage } from '@/api/common'
  import { getRiskPage, deleteRisk } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'HazardSource',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
    },
    data() {
      return {
        riskTypeOptions: [],
        riskLevelOptions: [],
        projectOptions: [],
        showForm: false,
        showFormDetails: false,

        isChecked: false,
        list: [],
        tableTitle: '危险源识别',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          riskName: null,
          riskType: undefined,
          riskLevel: undefined,
          projectId: null,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },

          {
            title: '危险源名称',
            minWidth: 120,
            field: 'riskName',
            showOverflow: true,
          },
          {
            title: '危险类型',
            minWidth: 100,
            showOverflow: true,
            slots: {
              default: ({ row }) => {
                return this.riskTypeOptions.find(el => el.value == row.riskType)?.label
              },
            },
          },
          {
            title: '危险等级',
            minWidth: 100,
            showOverflow: true,
            slots: {
              default: ({ row }) => {
                return this.riskLevelOptions.find(el => el.value == row.riskLevel)?.label
              },
            },
          },
          {
            title: '危险区域',
            minWidth: 120,
            field: 'riskAddress',
            showOverflow: true,
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 90,
            showOverflow: true,
          },
          {
            title: '日期',
            minWidth: 110,
            field: 'riskDate',
            showOverflow: true,
          },
          {
            title: '创建时间',
            minWidth: 170,
            field: 'createdTime',
            showOverflow: true,
          },
          {
            title: '操作',
            field: 'operate',
            width: 150,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      getOptions('riskType').then(res => {
        this.riskTypeOptions = res.data.map(el => ({ label: el.value, value: +el.key }))
      })

      getOptions('riskLevel').then(res => {
        this.riskLevelOptions = res.data.map(el => ({ label: el.value, value: +el.key }))
      })

      this.getList()

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.loading = true
        this.selectChange({ records: [] })
        getRiskPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
        this.names = valObj.records.map(item => item.riskName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          riskName: null,
          riskType: undefined,
          riskLevel: undefined,
          projectId: null,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteRisk({ ids: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
