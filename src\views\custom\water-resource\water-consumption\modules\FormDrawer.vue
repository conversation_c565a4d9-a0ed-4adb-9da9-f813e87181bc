<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="500"
    @cancel="cancel"
    modalHeight="724"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="年份" prop="year">
              <a-date-picker
                style="width: 200px"
                allow-clear
                :value="form.year"
                format="YYYY"
                valueFormat="YYYY"
                mode="year"
                @panelChange="
                  val => {
                    form.year = val
                  }
                "
                placeholder="请选择"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="一月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.january"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="二月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.february"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="三月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.march"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="四月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.april"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="五月(万m³)">
              <a-input-number style="width: 200px" allow-clear placeholder="请输入" v-model="form.may" :precision="2" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="六月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.june"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="七月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.july"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="八月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.august"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="九月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.september"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="十月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.october"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="十一月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.november"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="十二月(万m³)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.december"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addAairport, editAairport, getAairportById } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal },
    props: [],
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        loading: false,
        modalLoading: false,
        form: {
          april: null,
          august: null,
          december: null,
          february: null,
          january: null,
          july: null,
          june: null,
          march: null,
          may: null,
          november: null,
          october: null,
          september: null,
          id: null,
          year: null,
        },
        open: false,
        rules: {
          year: [{ required: true, message: '年份不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        this.form.year = moment().format('YYYY')
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getAairportById({
            id: row.id,
          }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
              }
              this.form.year = moment(res?.data?.year, 'YYYY')
            }
            this.modalLoading = false
          })
        }
      },
      //

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const params = {
              ...this.form,
              year: moment(this.form.year).format('YYYY'),
            }
            if (this.form.id == null) {
              addAairport(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editAairport(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
