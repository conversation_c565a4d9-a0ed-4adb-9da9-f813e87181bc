<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane key="1" tab="按类别">
          <TreeGeneral
            v-if="treeTabKey === '1'"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="treeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'category')"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="行政区划">
          <TreeGeneral
            v-if="treeTabKey === '2'"
            :key="2"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="districtTreeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'district')"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="工程编码">
          <a-input
            v-model="queryParam.projectCode"
            placeholder="请输入工程编码"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>
        <a-form-item label="工程名称">
          <a-input
            v-model="queryParam.projectName"
            placeholder="请输入工程名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>

    <FormDrawer
      v-if="showFormDrawer"
      :districtOptions="districtTreeOptions.dataSource"
      :projectOptions="treeOptions.dataSource"
      ref="formDrawerRef"
      @ok="onOperationComplete"
      params
      @close="showFormDrawer = false"
    />

    <FormModal
      v-if="showFormModal"
      :siteTypeList="siteTypeList"
      ref="formModalRef"
      @ok="onOperationComplete"
      params
      @close="showFormModal = false"
    />
    <MonitoringIndexModal
      v-if="showMonitoringIndexModal"
      ref="paramsMonitoringIndexModalRef"
      @ok="showMonitoringIndexModal = false"
    />
    <ChildrenProject
      v-if="showChildrenProject"
      :districtTreeOptions="districtTreeOptions"
      :treeOptions="treeOptions"
      :siteTypeList="siteTypeList"
      :treeTabKey="treeTabKey"
      :objectType="objectType"
      :districtTypes="districtTypes"
      ref="childrenProjectRef"
      @ok="showChildrenProject = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getTreeByCode, deleteProject, getBaseProject } from './services'
  import {
    getDistrictTree,
    getProjectTree,
    getProjectChildren,
    getTreeWithCount,
    getOptions,
    getSysUserPage,
  } from '@/api/common'
  import FormDrawer from './modules/FormDrawer'
  import FormModal from './modules/FormModal'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import TreeGeneral from '@/components/TreeGeneral'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import { getFlatTree } from '@/utils/getMapFlatTree'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import cloneDeep from 'lodash.clonedeep'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'
  import MonitoringIndexModal from '@/components/MonitoringIndex'
  import ChildrenProject from './modules/ChildrenProject.vue'

  export default {
    name: 'ProjectManage',
    components: {
      TreeGeneral,
      AdvanceTable,
      FormModal,
      FormDrawer,
      VxeTable,
      VxeTableForm,
      MonitoringIndexModal,
      ChildrenProject,
    },
    data() {
      return {
        userOptions: [],
        objectType: undefined,
        showMonitoringIndexModal: false,
        showChildrenProject: false,
        siteTypeList: [],
        treeTabKey: '1',
        exportLoading: false,
        treeOptions: {
          getDataApi: getTreeByCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
            value: 'objectCategoryId',
          },
        },
        districtTreeOptions: {
          getDataApi: getDistrictTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'districtName',
            key: 'districtCode',
            value: 'districtCode',
          },
        },
        // 新增编辑
        showFormDrawer: false,
        showFormModal: false,
        list: [],
        tableTitle: '',
        total: 0,
        isChecked: false,
        isExpandIcon: 1,
        isExpandId: null,

        projectId: '',

        ids: [],
        names: [],
        loading: false,

        queryParam: {
          districtCode: undefined,
          objectCategoryId: undefined,
          orgId: undefined,
          pageNum: 1,
          pageSize: 10,
          parentId: undefined,
          patrolType: undefined,
          projectCode: undefined,
          projectIds: [],
          projectName: undefined,
        },
        districtTypes: {}, // 行政区划枚举(临时)
        columns: [
          { type: 'checkbox', width: 30 },
          {
            title: '工程编码',
            field: 'projectCode',
            minWidth: 180,
            showOverflow: 'tooltip',
            treeNode: true,
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '工程类别',
            field: 'objectCategoryName',
            minWidth: 100,
          },
          {
            title: '行政区划',
            field: 'districtCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.districtTypes[row.districtCode]?.districtName || ''
              },
            },
          },
          {
            title: '排序号',
            field: 'sort',
            minWidth: 80,
          },
          {
            title: '负责人',
            field: 'chargeId',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.userOptions.find(el => el.userId == row.chargeId)?.name || ''
              },
            },
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '子工程总数',
            field: 'parentCount',
            minWidth: 80,
          },
          {
            title: '操作',
            field: 'operate',
            width: 280,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleChildren(row)}>查看子工程</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleConfig(row)}>配置代表站</a>
                    <a-divider type='vertical' />
                    <a-dropdown>
                      <a>
                        更多
                        <a-icon type='down' />
                      </a>
                      <a-menu slot='overlay'>
                        <a-menu-item>
                          <a onClick={() => this.handleParamSet(row)}>监测指标</a>
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item>
                          <a onClick={() => this.handleAdd(row)}>添加子工程</a>
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item>
                          <a onClick={() => this.handleDelete(row)}>删除</a>
                        </a-menu-item>
                      </a-menu>
                    </a-dropdown>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getDistrictTree().then(res => {
        this.districtTreeOptions.dataSource = res.data
        this.districtTypes = getFlatTreeMap(res.data, 'districtCode')
      })
      getOptions('siteType').then(res => {
        this.siteTypeList = res?.data || []
      })
      getSysUserPage({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        isDisabled: 0,
        deptId: null,
      }).then(res => {
        this.userOptions = res.data.data
      })
    },
    methods: {
      /** 查询列表 */
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        // 根据树默认选中id来判断
        if (this.queryParam.objectCategoryId || this.queryParam.districtCode) {
          this.getList()
        }
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getBaseProject(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          districtCode: undefined,
          objectCategoryId: undefined,
          orgId: undefined,
          pageNum: 1,
          parentId: undefined,
          patrolType: undefined,
          projectCode: undefined,
          projectIds: [],
          projectName: undefined,
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.projectId)
        this.names = valObj.records.map(item => item.projectName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 树加载完成后
      onTreeMounted(data) {
        if (this.treeTabKey === '1') {
          this.queryParam.objectCategoryId = data[0].objectCategoryId
          this.objectType = data[0]?.objectCategoryCode?.substring(0, 2)
          this.queryParam.districtCode = ''
          this.tableTitle = data[0].objectCategoryName
          this.treeOptions.dataSource = data
        }
        if (this.treeTabKey === '2') {
          this.queryParam.objectCategoryId = ''
          this.queryParam.districtCode = data[0].districtCode
          this.tableTitle = data[0].districtName
        }
        this.getList()
      },

      clickTreeNode(node, type) {
        if (type === 'category') {
          this.queryParam.objectCategoryId = node.$options.propsData.eventKey
          this.objectType = node.$options.propsData?.dataRef?.objectCategoryCode?.substring(0, 2)
        }
        if (type === 'district') {
          this.queryParam.districtCode = node.$options.propsData.eventKey
        }

        this.tableTitle = node.$options.propsData.dataRef.title

        this.getList()
      },
      // 参数配置
      handleParamSet(record) {
        this.showMonitoringIndexModal = true
        let obj = {
          ...record,
          objectType: this.objectType,
          objectId: record?.projectId,
        }
        this.$nextTick(() => {
          this.$refs.paramsMonitoringIndexModalRef.handleShow(obj)
        })
      },
      //子工程
      handleChildren(row) {
        this.showChildrenProject = true
        this.$nextTick(() => this.$refs.childrenProjectRef.projectChildren(row))
      },

      /* 新增 */
      handleAdd(record) {
        this.showFormDrawer = true
        const type = record ? 'leaf' : 'root'
        if (this.treeTabKey === '1') {
          const r = record ? record : { objectCategoryId: this.queryParam.objectCategoryId }
          this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(type, r, '1'))
        }
        if (this.treeTabKey === '2') {
          const r = record ? record : { districtCode: this.queryParam.districtCode }
          this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(type, r, '2'))
        }
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },
      handleConfig(record) {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleUpdate(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const projectIds = row.projectId ? [row.projectId] : this.ids
        const names = row.projectName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteProject({ projectIds: projectIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        this.getList(null, true, (columns, data) => {
          this.exportLoading = false

          const dataList = getFlatTree(data).map(el => ({
            ...el,
            districtCode: this.districtTypes[el.districtCode]?.districtName || '',
          }))

          const columnsList = [
            {
              title: '工程编码',
              field: 'projectCode',
              minWidth: 180,
            },
            {
              title: '工程名称',
              field: 'projectName',
              minWidth: 180,
            },
            {
              title: '工程简称',
              field: 'projectNameAbbr',
              minWidth: 110,
            },
            {
              title: '工程类别',
              field: 'objectCategoryName',
              minWidth: 100,
            },
            {
              title: '行政区划',
              field: 'districtCode',
              minWidth: 120,
            },
            {
              title: '所属上级工程',
              field: 'parentName',
              minWidth: 100,
            },
            {
              title: '经度',
              field: 'longitude',
              minWidth: 120,
            },
            {
              title: '纬度',
              field: 'latitude',
              minWidth: 120,
            },
            {
              title: '所在位置',
              field: 'location',
              minWidth: 200,
            },
          ]

          excelExport(columnsList, dataList, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
