<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="隐患名称" prop="hiddenName">
              <a-input v-model="form.hiddenName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="隐患类型" prop="hiddenType">
              <a-select placeholder="请输入" v-model="form.hiddenType" :options="hiddenTypeOptions"></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="隐患地点" ref="hiddenAddress" prop="hiddenAddress">
              <a-input allowClear v-model="form.hiddenAddress" placeholder="请输入">
                <a-icon
                  style="font-size: 20px; cursor: pointer"
                  @click="onMapOpen"
                  slot="addonAfter"
                  type="environment"
                  theme="twoTone"
                />
              </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="部位" prop="toponym">
              <a-input v-model="form.toponym" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="隐患描述" prop="hiddenDescription">
              <a-textarea
                v-model="form.hiddenDescription"
                placeholder="请输入"
                allow-clear
                :auto-size="{ minRows: 3, maxRows: 3 }"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="隐患图片">
              <UploadFile
                :fileUrl.sync="form.phoneAttaches"
                :multiple="true"
                listType="picture-card"
                folderName="safety-manage"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="处置人员" prop="disposeUserId">
              <a-select
                show-search
                option-filter-prop="children"
                placeholder="请输入"
                v-model="form.disposeUserId"
                :options="userOptions"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="处置完成时限" ref="finishTime" prop="finishTime">
              <a-input-number
                v-model="form.finishTimeDay"
                :min="0"
                style="width: 80px"
                @change="onFinishTimeDayChange"
              />
              天
              <a-input-number
                v-model="form.finishTimeHour"
                :min="0"
                style="width: 80px"
                @change="onFinishTimeHourChange"
              />
              小时
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="处置截止时间" prop="disposeDeadline">
              <a-date-picker
                :allowClear="false"
                v-model="form.disposeDeadline"
                :show-time="{ format: 'HH' }"
                format="YYYY-MM-DD HH"
                valueFormat="YYYY-MM-DD HH"
                placeholder="请选择"
                style="width: 100%"
                :disabled-date="current => current && current < moment().subtract(1, 'days').endOf('day')"
                @change="onChangeTime"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="备注" prop="remark">
              <a-textarea
                v-model="form.remark"
                placeholder="请输入"
                allow-clear
                :auto-size="{ minRows: 3, maxRows: 3 }"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="附件" prop="positionAttaches">
              <UploadFile
                :fileUrl.sync="form.positionAttaches"
                :multiple="true"
                listType="text"
                folderName="safety-manage"
              />
            </a-form-model-item>
          </a-col>
        </a-row>

        <MapModal ref="mapModalRef" @close="showMapModal = false" @confirm="onMapModalConfirm" />
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button type="primary" @click="submitForm('save')" :loading="loading">保存</a-button>
      <a-button type="primary" @click="submitForm('report')" :loading="loading">上报</a-button>
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addHidden, updateHidden, getHidden, reportedHidden, updateHiddenStatus } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'
  import MapModal from '@/components/MapBox/MapboxModal.vue'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile, MapModal },
    props: ['projectOptions', 'hiddenTypeOptions', 'userOptions'],
    data() {
      return {
        showMapModal: false,

        loading: false,
        modalLoading: false,
        formTitle: '',
        open: false,

        moment,

        form: {
          hiddenId: undefined,
          disposeDeadline: undefined,
          disposeUserId: undefined,
          finishTime: undefined,
          finishTimeDay: undefined,
          finishTimeHour: undefined,
          hiddenAddress: undefined,
          hiddenDescription: undefined,
          hiddenName: undefined,
          hiddenType: undefined,
          projectId: undefined,
          toponym: undefined,
          remark: undefined,
          phoneAttaches: [],
          positionAttaches: [],
        },
        rules: {
          hiddenName: [{ required: true, message: '隐患名称不能为空', trigger: 'blur' }],
          hiddenType: [{ required: true, message: '隐患类型不能为空', trigger: 'change' }],
          hiddenAddress: [{ required: true, message: '隐患地点不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
          toponym: [{ required: true, message: '部位不能为空', trigger: 'blur' }],
          hiddenDescription: [{ required: true, message: '隐患描述不能为空', trigger: 'blur' }],
          disposeUserId: [{ required: true, message: '处置人员不能为空', trigger: 'change' }],
          finishTime: [{ required: true, message: '处置完成时限不能为空', trigger: 'change' }],
          disposeDeadline: [{ required: true, message: '处置截止时间不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 打开地图
      onMapOpen() {
        this.showMapModal = true
        const mapInfo = {
          longitude: undefined,
          latitude: undefined,
          location: this.form.hiddenAddress,
        }
        this.$nextTick(() => this.$refs.mapModalRef.handleOpen(mapInfo))
      },
      onMapModalConfirm(mapInfo) {
        this.form.hiddenAddress = mapInfo.location
        this.$refs.hiddenAddress.onFieldChange()
        this.showMapModal = false
      },

      onChangeTime(value, dateString) {
        // this.form.disposeDeadline = dateString

        let m2 = moment(dateString)
        let m1 = moment()
        let du = moment.duration(m2 - m1, 'ms')
        let days = du.get('days')
        let hours = du.get('hours') + 1
        this.form.finishTimeDay = days
        this.form.finishTimeHour = hours

        this.onFinishTimeDayChange(days)
        this.onFinishTimeHourChange(hours)
      },
      /**
       * 改变处置完成时限天数时触发
       * @param {Number} val 处置完成时限天数
       */
      onFinishTimeDayChange(val) {
        this.form.finishTime = `${val ? val + '天' : ''}${
          this.form.finishTimeHour ? this.form.finishTimeHour + '小时' : ''
        }`

        this.form.disposeDeadline = undefined
        this.form.disposeDeadline = this.form.disposeDeadline
          ? moment(this.form.disposeDeadline).add(this.form.finishTimeDay, 'days').format('YYYY-MM-DD HH')
          : moment().add(this.form.finishTimeDay, 'days').format('YYYY-MM-DD HH')

        this.$refs.finishTime.onFieldChange()
      },
      onFinishTimeHourChange(val) {
        this.form.finishTime = `${this.form.finishTimeDay ? this.form.finishTimeDay + '天' : ''}${
          val ? val + '小时' : ''
        }`

        this.form.disposeDeadline = undefined

        this.form.disposeDeadline = this.form.disposeDeadline
          ? moment(this.form.disposeDeadline).add(this.form.finishTimeDay, 'days').format('YYYY-MM-DD HH')
          : moment().add(this.form.finishTimeDay, 'days').format('YYYY-MM-DD HH')

        this.form.disposeDeadline = this.form.disposeDeadline
          ? moment(this.form.disposeDeadline).add(this.form.finishTimeHour, 'hours').format('YYYY-MM-DD HH')
          : moment().add(this.form.finishTimeHour, 'days').format('YYYY-MM-DD HH')

        this.$refs.finishTime.onFieldChange()
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '隐患上报'
        if (row != undefined) {
          this.form.hiddenId = row?.hiddenId
          this.modalLoading = true
          this.formTitle = '隐患上报'
          getHidden({ hiddenId: row.hiddenId }).then(res => {
            if (res.code == 200) {
              const time = res.data.finishTime
              let finishTimeHour = undefined
              if (time.includes('小时')) {
                if (time.includes('天')) {
                  finishTimeHour = +time.match(/天(\S*)小时/)[1]
                } else {
                  finishTimeHour = +time.split('小时')[0]
                }
              } else {
                finishTimeHour = undefined
              }

              this.form = {
                ...res.data,
                phoneAttaches: res.data.phoneAttaches?.map(el => el.attachUrl),
                positionAttaches: res.data.positionAttaches?.map(el => el.attachUrl),
                finishTimeDay: time.includes('天') ? +time.split('天')[0] : undefined,
                finishTimeHour,
              }

              this.modalLoading = false
            }
          })
        }
      },

      /** 提交按钮 */
      submitForm(type) {
        this.$refs.form.validate(valid => {
          if (valid) {
            const params = {
              ...this.form,
              disposeDeadline: this.form.disposeDeadline
                ? moment(this.form.disposeDeadline).format('YYYY-MM-DD HH:mm:ss')
                : undefined,
            }

            this.loading = true

            if (!!this.form.hiddenId) {
              if (type === 'report') {
                updateHiddenStatus({ hiddenId: this.form.hiddenId })
                  .then(res => {
                    if (res.code == 200) {
                      this.$message.success('上报成功', 3)
                      this.open = false
                      this.$emit('close')
                      this.$emit('ok')
                    }
                  })
                  .finally(() => (this.loading = false))
              } else {
                updateHidden(params)
                  .then(res => {
                    if (res.code == 200) {
                      this.$message.success('修改成功', 3)
                      this.open = false
                      this.$emit('close')
                      this.$emit('ok')
                    }
                  })
                  .finally(() => (this.loading = false))
              }
            } else {
              if (type === 'report') {
                reportedHidden(params)
                  .then(res => {
                    if (res.code == 200) {
                      this.$message.success('上报成功', 3)
                      this.open = false
                      this.$emit('close')
                      this.$emit('ok')
                    }
                  })
                  .finally(() => (this.loading = false))
              } else {
                addHidden(params)
                  .then(res => {
                    if (res.code == 200) {
                      this.$message.success('新增成功', 3)
                      this.open = false
                      this.$emit('close')
                      this.$emit('ok')
                    }
                  })
                  .finally(() => (this.loading = false))
              }
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
