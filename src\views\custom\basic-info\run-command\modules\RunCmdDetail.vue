<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="720"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">工作票编码：</label>
            <span class="common-value-text">
              {{ objDetail?.cmdCode }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">工作票名称：</label>
            <span class="common-value-text">
              {{ objDetail?.cmdName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">调度方案：</label>
            <span class="common-value-text">
              {{ objDetail?.dispatchCode }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">调度类型：</label>
            <span class="common-value-text">
              {{ objDetail?.dispatchTypeName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">计划工作时间：</label>
            <span class="common-value-text">
              {{ objDetail?.planStartDate + '~' + objDetail?.planEndDate }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px">
            <div class="title">工程信息</div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px;height: 200px">
            <VxeTable
              ref="vxeProjectTableRef"
              :height="200"
              :isShowTableHeader="false"     
              :columns="projectColumns"
              :tableData="objDetail?.projectList || []"
              :rowConfig="{ isCurrent: true, isHover: true }"
            ></VxeTable>
          </div>
        </a-col>

        <template v-if="mode === 'view'">
          <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px">
            <div class="title">审批信息</div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px;height: 200px">
            <VxeTable
              ref="vxeAuditTableRef"
              :height="200"
              :isShowTableHeader="false"     
              :columns="auditColumns"
              :tableData="objDetail?.auditList || []"
              :rowConfig="{ isCurrent: true, isHover: true }"
            ></VxeTable>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px">
            <div class="title">工程操作信息</div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 10px;height: 200px">
            <VxeTable
              ref="vxeProjectOpInfoTableRef"
              :height="200"
              :isShowTableHeader="false"     
              :columns="projectOpInfoColumns"
              :tableData="objDetail?.projectOpInfoList || []"
              :rowConfig="{ isCurrent: true, isHover: true }"
              :editConfig="mode === 'edit' ? { trigger: 'click', mode: 'cell' } : false"
            ></VxeTable>
          </div>
        </a-col>
        </template>
       

        <!-- 审核模式的额外内容 -->
        <template v-if="mode === 'audit'">
          <!-- <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">审批操作</div>
          </a-col> -->
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model ref="auditForm" :model="auditForm" :rules="auditRules">
              <a-form-model-item label="审批意见" prop="auditResult">
                <a-radio-group v-model="auditForm.auditResult" @change="onAuditResultChange">
                  <a-radio :value="1">同意</a-radio>
                  <a-radio :value="0">驳回</a-radio>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item label="其他说明" prop="remark">
                <a-textarea 
                  v-model="auditForm.remark" 
                  placeholder="请输入其他说明" 
                  :rows="4"
                  allow-clear 
                />
              </a-form-model-item>
            </a-form-model>
          </a-col>
        </template>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">{{ mode === 'receive' ? '取消' : mode === 'audit' ? '取消' : mode === 'edit' ? '取消' : '关闭' }}</a-button>
      <a-button v-if="mode === 'audit'" type="primary" @click="handleAudit">确定</a-button>
      <a-button v-if="mode === 'receive'" type="primary" @click="handleReceive">接收</a-button>
      <a-button v-if="mode === 'edit'" type="primary" @click="handleSave" :loading="submitLoading">保存</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable'
  import moment from 'moment'
  import { getRunmdById, auditRunmd, recRunmd, editRunmd } from '../services'

  export default {
    name: 'RunCmdDetail',
    components: { AntModal, VxeTable },
    props: [],
    data() {
      return {
        formTitle: '工作票详情',
        modalLoading: false,
        open: false,
        mode: 'view', // view, audit, receive
        systemUserOptions: [],
        objDetail: {},
        tableList: [],
        auditForm: {
          auditResult: 1,  // 设置默认值为同意
          remark: '',      // 其他说明
        },
        auditRules: {
          auditResult: [{ required: true, message: '请选择审批意见', trigger: 'change' }],
          remark: [],
        },
        submitLoading: false,
        projectColumns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '工程负责人',
            field: 'wardUserName',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 300,
            showOverflow: 'tooltip',
          },
        ],
        auditColumns: [
          {
            title: '审批人',
            field: 'auditUser',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '审批时间',
            field: 'auditTime',
            minWidth: 200,
            showOverflow: 'tooltip',
          },
          {
            title: '审批意见',
            field: 'auditResult',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '备注信息',
            field: 'auditRemark',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
        ],
        projectOpInfoColumns: [
         {
            title: '工程名称',
            field: 'projectName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作时间',
            field: 'operateDate',
            minWidth: 200,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                // 如果是编辑模式，显示日期选择器
                if (this.mode === 'edit' || this.mode === 'audit') {
                  return this.$createElement('a-date-picker', {
                    props: {
                      value: row.operateDate,
                      format: 'YYYY-MM-DD HH:mm:ss',
                      valueFormat: 'YYYY-MM-DD HH:mm:ss',
                      showTime: true,
                      placeholder: '请选择操作时间',
                      disabledDate: (current) => {
                        // 禁用今天之前的日期
                        return current && current < moment().startOf('day')
                      }
                    },
                    style: { width: '100%' },
                    on: {
                      change: (value) => {
                        this.$set(row, 'operateDate', value)
                      }
                    }
                  })
                }
                // 查看模式显示格式化的时间
                return row.operateDate ? moment(row.operateDate).format('YYYY-MM-DD HH:mm:ss') : '-'
              },
            },
          },
          {
            title: '操作人',
            field: 'operateName',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '监护人',
            field: 'guardianName',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          
        ],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '设备名称',
            field: 'deviceName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '开启时间范围',
            minWidth: 200,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return row.startDate + '~' + row.endDate
              },
            },
          },
        ],
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
        // 重置表单
        if (this.$refs.auditForm) {
          this.$refs.auditForm.resetFields()
        }
      },

      getDispatchTypeName(type) {
        const typeMap = {
          1: '防汛调度',
          2: '灌溉调度', 
          3: '生态调水'
        }
        return typeMap[type] || '-'
      },

       // 获取调度类型下拉选项
       getdispatchTypeCodeOptions() {
        getOptions('runCmddispatchTypeCode').then(res => {
          if (res.code == 200) {
            this.dispatchTypeCodeOptions = res.data.map(item => ({
              label: item.value,
              value: item.value
            }))
          }
        })
      },

      /** 按钮操作 */
      handle(row, mode = 'view') {
        this.open = true
        this.mode = mode
        
        // 设置标题
        const titleMap = {
          view: '查看工作票',
          audit: '审核工作票',
          receive: '接收工作票',
          edit: '编辑工作票'
        }
        this.formTitle = titleMap[mode] || '工作票详情'
        
        // 重置审核表单，并设置默认值为同意
        this.auditForm = {
          auditResult: mode === 'audit' ? 1 : null,  // 设置默认值为同意(1)
          remark: '',
        }
        this.updateRemarkRule()
        
        getRunmdById({
          runCmdId: row.runCmdId,
        }).then(res => {
          if (res.code == 200) {
            this.objDetail = {
              ...res?.data,
              workUserIds: res?.data.workUserIds,
              auditList: [{
                auditResult: res?.data.auditResult === 1 ? '同意' : '驳回',
                auditTime: res?.data.auditTime,
                auditUser: res?.data.auditUserName,
                auditRemark: res?.data.remark,
              }],
              // 确保工程操作信息列表是响应式的
              projectOpInfoList: res?.data.projectOpInfoList ? [...res.data.projectOpInfoList] : []
            }
            this.tableList = this.objDetail.deviceOpenTimes
          }
          this.modalLoading = false
        })
      },

      onAuditResultChange(e) {
        this.auditForm.auditResult = e.target.value
        this.updateRemarkRule()
      },

      handleAudit() {
        this.$refs.auditForm.validate(valid => {
          if (valid) {
            // 调用审批接口
            auditRunmd({
              auditResult: this.auditForm.auditResult,
              remark: this.auditForm.remark,
              runCmdId: this.objDetail.runCmdId,
            }).then(res => {
              if (res.code === 200) {
                this.$message.success('审批成功')
                // 通知父组件刷新列表
                this.$emit('auditResult', this.objDetail, this.auditForm)
                this.open = false
                this.$emit('close') 
                this.$refs.auditForm.resetFields()
              } else {
                this.$message.error(res.msg || '审批失败')
              }
            })
          }
        })
      },

      handleReceive() {
        this.submitLoading = true
        recRunmd(this.objDetail.runCmdId).then(res => {
          if (res.code === 200) {
            this.$message.success('接收成功')
            // 通知父组件刷新列表
            this.$emit('receiveResult', this.objDetail)
            this.cancel()
          } else {
            this.$message.error(res.msg || '接收失败')
          }
          this.submitLoading = false
        })
      },

      handleSave() {
        this.submitLoading = true
        
        // 准备保存数据
        const saveData = {
          ...this.objDetail,
          projectOpInfoList: this.objDetail.projectOpInfoList.map(item => ({
            ...item,
            operateDate: item.operateDate
          }))
        }
        
        editRunmd(saveData).then(res => {
          if (res.code === 200) {
            this.$message.success('保存成功')
            // 通知父组件刷新列表
            this.$emit('saveResult', this.objDetail)
            this.cancel()
          } else {
            this.$message.error(res.msg || '保存失败')
          }
          this.submitLoading = false
        }).catch(() => {
          this.submitLoading = false
        })
      },

      updateRemarkRule() {
        if (this.auditForm.auditResult === 0) {
          this.auditRules.remark = [{ required: true, message: '请输入其他说明', trigger: 'blur' }]
        } else {
          this.auditRules.remark = []
        }
        this.$nextTick(() => {
          if (this.$refs.auditForm) {
            this.$refs.auditForm.clearValidate(['remark'])
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  :deep(.ant-form-item) {
    margin-bottom: 20px;
  }
</style>
