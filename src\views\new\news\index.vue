<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item :label="queryParam.typeMenu == 1 ? '新闻标题' : '活动标题'">
        <a-input v-model="queryParam.newsName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item :label="queryParam.typeMenu == 1 ? '新闻类别' : '活动类别'">
        <a-select allowClear v-model="queryParam.type" placeholder="请选择" :options="typeOptions"></a-select>
      </a-form-item>
      <a-form-item label="发布状态">
        <a-select allowClear v-model="queryParam.status" placeholder="请选择" :options="statusOptions"></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="queryParam.typeMenu == 1 ? columns1 : columns2"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          :tooltip-config="tooltipConfig"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :checkboxConfig="{ highlight: true, showHeader: true, checkMethod: ({ row }) => row.status !== 2 }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormModal
          v-if="showForm"
          ref="formRef"
          @ok="onOperationComplete"
          :typeOptions="typeOptions"
          :stateOptions="stateOptions"
          :deptOptions="deptOptions"
          @close="showForm = false"
        />
        <FormModalPreview
          v-if="showFormPreview"
          :typeOptions="typeOptions"
          :stateOptions="stateOptions"
          :deptOptions="deptOptions"
          ref="formPreviewRef"
          @ok="onOperationComplete"
          @close="showFormPreview = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getDeptTree } from '@/api/common'
  import { getNewsPage, deleteNews, setTop, recall } from './services'
  import FormModal from './modules/FormModal.vue'
  import FormModalPreview from './modules/FormModalPreview.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'News',
    components: {
      VxeTable,
      VxeTableForm,
      FormModalPreview,
      FormModal,
    },
    data() {
      return {
        typeOptions: [],
        stateOptions: [],
        statusOptions: [],
        deptOptions: [],

        inspectionTypeOptions: [],
        inspectionTypes: [],
        inspectionStatuses: [],
        planTypes: [],

        showForm: false,
        showFormPreview: false,
        showRecordNewsModal: false,

        list: [],
        tableTitle: '',
        typeTitle: '',
        isChecked: false, //禁止删除
        ids: [],
        names: [],
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          // newsName: '',
          // pageNum: 1,
          // pageSize: 10,
          // sort: [],
          // status: undefined,
          // type: undefined,
          // updatedUserId: undefined,

          newsName: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
          status: undefined,
          type: undefined,
          typeMenu: null, //1灌区新闻 2灌区活动
        },
        tooltipConfig: {
          showAll: true,
          enterable: true,
          contentMethod: ({ type, column, row, items, _columnIndex }) => {
            const { field } = column
            // 重写默认的提示内容
            if (field === 'isTop') {
              if (type === 'header') {
                return '选择首页置顶后，将会取消当前类别中已置顶新闻的置顶状态，请谨慎操作。'
              }
            }
            // 其余的单元格使用默认行为
            return null
          },
        },
        columns1: [
          { type: 'checkbox', width: 30, align: 'center' },
          { type: 'seq', title: '序号', align: 'center', width: 50 },
          {
            title: '是否置顶',
            field: 'isTop',
            minWidth: 90,
            titleHelp: { content: '选择首页置顶后，将会取消当前类别中已置顶新闻的置顶状态，请谨慎操作。' },
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span style='display:flex;align-items:center;'>
                    <a-switch
                      size='small'
                      checked={row.isTop == 1}
                      disabled={row.status == 1}
                      onChange={val => this.onSwitchChange(val, row)}
                    />
                  </span>
                )
              },
            },
          },
          {
            title: '新闻标题',
            field: 'newsName',
            minWidth: 300,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                let tmpName = row.newsName
                return (
                  <div class='news-name'>
                    {row.isTop == 1 ? <a-tag color='cyan'>置顶</a-tag> : ''}
                    <div class='name-text'>{row.newsName}</div>
                    {/* {this.filterName(tmpName, 22)} */}
                  </div>
                )
              },
            },
          },
          {
            title: '新闻类别',
            field: 'type',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.typeOptions.find(el => el.value == row.type)?.label
              },
            },
          },
          {
            title: '发表时间',
            field: 'startDate',
            minWidth: 150,
          },
          {
            title: '发布状态',
            field: 'status',
            minWidth: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.statusOptions.find(el => el.value == row.status)?.label
                // return <a>{this.statusOptions.find(el => el.value == row.status)?.label}</a>
              },
            },
          },
          {
            title: '更新时间',
            field: 'updatedTime',
            minWidth: 150,
          },
          {
            title: '操作人',
            field: 'updatedUserName',
            minWidth: 150,
          },
          {
            title: '操作',
            field: 'operate',
            width: 138,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>查看</a>
                    {row.status == 2 ? <a-divider type='vertical' /> : ''}
                    {row.status == 2 ? <a onClick={() => this.onSwitchHotChange(row)}>撤回</a> : ''}
                    {row.status != 2 ? <a-divider type='vertical' /> : ''}
                    {row.status != 2 ? <a onClick={() => this.handleDelete(row)}>删除</a> : ''}
                  </span>
                )
              },
            },
          },
        ],
        columns2: [
          { type: 'checkbox', width: 30, align: 'center' },
          { type: 'seq', title: '序号', align: 'center', width: 50 },
          {
            title: '是否置顶',
            field: 'isTop',
            minWidth: 90,
            titleHelp: { content: '选择首页置顶后，将会取消当前类别中已置顶新闻的置顶状态，请谨慎操作。' },
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span style='display:flex;align-items:center;'>
                    <a-switch
                      size='small'
                      checked={row.isTop == 1}
                      disabled={row.status == 1}
                      onChange={val => this.onSwitchChange(val, row)}
                    />
                  </span>
                )
              },
            },
          },
          {
            title: '活动标题',
            field: 'newsName',
            minWidth: 200,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                let tmpName = row.newsName
                return (
                  <div class='news-name'>
                    {row.isTop == 1 ? <a-tag color='cyan'>置顶</a-tag> : ''}
                    <div class='name-text'>{row.newsName}</div>
                    {/* {this.filterName(tmpName, 22)} */}
                  </div>
                )
              },
            },
          },
          {
            title: '活动类别',
            field: 'type',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.typeOptions.find(el => el.value == row.type)?.label
              },
            },
          },
          {
            title: '组织单位',
            field: 'deptNames',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '活动开始时间',
            field: 'startDate',
            minWidth: 150,
          },
          {
            title: '活动结束时间',
            field: 'startDate',
            minWidth: 150,
          },
          {
            title: '更新时间',
            field: 'updatedTime',
            minWidth: 150,
          },

          {
            title: '活动状态',
            field: 'state',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.stateOptions.find(el => el.value == row.state)?.label
              },
            },
          },
          {
            title: '发布状态',
            field: 'status',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.statusOptions.find(el => el.value == row.status)?.label
              },
            },
          },
          {
            title: '操作人',
            field: 'updatedUserName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 138,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>查看</a>
                    {row.status == 2 ? <a-divider type='vertical' /> : ''}
                    {row.status == 2 ? <a onClick={() => this.onSwitchHotChange(row)}>撤回</a> : ''}
                    {row.status != 2 ? <a-divider type='vertical' /> : ''}
                    {row.status != 2 ? <a onClick={() => this.handleDelete(row)}>删除</a> : ''}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.queryParam.typeMenu = this.$route.meta.query?.type
      this.tableTitle = this.$route.meta.query?.type == 1 ? '灌区新闻' : '灌区活动'
      this.typeTitle = this.$route.meta.query?.type == 1 ? '新闻' : '活动'
      if (this.$route.meta.query?.type == 1) {
        getOptions('newsType').then(res => {
          let options = res.data.map(el => ({ label: el.value, value: el.key }))
          // this.typeOptions = options?.filter(item => item.value != 1)
          this.typeOptions = options
        })
      } else {
        getOptions('eventType').then(res => {
          let options = res.data.map(el => ({ label: el.value, value: el.key }))
          // this.typeOptions = options?.filter(item => item.value != 1)
          this.typeOptions = options
        })
      }

      getOptions('state').then(res => {
        let options = res.data.map(el => ({ label: el.value, value: el.key }))
        this.stateOptions = options
      })
      getOptions('newStatus').then(res => {
        let options = res.data.map(el => ({ label: el.value, value: el.key }))
        this.statusOptions = options
      })

      getDeptTree({
        deptName: '',
        type: null,
      }).then(res => {
        if (res?.code == 200) {
          let list = res?.data
          this.deptOptions = list
          // console.log('deptOptions', list)
        }
      })

      this.getList()
    },
    mounted() {},
    methods: {
      filterOption(input, option) {
        return this.userOptions.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      filterName(str, length) {
        if (str.length > length) {
          str = str.substring(0, length) + '...'
        }
        return str
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormPreview = false
        this.showRecordNewsModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getNewsPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 置顶
      onSwitchChange(val, row) {
        const newsIds = row.newsId ? [row.newsId] : this.ids
        let isTopNew = row.isTop == 0 ? 1 : row.isTop == 1 ? 0 : ''

        this.confirmIns = this.$confirm({
          title: `确认${val ? '将' + this.typeTitle + '设为置顶显示' : '取消' + this.typeTitle + '的置顶状态'}?`,
          // content: '当前选中名称为"' + newsNames + '"的数据',
          content: isTopNew
            ? '选择首页置顶后，将会取消当前类别中已置顶' + this.typeTitle + '的置顶状态,请谨慎操作。'
            : '取消首页置顶后，将会取消该' + this.typeTitle + '的置顶状态,请谨慎操作。',
          onOk: () => {
            setTop({ newsId: row.newsId, isTop: isTopNew }).then(res => {
              this.getList()
            })
          },
          onCancel() {},
        })
      },
      //撤回
      onSwitchHotChange(row) {
        const newsIds = row.newsId ? [row.newsId] : this.ids
        // let isHotNew = row.isHot == 0 ? 1 : row.isHot == 1 ? 0 : ''

        this.confirmIns = this.$confirm({
          title: `确认将撤回?`,
          content: '撤回将会取消该条' + this.typeTitle + '在“' + this.typeTitle + '”模块中展示。',
          onOk: () => {
            recall({ newsId: row.newsId }).then(res => {
              this.getList()
            })
          },
          onCancel() {},
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        // console.log('valObj.records', valObj)
        this.selectIds = valObj.records.map(item => item.newsId)
        this.names = valObj.records.map(item => item.newsName)
        this.isChecked = !!valObj.records.length
      },

      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({
          property: item.field,
          direction: item.order,
        }))
        this.getList()
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          newsName: '',
          status: undefined,
          type: undefined,
          updatedUserId: undefined,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(null, 1))
      },
      /* 修改 */
      handleEdit(record) {
        // this.showForm = true
        // this.$nextTick(() => this.$refs.formRef.handle(record))
        this.showFormPreview = true
        this.$nextTick(() => this.$refs.formPreviewRef.handle(record, 3))
      },

      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.recordNewsModalRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.newsId ? [row?.newsId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteNews({ newsIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .news-name {
    // width: 310px;
    display: flex;
    height: 20px;

    .name-text {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .topNew {
    background: rgb(0, 255, 255, 0.2);
    color: rgb(0, 255, 255, 1);
    border: 1px dashed rgb(0, 255, 255, 1);
  }
  .hotNew {
    background: rgb(255, 0, 0, 0.2);
    border: 1px solid rgb(0, 255, 255, 1);
    color: rgb(255, 0, 0, 1);
  }

  .status-1 {
    background: red;
    background-color: rgba(0, 0, 0, 0.2); /* 半透明遮罩层 */
    color: grey; /* 禁用状态下的文本颜色 */
    cursor: not-allowed; /* 禁用状态下的鼠标指针样式 */
    /* 样式定义 */
  }
</style>
