import request from '@/utils/request'

// 分页查询
export function getDamageWarnPage(data) {
  return request({
    url: '/model/damage/warn/page',
    method: 'post',
    data,
  })
}

// 新增
export function addDamageWarn(data) {
  return request({
    url: '/model/damage/warn/add',
    method: 'post',
    data,
  })
}

// 修改
export function updateDamageWarn(data) {
  return request({
    url: '/model/damage/warn/update',
    method: 'post',
    data,
  })
}

//删除
export function deleteDamageWarn(params) {
  return request({
    url: '/model/damage/warn/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取所有图层
export function getGeoServer() {
  return request({
    url: '/external/geoserver/layers',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
