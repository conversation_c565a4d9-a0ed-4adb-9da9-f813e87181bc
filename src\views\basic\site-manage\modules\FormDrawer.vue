<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="710"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="站点编码" prop="siteCode">
                <a-input allowClear v-model="form.siteCode" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="站点名称" prop="siteName">
                <a-input allowClear v-model="form.siteName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="站点简称" prop="siteNameAbbr">
                <a-input allowClear v-model="form.siteNameAbbr" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="站点类别" prop="objectCategoryId">
                <a-tree-select
                  v-model="form.objectCategoryId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="siteOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'objectCategoryName',
                    key: 'objectCategoryId',
                    value: 'objectCategoryId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属工程" prop="projectId">
                <a-tree-select
                  v-model="form.projectId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="projectOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'projectName',
                    key: 'projectId',
                    value: 'projectId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属水系" prop="riverSystemId">
                <a-select
                  show-search
                  :allowClear="true"
                  placeholder="请输入"
                  option-filter-prop="children"
                  :filter-option="filterOption"
                  v-model="form.riverSystemId"
                  :options="riverSystemOptions"
                ></a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属其他管理对象">
                <a-select
                  show-search
                  :allowClear="true"
                  placeholder="请输入"
                  option-filter-prop="children"
                  :filter-option="filterOption"
                  v-model="form.otherObjectId"
                  :options="otherObjectOptions"
                ></a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="行政区划" prop="districtCode">
                <a-tree-select
                  v-model="form.districtCode"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="districtOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'districtName',
                    key: 'districtCode',
                    value: 'districtCode',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所在位置" prop="location">
                <a-input allowClear v-model="form.location" placeholder="请输入">
                  <a-icon
                    style="font-size: 20px; cursor: pointer"
                    @click="onMapOpen"
                    slot="addonAfter"
                    type="environment"
                    theme="twoTone"
                  />
                </a-input>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="经度" prop="longitude">
                <a-input-number style="width: 100%" v-model="form.longitude" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="纬度" prop="latitude">
                <a-input-number style="width: 100%" v-model="form.latitude" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="排序号" prop="sort">
                <a-input-number style="width: 100%" v-model="form.sort" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="封面图">
                <UploadFile
                  :fileUrl.sync="form.coverImgUrl"
                  :multiple="false"
                  listType="picture-card"
                  folderName="projectCover"
                  accept=".png,.jpg,.jpeg"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注" prop="remark">
                <a-textarea v-model="form.remark" placeholder="请输入" allow-clear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <MapModal ref="mapModalRef" @close="showMapModal = false" @confirm="onMapModalConfirm" />
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addSite, updateSite, getSite, getBaseOtherObject } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'
  import MapModal from '@/components/MapBox/MapboxModal.vue'

  export default {
    name: 'FormDrawer',
    props: ['districtOptions', 'siteOptions', 'projectOptions', 'riverSystemOptions'],
    components: { AntModal, MapModal, UploadFile },
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        showMapModal: false,
        otherObjectOptions: [],
        // 表单参数
        form: {
          siteId: undefined,
          siteName: undefined,
          siteCode: undefined,
          siteNameAbbr: undefined,
          objectCategoryId: undefined,
          districtCode: undefined,
          projectId: undefined,
          location: '',
          longitude: null,
          latitude: null,
          coverImgUrl: undefined,
          remark: undefined,

          otherObjectId: undefined,
          sort: undefined,
        },
        open: false,
        rules: {
          siteCode: [{ required: true, message: '站点编码不能为空', trigger: 'blur' }],
          siteName: [{ required: true, message: '站点名称不能为空', trigger: 'blur' }],
          objectCategoryId: [{ required: true, message: '站点类别不能为空', trigger: 'change' }],
          districtCode: [{ required: true, message: '行政区划不能为空', trigger: 'change' }],
          sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
        },
      }
    },
    filters: {},
    created() {
      getBaseOtherObject().then(res => {
        this.otherObjectOptions = res.data.map(e => {
          return {
            value: e.otherObjectId,
            label: e.otherObjectName,
          }
        })
      })
    },
    computed: {},
    watch: {},
    methods: {
      // 打开地图
      onMapOpen() {
        this.showMapModal = true
        const mapInfo = {
          longitude: this.form.longitude,
          latitude: this.form.latitude,
          location: this.form.location,
        }
        this.$nextTick(() => this.$refs.mapModalRef.handleOpen(mapInfo))
      },
      onMapModalConfirm(mapInfo) {
        this.form.longitude = mapInfo.longitude
        this.form.latitude = mapInfo.latitude
        this.form.location = mapInfo.location
        this.showMapModal = false
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(r, treeTabType) {
        this.open = true
        this.formTitle = '新增'
        if (treeTabType === '1') {
          this.form.objectCategoryId = r.objectCategoryId || undefined
        }
        if (treeTabType === '2') {
          this.form.districtCode = r.districtCode || undefined
        }
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.modalLoading = true

        getSite({ siteId: record.siteId }).then(res => {
          this.form = {
            ...res.data,
            riverSystemId: res.data?.riverSystemId === null ? undefined : res.data.riverSystemId,
          }
          this.modalLoading = false
        })
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const saveForm = JSON.parse(JSON.stringify(this.form))

            if (this.form.siteId !== undefined) {
              updateSite(saveForm)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            } else {
              addSite(saveForm)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
