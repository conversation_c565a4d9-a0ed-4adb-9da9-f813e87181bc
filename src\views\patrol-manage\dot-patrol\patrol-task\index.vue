<template>
  <div class="common-table-page" :style="isDetail ? { height: '100%' } : {}">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="任务编码">
        <a-input
          v-model="queryParam.taskCode"
          placeholder="请输入任务编码"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="任务名称">
        <a-input
          v-model="queryParam.taskName"
          placeholder="请输入任务名称"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="任务状态">
        <a-select v-model="queryParam.taskStatus" allowClear class="value" placeholder="请选择" @change="handleQuery">
          <a-select-option v-for="(el, i) in taskOptions" :key="i" :value="el.key">
            {{ el.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :label="$route.meta.query.patrolType == 1 ? '巡检线路' : '巡检范围'">
        <a-select v-model="queryParam.lineId" allowClear placeholder="请选择" @change="handleQuery">
          <a-select-option v-for="(d, index) in lineOptions" :key="index" :value="d.lineId">
            {{ d.lineName }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="计划时间">
        <a-range-picker
          allow-clear
          :value="createValue"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>
      <a-form-item label="计划名称">
        <a-input
          v-model="queryParam.planName"
          placeholder="请输入计划名称"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="巡检班组">
        <a-select v-model="queryParam.groupId" allowClear placeholder="请选择" @change="handleQuery">
          <a-select-option v-for="(d, index) in workGroupList" :key="index" :value="d.groupId">
            {{ d.groupName }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="巡检人" style="padding-left: 15px">
        <a-input
          v-model="queryParam.patrolUserName"
          placeholder="请输入巡检人"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">批量导出</a-button>
            <a-button type="primary" @click="handleAdd()" v-if="!isDetail">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked && isDel && !isDetail" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showFormDrawer"
          :configTreeOptions="configTreeOptions"
          :lineTypeOptions="lineTypeOptions"
          ref="formDrawerRef"
          @ok="onOperationComplete"
          @close="showFormDrawer = false"
        />
      </template>
    </VxeTableForm>

    <ParamsSetModal
      v-if="showParamsSetModal"
      :workShiftList="workShiftList"
      :lineOptions="lineOptions"
      ref="paramsSetModalRef"
      @ok="onOperationComplete"
    />

    <LineParamSetModal
      v-if="showLineParamsSetModal"
      ref="lineParamsSetModalRef"
      :workShiftList="workShiftList"
      :lineOptions="lineOptions"
      @ok="onOperationComplete"
      @close="showLineParamsSetModal = false"
    />
    <InspectionResultsModal
      v-if="showInspectionResults"
      ref="inspectionResultsRef"
      @ok="onOperationComplete"
      @close="showInspectionResults = false"
    />
  </div>
</template>

<script lang="jsx">
  import {
    getPatrolTaskPage,
    deletePatrolTask,
    cancelPatrolTask,
    getDelete,
    exportTask,
    copyPatrolTask,
  } from './services'
  import { getWorkShiftList, getWorkGroupList, getPatrolLineList } from '../patrol-plan/services'
  import { getOptions } from '@/api/common'
  import FormDrawer from './modules/FormDrawer.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import ParamsSetModal from './modules/ParamsSetModal.vue'
  import LineParamSetModal from './modules/LineParamSetModal.vue'
  import InspectionResultsModal from './modules/InspectionResultsModal.vue'
  import { message } from 'ant-design-vue'
  import moment from 'moment'

  export default {
    name: 'PatrolPlan',
    components: {
      ParamsSetModal,
      VxeTable,
      VxeTableForm,
      FormDrawer,
      LineParamSetModal,
      InspectionResultsModal,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      type: {
        type: String,
      },
      modalHeight: {},
    },
    data() {
      return {
        copyTaskCode: '',
        adaptPageSize: null,
        exportLoading: false,
        showFormDrawer: false,
        showParamsSetModal: false,
        showLineParamsSetModal: false,
        showInspectionResults: false,
        createValue: [],
        configTreeTypes: [],
        configTreeOptions: [],

        workShiftList: [],
        workGroupList: [],
        lineOptions: [],
        taskStatus: [],
        taskOptions: [
          { key: 1, value: '未巡检' },
          { key: 2, value: '巡检中' },
          { key: 3, value: '已完成' },
          { key: 9, value: '已作废' },
        ],
        lineTypeOptions: [
          { key: '1', value: '普通线路' },
          { key: '2', value: '测试线路' },
        ],
        lineTypes: {},
        list: [],
        tableTitle: '巡检任务',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        isDel: false,

        queryParam: {
          planStartTimeHigh: '',
          planStartTimeLow: '',
          groupId: null,
          isForceEnded: null,
          isIncomplete: null,
          isTemp: null,
          lineId: null,
          pageNum: 1,
          pageSize: 10,
          patrolUserName: '',
          planName: '',
          shiftId: null,
          sort: [],
          taskCode: '',
          taskName: '',
          taskStatus: null,
          patrolType: this.$route.meta.query.patrolType || this.type,

          projectId: this.projectId,
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '任务编码',
            field: 'taskCode',
            minWidth: 60,
            showOverflow: 'tooltip',
          },
          {
            title: '任务名称',
            field: 'taskName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '计划名称',
            field: 'planName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: this.$route.meta.query.patrolType == 1 ? '巡检线路' : '巡检范围',
            field: 'lineName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '巡检班组',
            field: 'groupName',
            minWidth: 70,
          },
          {
            title: '巡检人',
            field: 'patrolUserName',
            minWidth: 60,
          },
          {
            title: '计划开始时间',
            field: 'planStartTime',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '计划结束时间',
            field: 'planEndTime',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '任务状态',
            field: 'taskStatus',
            minWidth: 80,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                let statusPointColor = 'common-status-incomplete'
                if (row.taskStatus == 2) {
                  statusPointColor = 'common-status-waiting'
                } else if (row.taskStatus == 3) {
                  statusPointColor = 'common-status-completed'
                }
                return (
                  <div class='common-status-box'>
                    <i class={['common-status-icon', statusPointColor]}></i>
                    <span>{this.taskStatus[row.taskStatus]?.value + this.isData(row)}</span>
                  </div>
                )
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 240,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    {!this.isDetail && row.taskStatus == 1 && row.isTemp == 1 ? (
                      <a onClick={() => this.handleParamCopy(row)}>复制</a>
                    ) : (
                      ''
                    )}
                    {!this.isDetail && row.taskStatus == 1 && row.isTemp == 1 ? <a-divider type='vertical' /> : ''}
                    {!this.isDetail && row.taskStatus == 1 ? <a onClick={() => this.handleParamSet(row)}>作废</a> : ''}
                    {!this.isDetail && row.taskStatus == 1 ? <a-divider type='vertical' /> : ''}
                    {!this.isDetail && row.isTemp == 1 && row.taskStatus == 1 ? (
                      <a onClick={() => this.handleUpdate(row)}>修改</a>
                    ) : (
                      ''
                    )}
                    {!this.isDetail && row.isTemp == 1 && row.taskStatus == 1 ? <a-divider type='vertical' /> : ''}

                    <a onClick={() => this.handleDetail(row)}>详情</a>
                    {!this.isDetail && this.isDel ? <a-divider type='vertical' /> : ''}
                    {!this.isDetail && this.isDel ? <a onClick={() => this.handleDelete(row)}>删除</a> : ''}
                    {(!this.isDetail && row.taskStatus == 2) || row.taskStatus == 3 ? (
                      <a onClick={() => this.handleInspectionResults(row)}>
                        <a-divider type='vertical' />
                        巡检结果
                      </a>
                    ) : (
                      ''
                    )}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      this.taskStatus = getFlatTreeMap(this.taskOptions, 'key')
      this.getList()
      this.getData()
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.adaptPageSize = pageSize
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      isData(item) {
        let labels = []
        if (item.isIncomplete == 1) {
          labels.push('漏检')
        }
        if (item.isUnchecked == 1) {
          labels.push('未检')
        }
        if (item.isAbnormal == 1) {
          labels.push('异常')
        }
        if (item.isForceEnded == 1) {
          labels.push('强制结束')
        }
        if (labels.length > 0) {
          return `(${labels.join('、')})`
        }

        return ''
      },
      findMenu(menuCode, menuArray) {
        for (let i = 0; i < menuArray.length; i++) {
          if (menuArray[i].menuCode == menuCode) {
            return menuArray[i]
          }
          if (menuArray[i].children) {
            const result = this.findMenu(menuCode, menuArray[i].children)
            if (result) {
              return result
            }
          }
        }
        return null
      },
      getData() {
        let paramWorkShift = {
          deptId: null,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          shiftCode: '',
          shiftName: '',
          sort: [],
        }
        getWorkShiftList(paramWorkShift).then(res => {
          this.workShiftList = res?.data?.data
        })
        getWorkGroupList().then(res => {
          let tmpArr = res?.data
          tmpArr.forEach(element => {
            if (element.groups) {
              element.groups.forEach(item => {
                this.workGroupList.push({ groupId: item.groupId, groupName: item.groupName })
              })
            }
          })
        })
        let paramPatrolLine = {
          lineName: '',
          lineType: '',
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          patrolType: this.$route.meta.query.patrolType || this.type,
          sort: [],
        }
        getPatrolLineList(paramPatrolLine).then(res => {
          this.lineOptions = res?.data?.data
        })

        getDelete().then(delRes => {
          const patrolTask =
            this.$route.meta.query.patrolType == '1'
              ? this.findMenu('patrol-task', delRes.data)
              : this.$route.meta.query.patrolType == '2'
              ? this.findMenu('line-patrol-task', delRes.data)
              : []
          let hasDelData = patrolTask.children
          if (hasDelData) {
            hasDelData.forEach(item => {
              if (item.menuCode == 'task-delete') {
                this.isDel = true
              }
            })
          } else {
            this.isDel = false
          }
        })
      },
      onRangeChange(value, dateString) {
        this.createValue = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.planStartTimeLow = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : undefined
        this.queryParam.planStartTimeHigh = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : undefined
        this.handleQuery()
      },
      /** 查询列表 */
      getList() {
        this.list = []
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getPatrolTaskPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          patrolType: this.$route.meta.query.patrolType || this.type,
          patrolUserName: '',
          taskStatus: null,
          taskCode: '',
          taskName: '',
          groupId: null,
          isStopped: '',
          lineId: null,
          pageNum: 1,
          planName: '',
          planStartTimeHigh: '',
          planStartTimeLow: '',
          sort: [],
        }
        this.createValue = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.taskId)
        this.names = valObj.records.map(item => item.taskName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        // this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      /**批量导出 */
      handleExport() {
        this.exportLoading = true
        if (!this.queryParam.taskStatus || this.queryParam.taskStatus != 3) {
          this.$message.warn(`仅导出“已完成”数据，请选择任务状态为“已完成”`, 3)
          this.exportLoading = false
          return
        } else {
          this.queryParam.pageSize = Number.MAX_SAFE_INTEGER

          exportTask(this.queryParam)
            .then(res => {
              this.exportLoading = false
              const link = document.createElement('a')
              link.href = res.data
              // 设置下载的文件名
              // link.download = "word.docx";
              // 模拟点击<a>标签进行下载
              link.click()
              this.queryParam.pageSize = this.adaptPageSize
              //this.$message.info(`导出数据已完成`, 3)
            })
            .catch(err => {
              this.exportLoading = false
              console.log('err', err)
            })
        }
      },
      /* 新增 */
      handleAdd() {
        if (this.$route.meta.query.patrolType == 1) {
          this.showParamsSetModal = true
          this.$nextTick(() => {
            this.$refs.paramsSetModalRef.handleAdd()
          })
        }
        if (this.$route.meta.query.patrolType == 2) {
          this.showLineParamsSetModal = true
          this.$nextTick(() => {
            this.$refs.lineParamsSetModalRef.handleAdd()
          })
        }
      },
      /* 修改 */
      handleUpdate(record) {
        if (this.$route.meta.query.patrolType == 1) {
          this.showParamsSetModal = true
          this.$nextTick(() => {
            this.$refs.paramsSetModalRef.handleUpdate(record)
          })
        }
        if (this.$route.meta.query.patrolType == 2) {
          this.showLineParamsSetModal = true
          this.$nextTick(() => {
            this.$refs.lineParamsSetModalRef.handleUpdate(record)
          })
        }
      },
      //巡检结果
      handleInspectionResults(record) {
        this.showInspectionResults = true
        this.$nextTick(() => {
          this.$refs.inspectionResultsRef.handleResults(record)
        })
      },
      handleDetail(record) {
        if (this.$route.meta.query.patrolType == 1) {
          this.showParamsSetModal = true
          this.$nextTick(() => {
            this.$refs.paramsSetModalRef.handleDetail(record)
          })
        }
        if (this.$route.meta.query.patrolType == 2 || this.type=='2') {
          this.showLineParamsSetModal = true
          this.$nextTick(() => {
            this.$refs.lineParamsSetModalRef.handleDetail(record)
          })
        }
      },
      //复制
      handleParamCopy(row) {
        var that = this
        const taskId = row.taskId

        // this.$success({
        //   title: '任务复制成功',
        //   content: '任务编码为:222',
        //   okText: '确定',
        //   onOk() {},
        //   onCancel() {},
        // })

        // return
        copyPatrolTask({ taskId: taskId })
          .then(res => {
            that.copyTaskCode = res.data?.taskCode
            // that.isModalVisible = true
            // that.isSuccess = true

            this.$success({
              title: '任务复制成功',
              content: '任务编码为:' + that.copyTaskCode,
              okText: '确定',
              onOk() {},
              onCancel() {},
            })
            that.selectChange({ records: [] })
            that.onOperationComplete()
          })
          .catch(err => {
            // 巡检任务复制失败
            // 请重试
            this.$error({
              title: '巡检任务复制失败',
              content: '请重试',
              okText: '确定',
              onOk() {},
              onCancel() {},
            })

            // that.isModalVisible = true
            // that.isSuccess = false
          })
        // that.isModalVisible = true
        // that.isSuccess = false
      },
      // 作废
      handleParamSet(row) {
        var that = this
        const taskIds = row.taskId ? [row.taskId] : this.ids
        const names = row.taskName || this.names

        this.$confirm({
          title: '确认作废所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return cancelPatrolTask({ taskIds: taskIds.join(',') }).then(res => {
              that.$message.success(`成功作废 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const taskIds = row.taskId ? [row.taskId] : this.ids
        const names = row.taskName || this.names
        // console.log('del taskIds', taskIds)
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deletePatrolTask({ taskIds: taskIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .confirm-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    .title {
      font-size: 16px;
      font-weight: 800;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      .confirm-icon {
        width: 20px;
        height: 20px;
      }
    }

    .desc {
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      padding: 8px 0px 0 24px;
      font-size: 16px;
    }
  }
  // ::v-deep.ant-modal-body {
  //   background: linear-gradient(155deg, rgba(235, 51, 53, 0.2) 5%, rgba(255, 125, 0, 0) 40%) !important;
  // }
</style>
