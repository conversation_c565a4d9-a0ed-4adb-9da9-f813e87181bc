<template>
  <div class="plan-detail-container">
    <!-- 方案标题 -->
    <div class="plan-title">
      <h3>{{ planData?.schedulingName || '--' }}</h3>
      <!-- <div class="plan-name"></div> -->
    </div>
    
    <!-- 信息展示表单 -->
    <div class="info-form-section">
      <div class="info-form-container">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">预报范围：</span>
            <span class="info-value">{{ planData?.fcstRangeName || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">调度类型：</span>
            <span class="info-value">{{ getDispatchTypeLabel(planData?.dispathType) }}</span>
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-item full-width">
            <span class="info-label">预演时间：</span>
            <span class="info-value">{{ planData ? `${planData.startTime} - ${planData.endTime}` : '暂无数据' }}</span>
          </div>
        </div>
        
        <div class="info-row" v-if="detailData">
          <div class="info-item">
            <span class="info-label">预报降水总量：</span>
            <span class="info-value">{{ getDetailValue('sumRaun', 'mm') }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">涝灾面积：</span>
            <span class="info-value">{{ getDetailValue('floodArea', 'm³') }}</span>
          </div>
        </div>
        
        <div class="info-row" v-if="detailData">
          <div class="info-item">
            <span class="info-label">河道最高水位：</span>
            <span class="info-value">{{ getDetailValue('maxWlv', 'm') }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 河道选择下拉框 -->
    <div class="hedao-select-section">
      <a-select
        v-model="selectedHedao"
        placeholder="请选择河道"
        style="width: 100%"
        :options="hedaoOptions"
        allowClear
        @change="onHedaoChange"
      />
    </div>
    
    <!-- 流量水位图表 -->
    <div class="chart-section">
      <div v-if="chartData.length === 0" style="text-align: center; color: #999; padding: 50px;">
        正在加载图表数据...
      </div>
      <LineEchart 
        v-else
        :height="'250px'" 
        :dataSource="chartData"
        :custom="chartCustom"
      />
    </div>
    
    <!-- 播放条 -->
    <div class="play-slider-section">
      <div v-if="times.length === 0" style="text-align: center; color: #999; padding: 20px;">
        正在加载时间轴...
      </div>
      <TimePlaySlider 
        v-else
        :times="times" 
        @onTimeChange="onTimeChange"
      />
    </div>
  </div>
</template>

<script>
import { LineEchart } from '@/components/Echarts'
import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
import axios from 'axios'
// 导入ShuiDongLi的工具方法
import { colors, extractData, getChartsData } from '@/views/schedule/model-result/ShuiDongLi/Utils.js'

export default {
  name: 'PlanDetail',
  components: {
    LineEchart,
    TimePlaySlider
  },
  props: {
    planData: {
      type: Object,
      default: null
    },
    detailData: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    fcstRangeOptions: {
      type: Array,
      default: () => []
    },
    dispatchTypeOptions: {
      type: Array,
      default: () => []
    },
    sourceTypeOptions: {
      type: Array,
      default: () => []
    },
    planTitle: {
      type: String,
      default: '方案详情'
    },
    modelcode: {
      type: String,
      default: '3591e7a45155469388c291f552f7c0f2'
    }
  },
  data() {
    return {
      selectedHedao: '',
      hedaoOptions: [],
      chartData: [],
      times: [], // 这将是根据预演时间生成的假时间轴
      currentTime: '',
      currentTimeIndex: 0, // 新增：当前时间在假时间轴中的索引
      originalTimes: [], // 新增：存储原始数据的时间轴
      timeMapping: {}, // 新增：假时间到原始时间的映射
      // 动态获取的数据
      hedaoData: {},
      colors,
      maxWlevel: null,
      maxQ: null,
      minWlevel: null,
      minQ: null,

      chartCustom: {
        shortValue: true,
        xLabel: '',
        yLabel: '水位(m)',
        yUnit: '',
        legend: true,
        showAreaStyle: true,
        rYUnit: '',
        rYLabel: '流量(m³/s)',
        dataZoom: false,
        color: null,
        grid: {
          left: '8%',
          right: '8%',
          bottom: '15%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendLeft: '65%',
        xAxisData: []
      }
    }
  },

  watch: {
    planData: {
      handler(newVal) {
        if (newVal) {
          console.log('planData更新:', {
            schedulingName: newVal.schedulingName,
            schedulingId: newVal.schedulingId,
            modelId: newVal.modelId,
            startTime: newVal.startTime,
            endTime: newVal.endTime
          })
          
          if (newVal.startTime && newVal.endTime) {
            this.generateFakeTimeline()
          }
        }
      },
      immediate: true
    },
    detailData: {
      handler(newVal) {
        if (newVal) {
          this.initializeData()
        }
      },
      immediate: true
    },
    modelcode: {
      handler(newVal) {
        console.log('modelcode变化:', newVal)
        if (newVal) {
          // 当modelcode变化时，重新加载河道数据
          this.loadHedaoData()
        }
      },
      immediate: true
    }
  },
  
  created() {
    // 初始化河道选项
    this.initHedaoOptions()
    // loadHedaoData将在modelcode的watch中调用
  },
  
  mounted() {
    // 在组件挂载后初始化数据
    this.$nextTick(() => {
      // 如果没有河道选项，从数据中初始化
      if (this.hedaoOptions.length === 0) {
        this.initHedaoOptionsFromData()
      } else {
        this.updateChartData()
      }
    })
  },
  
  methods: {
    async loadHedaoData() {
      if (!this.modelcode) {
        console.warn('modelcode未设置，无法加载河道数据')
        return
      }
      
      console.log('开始加载河道数据，modelcode:', this.modelcode)
      
      try {
        const url = `${process.env.VUE_APP_MODEL_BASE}/${this.modelcode}.json`
        console.log('请求URL:', url)
        
        const response = await axios.get(url)
        this.hedaoData = response.data
        
        console.log('河道数据加载成功，数据键:', Object.keys(this.hedaoData).slice(0, 5))
        
        // 数据加载完成后初始化
        this.initializeFromHedaoData()
      } catch (error) {
        console.error('加载河道数据失败:', error)
        console.error('请求URL:', `${process.env.VUE_APP_MODEL_BASE}/${this.modelcode}.json`)
        // 可以设置默认值或显示错误信息
        this.hedaoData = {}
      }
    },
    
    initializeFromHedaoData() {
      // 获取原始数据的时间轴和其他信息
      const { times, POINTS, maxWlevel, maxQ, minWlevel, minQ } = extractData(this.hedaoData)
      this.originalTimes = times
      this.maxWlevel = maxWlevel
      this.maxQ = maxQ
      this.minWlevel = minWlevel
      this.minQ = minQ
      
      // 如果有planData，生成假时间轴
      if (this.planData && this.planData.startTime && this.planData.endTime) {
        this.generateFakeTimeline()
      } else {
        // 如果没有planData，使用原始时间轴
        this.times = times
        this.currentTime = this.times[0]
        this.currentTimeIndex = 0
      }
      
      console.log('初始化数据:', {
        originalTimes: this.originalTimes.length,
        fakeTimes: this.times.length,
        hedaoDataKeys: Object.keys(this.hedaoData).slice(0, 5)
      })
    },
    
    // 新增：生成假的时间轴
    generateFakeTimeline() {
      if (!this.planData || !this.planData.startTime || !this.planData.endTime) {
        return
      }
      
      const startTime = new Date(this.planData.startTime)
      const endTime = new Date(this.planData.endTime)
      
      // 生成每10分钟的时间点
      const fakeTimeline = []
      const timeMapping = {}
      let currentTime = new Date(startTime)
      let originalIndex = 0
      
      while (currentTime <= endTime && originalIndex < this.originalTimes.length) {
        const timeStr = this.formatDateTime(currentTime)
        fakeTimeline.push(timeStr)
        
        // 映射到原始数据的时间点
        if (originalIndex < this.originalTimes.length) {
          timeMapping[timeStr] = this.originalTimes[originalIndex]
          originalIndex++
        }
        
        // 增加10分钟
        currentTime.setMinutes(currentTime.getMinutes() + 10)
      }
      
      this.times = fakeTimeline
      this.timeMapping = timeMapping
      this.currentTime = this.times[0] || ''
      this.currentTimeIndex = 0
      
      console.log('生成假时间轴:', {
        总时间点: this.times.length,
        起始时间: this.times[0],
        结束时间: this.times[this.times.length - 1],
        时间映射: Object.keys(this.timeMapping).slice(0, 3)
      })
    },
    
    // 新增：格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },
    
    async initHedaoOptions() {
      try {
        // 参考 ShuiDongLi/index.vue 中的河道线要素获取逻辑
        const shuiDongLi = "https://www.sthzxgq.cn:11120/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=sthgq_vector%3AHP005&maxFeatures=50000&outputFormat=application%2Fjson"
        const resp = await axios(shuiDongLi)
        console.log('resp', resp)
        /**
         * 搜集河道线要素 object_name 为名称 id 为编码
         */
        const tempArr = []
        resp.data.features.forEach(item => {
          tempArr.push({
            label: item.properties.object_name,
            value: `GQ${item.properties.id}` // 加上GQ前缀以匹配hedaoData的键名
          })
        })
        this.hedaoOptions = tempArr
        
        // 设置默认选中第一个河道
        if (tempArr.length > 0) {
          // 现在value已经包含GQ前缀了
          this.selectedHedao = String(tempArr[0].value || '')
          console.log('API设置默认河道:', this.selectedHedao)
          // 在设置河道后更新图表数据
          this.$nextTick(() => {
            this.updateChartData()
          })
        }
      } catch (error) {
        console.error('获取河道选项失败:', error)
        // 如果请求失败，从hedaoData中获取河道选项
        this.initHedaoOptionsFromData()
      }
    },
    
    initHedaoOptionsFromData() {
      // 从hedaoData中获取河道选项
      this.hedaoOptions = Object.keys(this.hedaoData).map(hedaoId => ({
        label: `河道${hedaoId.replace('GQ', '')}`, // 这里可以改为更友好的名称
        value: hedaoId // 保持完整的河道ID，包含GQ前缀
      }))
      
      if (this.hedaoOptions.length > 0) {
        this.selectedHedao = String(this.hedaoOptions[0].value || '')
        console.log('数据设置默认河道:', this.selectedHedao, '选项数量:', this.hedaoOptions.length)
        this.$nextTick(() => {
          this.updateChartData()
        })
      }
    },
    
    initializeData() {
      // 如果有detailData，可以在这里处理
      // 但主要数据来源还是hedaoData
      this.updateChartData()
    },
    
    onHedaoChange(value) {
      this.selectedHedao = String(value || '')
      console.log('河道变化:', this.selectedHedao)
      this.updateChartData()
    },
    
    onTimeChange(time) {
      this.currentTime = time
      this.currentTimeIndex = this.times.indexOf(time)
      this.updateChartData()
    },
    
    updateChartData() {
      // 如果没有选中的河道或当前时间，不更新图表
      if (!this.selectedHedao || !this.currentTime) {
        console.warn('updateChartData: 缺少必要参数', { selectedHedao: this.selectedHedao, currentTime: this.currentTime })
        return
      }
      
      // 确保selectedHedao是字符串类型
      const selectedHedaoStr = String(this.selectedHedao || '')
      if (!selectedHedaoStr) {
        console.warn('updateChartData: selectedHedao为空')
        return
      }
      
      // 获取对应的原始时间点
      const originalTime = this.timeMapping[this.currentTime] || this.originalTimes[this.currentTimeIndex] || this.originalTimes[0]
      
      // 使用Utils.js的getChartsData方法获取图表数据
      const hedaoId = selectedHedaoStr
      const { wlevel, q, stakes } = getChartsData(hedaoId, originalTime, this.hedaoData)
      
      if (wlevel && q && stakes) {
        // 设置X轴数据
        this.chartCustom.xAxisData = stakes.map(stake => [stake])
        
        // 设置图表数据
        this.chartData = [
          {
            name: '水位(m)',
            color: '#507EF7',
            yAxisIndex: 0,
            data: wlevel.map(val => +val)
          },
          {
            name: '流量(m³/s)',
            color: '#B5E241',
            yAxisIndex: 1,
            data: q.map(val => +val)
          }
        ]
        
        console.log('更新图表数据:', {
          假时间: this.currentTime,
          原始时间: originalTime,
          数据点数: wlevel.length
        })
      } else {
        // 如果没有数据，清空图表
        this.chartData = []
        this.chartCustom.xAxisData = []
        console.warn('没有找到河道数据:', hedaoId, '假时间:', this.currentTime, '原始时间:', originalTime)
      }
    },
    
    getDetailValue(fieldName, unit) {
      if (!this.detailData) return '0'
      
      const value = this.detailData[fieldName] || 
                   this.detailData[`total${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`] ||
                   this.detailData[fieldName.replace('max', '').replace('Area', 'area')]
      
      return value ? `${value}${unit}` : `0${unit}`
    },
    
    getFcstRangeLabel(value) {
      const option = this.fcstRangeOptions.find(el => el.value === value)
      return option ? option.label : value || '暂无数据'
    },
    
    getDispatchTypeLabel(value) {
      const option = this.dispatchTypeOptions.find(el => el.value === value)
      return option ? option.label : value || '暂无数据'
    },
    
    getSourceTypeLabel(value) {
      const option = this.sourceTypeOptions.find(el => el.value === value)
      return option ? option.label : value || '暂无数据'
    }
  }
}
</script>

<style lang="less" scoped>
.plan-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .plan-title {
    margin-bottom: 16px;
    
    h3 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    
    .plan-name {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }
  
  .info-form-section {
    margin-bottom: 16px;
    
    .info-form-container {
      .info-row {
        display: flex;
        gap: 24px;
        margin: 0 0 12px 0;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .info-item {
          flex: 1;
          display: flex;
          align-items: center;
          
          &.full-width {
            flex: 1;
          }
          
          .info-label {
            font-weight: 600;
            color: #333;
            font-size: 13px;
            margin-right: 8px;
            white-space: nowrap;
            min-width: fit-content;
          }
          
          .info-value {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
            flex: 1;
          }
        }
      }
    }
  }
  
  .hedao-select-section {
    margin-bottom: 16px;
  }
  
  .chart-section {
    flex: 1;
    margin-bottom: 16px;
    min-height: 250px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 8px;
  }
  
  .play-slider-section {
    height: 60px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 8px;
  }
}
</style> 