<template>
  <LineEchart :dataSource="lineChart.dataSource" :custom="lineChart.custom" height="190px" />
</template>

<script lang="jsx">
  import { LineEchart } from '@/components/Echarts'

  export default {
    name: 'ProcessChart',
    components: { LineEchart },
    props: ['dataSource'],
    data() {
      return {
        lineChart: {
          dataSource: [],
          custom: {
            shortValue: true,
            legend: { left: 'center' },
            showAreaStyle: false,
            yLabel: '水位(m)',
            rYLabel: '流量(m³/s)',
            dataZoom: false,
          },
        },
      }
    },
    watch: {
      dataSource: {
        handler(newVal, oldVal) {
          function getCur() {
            if (newVal.chartData[0]?.type === 0 || newVal.chartData[0]?.type === 2) {
              return { field: 'outFlow', label: '过闸流量' }
            }
            if (newVal.chartData[0]?.type === 1 || newVal.chartData[0]?.type === 4) {
              return { field: 'inFlow', label: '抽水流量' }
            }
          }

          this.lineChart.dataSource = [
            {
              name: '上游水位',
              color: '#507EF7',
              data: newVal.chartData.map(el => [el.tm.slice(0, 16), el.upWlv]),
            },
            {
              name: '下游水位',
              color: '#74CF70',
              data: newVal.chartData.map(el => [el.tm.slice(0, 16), el.downWlv]),
            },
            {
              name: getCur()?.label,
              color: '#EEBC47',
              yAxisIndex: 1,
              data: newVal.chartData.map(el => [el.tm.slice(0, 16), el[getCur()?.field]]),
            },
          ]
        },
        deep: true,
        immediate: true,
      },
    },
    created() {},
    methods: {},
  }
</script>

<style lang="less" scoped></style>
