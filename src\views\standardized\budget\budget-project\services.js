import request from '@/utils/request'

// 预算--项目管理--列表分页查询
export function getBudgetProject(data) {
  return request({
    url: '/custom/bid-project/page',
    method: 'post',
    data,
  })
}
// 预算--项目管理--增加
export function addBudgetProject(data) {
  return request({
    url: '/custom/bid-project/add',
    method: 'post',
    data,
  })
}
// 预算--项目管理--详情
export function getBudgetProjectById(params) {
  return request({
    url: '/custom/bid-project/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 预算--项目管理--更新
export function editBudgetProject(data) {
  return request({
    url: '/custom/bid-project/update',
    method: 'post',
    data,
  })
}
// 预算--项目管理--删除
export function deleteBudgetProject(params) {
  return request({
    url: '/custom/bid-project/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//预算--项目--支付管理--列表
export function getBudgetPayment(data) {
  return request({
    url: '/custom/bid-project/payment/list',
    method: 'post',
    data,
  })
}
// 预算--项目--支付管理--增加
export function addBudgetPayment(data) {
  return request({
    url: '/custom/bid-project/payment/add',
    method: 'post',
    data,
  })
}
// 预算--项目--支付管理--详情
export function getBudgetPaymentById(params) {
  return request({
    url: '/custom/bid-project/payment/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 预算--项目--支付管理--更新
export function editBudgetPayment(data) {
  return request({
    url: '/custom/bid-project/payment/update',
    method: 'post',
    data,
  })
}
// 预算--项目--支付管理--删除
export function deleteBudgetPayment(params) {
  return request({
    url: '/custom/bid-project/payment/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//预算--项目--支付管理--年份列表
export function getPaymentYearList(params) {
  return request({
    url: '/custom/bid-project/payment/yearList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//预算--项目--预算管理--列表
export function getBudgetManage(data) {
  return request({
    url: '/custom/bid-project/budget/list',
    method: 'post',
    data,
  })
}

// 预算--项目--预算管理--增加
export function addBudgetManage(data) {
  return request({
    url: '/custom/bid-project/budget/add',
    method: 'post',
    data,
  })
}
// 预算--项目--预算管理--详情
export function getBudgetManageById(params) {
  return request({
    url: '/custom/bid-project/budget/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 预算--项目--预算管理--更新
export function editBudgetManage(data) {
  return request({
    url: '/custom/bid-project/budget/update',
    method: 'post',
    data,
  })
}
// 预算--项目--预算管理--删除
export function deleteBudgetManage(params) {
  return request({
    url: '/custom/bid-project/budget/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//预算--项目--预算管理--年份列表
export function getManageYearList(params) {
  return request({
    url: '/custom/bid-project/budget/yearList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
