<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="500"
    @cancel="cancel"
    modalHeight="560"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="年份" prop="year">
              <a-input v-model="form.year" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="乡镇" prop="districtCode">
              <a-select
                show-search
                placeholder="请输入"
                v-model="form.districtCode"
                option-filter-prop="children"
                :options="countyOptions"
              ></a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="面积（亩）" prop="area">
              <a-input-number
                style="width: 150px"
                allow-clear
                placeholder="请输入"
                v-model="form.area"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="定价（元/亩）" prop="price">
              <a-input-number
                style="width: 150px"
                allow-clear
                placeholder="请输入"
                v-model="form.price"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="水费（万元）" prop="waterRate">
              <a-input-number
                style="width: 150px"
                allow-clear
                placeholder="请输入"
                v-model="form.waterRate"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>

          <!--  -->
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addWaterCharge, editWaterCharge, getWaterChargeById } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal },
    props: ['countyOptions'],
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        loading: false,
        modalLoading: false,
        form: {
          id: null,
          area: null,
          price: null,
          waterRate: null,
          districtCode: null,
          year: null,
        },
        open: false,
        rules: {
          year: [{ required: true, message: '年份不能为空', trigger: 'blur' }],
          price: [{ required: true, message: '定价不能为空', trigger: 'blur' }],
          waterRate: [{ required: true, message: '水费不能为空', trigger: 'blur' }],
          districtCode: [{ required: true, message: '乡镇不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getWaterChargeById({
            id: row.id,
          }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
              }
            }
            this.modalLoading = false
          })
        }
      },
      //

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.form.id == null) {
              addWaterCharge(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editWaterCharge(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
