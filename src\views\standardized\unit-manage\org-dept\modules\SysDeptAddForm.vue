<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :adjust-size="true"
    @cancel="cancel"
    modalHeight="480"
    modalWidth="640"
  >
    <a-form-model ref="form" :model="form" :rules="rules" slot="content" layout="vertical">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="上级部门" prop="parentId">
            <a-tree-select
              v-model="form.parentId"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="deptOptions"
              showLine
              showIcon
              placeholder="请选择"
              :replaceFields="{
                children: 'children',
                title: 'deptName',
                key: 'deptId',
                value: 'deptId',
              }"
              @change="onDeptTreeChange"
              tree-default-expand-all
            ></a-tree-select>
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="部门名称" prop="deptName">
            <a-input v-model="form.deptName" placeholder="请输入" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="单位负责人" prop="unitId">
            <a-select
              allowClear
              show-search
              option-filter-prop="children"
              v-model="form.principalId"
              placeholder="请选择"
              :options="userListOptions"
            ></a-select>
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="排序" prop="sort">
            <a-input-number v-model="form.sort" :min="0" style="width: 100%" />
          </a-form-model-item>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <a-form-model-item label="部门类别" prop="type">
            <a-radio-group v-model="form.type" button-style="solid" v-show="form.deptType == 1">
              <a-radio-button value="1">机构</a-radio-button>
              <a-radio-button value="2">部门</a-radio-button>
            </a-radio-group>
            <a-radio-group v-model="form.type" button-style="solid" v-show="form.deptType == 2">
              <a-radio-button value="1" disabled>机构</a-radio-button>
              <a-radio-button value="2">部门</a-radio-button>
            </a-radio-group>
          </a-form-model-item>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <a-form-model-item label="备注" prop="remark">
            <a-input v-model="form.remark" placeholder="请输入" type="textarea" allow-clear />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm">保存</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import sysDeptForm from './SysDeptForm.js'
  export default {
    ...sysDeptForm,
  }
</script>
