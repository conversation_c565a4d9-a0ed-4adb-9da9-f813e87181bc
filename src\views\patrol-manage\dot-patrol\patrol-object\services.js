import request from '@/utils/request'

// 根据分类编码获取工程列表
export function listByObjectCategoryCode(params) {
  return request({
    url: '/base/project/listByObjectCategoryCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 巡检对象分类-列表分页查询
export function getPatrolObjectPage(data) {
  return request({
    url: '/patrol/object/page',
    method: 'post',
    data
  })
}
// 巡检对象分类-列表分页查询
export function getPatrolObjectList(data) {
  return request({
    url: '/patrol/object/list',
    method: 'post',
    data
    // params: { ...params, keyword: undefined }
  })
}

// 巡检对象分类-删除
export function deletePatrolObject(params) {
  return request({
    url: '/patrol/object/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 巡检对象工程设备-列表查询
export function getPatrolObjectDeviceList(data) {
  return request({
    url: '/patrol/object/device/list',
    method: 'post',
    data
  })
}

// 巡检对象工程信息-列表查询
export function getPatrolObjectProjectList(data) {
  return request({
    url: '/patrol/object/project/list',
    method: 'post',
    data
  })
}
// 巡检对象水系-列表查询
export function getPatrolObjectRiverSystemList(data) {
  return request({
    url: '/patrol/object/riverSystem/list',
    method: 'post',
    data
  })
}
// 巡检对象监测站点-列表查询
export function getPatrolObjectSiteList(data) {
  return request({
    url: '/patrol/object/site/list',
    method: 'post',
    data
  })
}

// 巡检对象-选择巡检对象
export function addPatrolObject(data) {
  return request({
    url: '/patrol/object/add',
    method: 'post',
    data
  })
}

// 巡检对象——设备配置
export function patrolObjectDeviceConfig(params) {
  return request({
    url: '/patrol/object/deviceConfig',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 巡检对象——设备配置列表
export function patrolObjectDeviceList(params) {
  return request({
    url: '/patrol/object/getDeviceList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 巡检对象——选择巡检项
export function patrolObjectItemAdd(data) {
  return request({
    url: '/patrol/object/item/add',
    method: 'post',
    data
  })
}
// 巡检对象——巡检项列表
export function patrolObjectItemList(data) {
  return request({
    url: '/patrol/item/page',
    method: 'post',
    data
  })
}

// 巡检对象分类-获取树
export function getObjectCategoryTree(params) {
  return request({
    url: '/base/objectCategory/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 水利对象分类-根据编码获取站点树
export function getTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params: { ...params, objectCategoryCode: 'MS' }
  })
}
// 水利对象分类-根据编码获取水系树
export function getTreeByRiverCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params: { ...params, objectCategoryCode: 'RL' }
  })
}
// 水利对象分类-根据编码获取工程树
export function getTreeByProjectCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params: { ...params, objectCategoryCode: 'HP' }
  })
}

// 巡检项分类——树形结构
export function getPatrolItemCategoryTree(params) {
  return request({
    url: '/patrol/item/category/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 水利对象-根据对象id获取已选择巡检项
export function getChooseItem(params) {
  return request({
    url: '/patrol/object/item/chooseItem',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

//巡检对象同步——执行一次
export function patrolObjectExecute(params) {
  return request({
    url: '/patrol/object/execute',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
