<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="项目名称">
        <a-input v-model="queryParam.projectName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="中标单位">
        <a-input v-model="queryParam.winBidUnit" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="中标日期">
        <a-range-picker
          allow-clear
          :value="takeEffect"
          format="YYYY-MM-DD"
          formatValue="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <!-- <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button> -->
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>
        <FormBudgetProject
          v-if="showBudgetProjectForm"
          ref="formBudgetProjectRef"
          @ok="onOperationComplete"
          @close="showBudgetProjectForm = false"
        />

        <PaymentManage
          v-if="showPaymentManage"
          ref="managePaymentRef"
          @ok="onOperationComplete"
          @close="showPaymentManage = false"
        />

        <BudgetManage
          v-if="showBudgetManage"
          ref="manageBudgetRef"
          @ok="onOperationComplete"
          @close="showBudgetManage = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getBudgetProject, deleteBudgetProject } from './services'
  import FormBudgetProject from './modules/FormBudgetProject.vue'
  import PaymentManage from './modules/PaymentManage.vue'
  import BudgetManage from './modules/BudgetManage.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'BudgetProject',
    components: {
      VxeTable,
      VxeTableForm,
      FormBudgetProject,
      PaymentManage,
      BudgetManage,
    },
    data() {
      return {
        showBudgetProjectForm: false,
        showPaymentManage: false,
        showBudgetManage: false,
        takeEffect: [],

        list: [],
        tableTitle: '项目管理',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          projectName: undefined,
          sort: [],
          winBidDateBegin: undefined,
          winBidDateEnd: undefined,
          winBidUnit: undefined,
        },
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '项目编码',
            field: 'projectCode',
          },
          {
            title: '项目名称',
            field: 'projectName',
          },
          {
            title: '中标单位',
            field: 'winBidUnit',
          },
          {
            title: '中标日期',
            field: 'winBidDate',
          },
          {
            title: '合同金额(元)',
            field: 'contractAmount',
          },
          {
            title: '工期(年)',
            field: 'projectDuration',
          },
          {
            title: '进度(%)',
            field: 'payPercentage',
          },
          {
            title: '操作',
            field: 'operate',
            width: 239,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handlePayment(row)}>支付管理</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleBudget(row)}>预算管理</a>
                    <a-divider type='vertical' />

                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.winBidDateBegin = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : null
        this.queryParam.winBidDateEnd = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : null
      },
      /** 查询列表 */
      getList() {
        this.showBudgetProjectForm = false
        this.loading = true
        this.selectChange({ records: [] })
        getBudgetProject(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      handleChange(value) {
        this.queryParam.unitId = value
      },
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.projectId)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.takeEffect = []
        this.queryParam = {
          ...this.queryParam,
          pageNum: 1,
          projectName: undefined,
          sort: [],
          winBidDateBegin: undefined,
          winBidDateEnd: undefined,
          winBidUnit: undefined,
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showBudgetProjectForm = true
        this.$nextTick(() => this.$refs.formBudgetProjectRef.handleForm())
      },
      /* 修改 */
      handleEdit(record) {
        this.showBudgetProjectForm = true
        this.$nextTick(() => this.$refs.formBudgetProjectRef.handleForm(record))
      },
      handlePayment(record) {
        this.showPaymentManage = true
        this.$nextTick(() => this.$refs.managePaymentRef.handlePaymentManage(record))
      },
      handleBudget(record) {
        this.showBudgetManage = true
        this.$nextTick(() => this.$refs.manageBudgetRef.handleBudgetManage(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.projectId ? [row?.projectId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteBudgetProject({ projectIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
