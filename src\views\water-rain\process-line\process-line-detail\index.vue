<template>
  <div style="height: 100%; display: flex; flex-direction: column; position: relative">
    <div
      v-if="!tabList.length"
      style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center"
    >
      <a-empty />
    </div>

    <div v-else>
      <a-tabs v-model="active" @change="onTabChange">
        <a-tab-pane v-for="(el, idx) in tabList" :key="el.siteId" :tab="el.siteName"></a-tab-pane>
      </a-tabs>
      <div style="flex: 1">
        <RainWaterLine v-if="site && site.key" :site="site" />
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import RainWaterLine from '@/views/water-rain/process-line/modules/RainWaterLine/index.vue'
  import { ACCESS_TOKEN } from '@/store/mutation-types'
  import storage from 'store'
  import QueryString from 'query-string'
  import { getSiteList } from './services'

  const searchObj = QueryString.parse(decodeURIComponent(location.search))

  export default {
    name: 'ProcessLineDetail',
    components: { RainWaterLine },
    data() {
      return {
        active: undefined,
        tabList: [],
        site: null,
      }
    },
    computed: {},
    created() {
      getSiteList({ objectId: searchObj.objectId, objectType: searchObj.objectType }).then(res => {
        this.tabList = res.data || []

        if (this.tabList.length) {
          this.active = this.tabList[0].siteId

          const site = this.tabList.find(el => el.siteId == this.active)
          this.site = { key: site?.siteId, name: site?.siteName }
        }
      })
    },
    methods: {
      onTabChange(val) {
        const site = this.tabList.find(el => el.siteId == val)
        this.site = { key: site?.siteId, name: site?.siteName }
      },
    },
    mounted() {
      // this.site = {
      //   key: searchObj?.id,
      //   name: searchObj?.name,
      // }
    },
  }
</script>

<style lang="less" scoped></style>
