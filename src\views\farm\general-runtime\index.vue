<template>
  <a-spin :spinning="spinning" size="large" tip="数据正在加载...">
    <div class="common-table-page">
      <a-empty v-if="isEmpty" />
      <div class="box" v-else>
        <div class="card" v-for="device in deviceOpera">
          <div class="content">
            <img class="icon" v-if="device.deviceCategoryCode == '1'" src="@/assets/images/farm/sbfl0004.png" />
            <img class="icon" v-else-if="device.deviceCategoryCode == '2'" src="@/assets/images/farm/sbfl0006.png" />
            <img class="icon" v-else-if="device.deviceCategoryCode == '3'" src="@/assets/images/farm/sbfl0009.png" />
            <div class="device-status status">
              设备状态:
              <span :style="{ color: device.status == 0 ? '#00B42A' : '#FF7D00' }">
                {{ device.status == 0 ? '在线' : '离线' }}
              </span>
            </div>
            <div class="run-status status">
              运行状态:
              <span :style="{ color: '#00B42A' }">开启</span>
            </div>
          </div>
          <p class="title">{{ device.deviceName }}</p>
          <p class="time">更新时间：{{ device.dateTime ? device.dateTime : '--' }}</p>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script lang="jsx">
  import { getDeviceOperation } from './services'

  export default {
    name: 'GeneralRuntime',
    data() {
      return {
        spinning: true,
        isEmpty: false,
        deviceOpera: [],
      }
    },
    computed: {},
    watch: {},
    created() {
      getDeviceOperation().then(res => {
        this.deviceOpera = res.data
        this.isEmpty = false
        this.spinning = false
      })
    },
    methods: {},
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-empty {
    margin-top: 30vh;
  }

  .box {
    width: 100%;
    background-color: white;
    display: flex;
    flex-flow: row wrap;
    align-content: flex-start;
    justify-content: left;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .card {
    width: 219px;
    height: 166px;
    background: #f7f8fa;
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
    margin: 16px 16px;
    padding: 16px 16px;
  }

  .title {
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 5px;
    color: #1d2129;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 鼠标悬浮时显示完整内容 */
  .title:hover {
    white-space: normal; /* 允许换行 */
    overflow: visible; /* 显示全部内容 */
    text-overflow: initial; /* 恢复默认值 */
  }

  .content {
    display: grid;
    grid-template-areas:
      'icon deviceStatus'
      'icon runStatus';
    grid-template-columns: 82px 1fr;
  }

  .icon {
    grid-area: icon;
    width: 82px;
    height: 82px;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    margin-right: 5px;
  }

  .device-status {
    grid-area: deviceStatus;
    margin-top: 14px;
  }

  .run-status {
    grid-area: runStatus;
    margin-top: 14px;
  }
  .status {
    text-align: center;
    height: 25px;
    line-height: 25px;
    background: #ffffff;
    border-radius: 20px 20px 20px 20px;
    opacity: 1;
  }

  .time {
    font-size: 12px;
    font-weight: 300;
    color: #86909c;
  }
</style>
