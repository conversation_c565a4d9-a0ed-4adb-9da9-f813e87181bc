<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :modalWidth="windowSize.width"
    :modalHeight="windowSize.height"
    :loading="modalLoading"
    @cancel="cancel"
    @onFullScreen="onFullScreen"
    :footer="null"
    ref="detailModalRef"
  >
    <div slot="content" layout="vertical" class="detail-content">
      <div class="detail-tab">
        <a-tabs v-model="tabVal" type="line" :tab-position="'left'" :style="{ height: '100%' }" @change="onTabChange">
          <a-tab-pane
            v-for="(ele, i) in displayInfoOptions"
            :key="ele.key"
            :tab="deviceInfo.includes(ele.key) ? ele.option1 : ele.value"
          >
            <!-- 闸基本信息 -->
            <deviceInfoGate
              v-if="ele.key === 'deviceInfoGate'"
              :displayCode="ele.key"
              :deviceId="recordInfo.deviceId"
            />

            <!-- 泵基本信息 -->
            <deviceInfoPump
              v-if="ele.key === 'deviceInfoPump'"
              :displayCode="ele.key"
              :deviceId="recordInfo.deviceId"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import deviceInfoGate from './components/deviceInfoGate'
  import deviceInfoPump from './components/deviceInfoPump'
  import { getDisplayCodes } from '../../services'
  import { getOptions } from '@/api/common'

  export default {
    name: 'CreateForm',
    props: {},
    components: {
      AntModal,
      deviceInfoGate,
      deviceInfoPump,
    },
    data() {
      return {
        open: false,
        modalTitle: '',
        modalLoading: false,
        tabVal: '',
        windowSize: {},
        displayInfoOptions: [],
        recordInfo: {},
        deviceInfo: ['deviceInfoGate', 'deviceInfoPump'], // 基本信息key集合
      }
    },
    created() {
      this.windowSize = {
        width: `${parseInt(window.innerWidth * 0.9)}`,
        height: `${parseInt(window.innerHeight * 0.95)}`,
      }
    },
    computed: {},
    watch: {},
    methods: {
      onTabChange(val) {
        this.tabVal = val
      },

      /** 打开 */
      handleDetail(record) {
        this.modalTitle = record.projectName + '-' + record.deviceName
        this.recordInfo = record
        this.modalLoading = true

        getDisplayCodes({ deviceId: record.deviceId }).then(resp => {
          if (!resp.data?.length) {
            this.$message.info('无展示信息')
            return
          }
          this.open = true
          getOptions('deviceDisplayType').then(res => {
            this.displayInfoOptions = [...this.displayInfoOptions, ...res.data.filter(el => resp.data.includes(el.key))]
            this.tabVal = this.displayInfoOptions[0].key

            this.modalLoading = false
          })
        })
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      onFullScreen() {
        this.$refs.demarcationRef?.[0].onFullScreen()
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 10px;
    background-color: #eef0f3 !important;
    border-radius: 0 0 4px 4px;
    .modal-content {
      width: 100%;
      height: 100%;
    }
  }

  .detail-content {
    width: 100%;
    height: 100%;
    position: relative;

    .detail-tab {
      height: 100%;

      ::v-deep .ant-tabs-content {
        height: 100%;
        padding-left: 0px;
        border-left: none;
        .ant-tabs-tabpane-active {
          height: 100%;
        }
        .ant-tabs-tab-active {
          background-color: #2f54eb;
        }
      }
      ::v-deep .ant-tabs.ant-tabs-left {
        background-color: #fff;
        .ant-tabs-left-bar {
          border-right: 10px solid #eef0f3;
        }
      }
    }
  }
</style>
