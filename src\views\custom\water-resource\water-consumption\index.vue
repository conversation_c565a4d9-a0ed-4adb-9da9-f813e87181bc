<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <!-- <a-form-item label="年份">
        <a-input v-model="queryParam.year" placeholder="年份" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item> -->
      <a-form-item label="年份">
        <!-- <a-select
          v-model="queryParam.year"
          placeholder="请选择"
          :options="yearOptions"
          showSearch
          allowClear
        ></a-select> -->
        <!-- @keyup.enter.native="handleQuery" -->
        <a-date-picker
          style="width: 200px"
          allow-clear
          :value="queryParam.year"
          format="YYYY"
          valueFormat="YYYY"
          mode="year"
          @panelChange="
            val => {
              queryParam.year = val
            }
          "
          placeholder="请选择"
        />
      </a-form-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormDrawer v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getAairportList, deleteAairport } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import { filter } from 'lodash'

  export default {
    name: 'WaterBackbonePlan',
    components: {
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    data() {
      return {
        isChecked: false,

        showForm: false,
        showFormDetails: false,

        list: [],
        tableTitle: '灌溉计划用水量',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          sort: [],
          year: null,
        },

        yearOptions: [],
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
          },
          {
            title: '年份',
            field: 'year',
            minWidth: 100,
          },
          {
            title: '一月(万m³)',
            field: 'january',
            minWidth: 100,
          },
          {
            title: '二月(万m³)',
            field: 'february',
            width: 120,
          },

          {
            title: '三月(万m³)',
            field: 'march',
            width: 120,
          },

          {
            title: '四月(万m³)',
            field: 'april',
            minWidth: 100,
          },
          {
            title: '五月(万m³)',
            field: 'may',
            width: 120,
          },

          {
            title: '六月(万m³)',
            field: 'june',
            width: 120,
          },

          {
            title: '七月(万m³)',
            field: 'july',
            minWidth: 100,
          },
          {
            title: '八月(万m³)',
            field: 'august',
            width: 120,
          },

          {
            title: '九月(万m³)',
            field: 'september',
            width: 120,
          },

          {
            title: '十月(万m³)',
            field: 'october',
            minWidth: 100,
          },
          {
            title: '十一月(万m³)',
            field: 'november',
            width: 120,
          },

          {
            title: '十二月(万m³)',
            field: 'december',
            width: 120,
          },

          {
            title: '操作',
            field: 'operate',
            width: 100,
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {},
    methods: {
      filterCounty(nodes) {
        if (!nodes) {
          return []
        }
        let countys = []
        for (let i = 0; i < nodes.length; i++) {
          let e = nodes[i]
          if (e.icon == '4') {
            countys.push(e)
          }
          let tmp = this.filterCounty(e.children)
          if (tmp.length > 0) {
            countys = countys.concat(tmp)
          }
        }
        return countys
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getAairportList(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response.data.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
        // this.names = valObj.records.map(item => item.planName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.queryParam.year = this.queryParam.year ? moment(this.queryParam.year).format('YYYY') : null
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          year: null,
          pageNum: 1,
          sort: [],
        }

        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteAairport({ ids: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
