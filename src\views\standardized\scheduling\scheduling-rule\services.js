import request from '@/utils/request'

// 调度规则-列表分页查询
export function getSchedulingPlanPage(data) {
  return request({
    url: '/prjstd/control/page',
    method: 'post',
    data,
  })
}
// 增加
export function addSchedulingPlan(data) {
  return request({
    url: '/prjstd/control/add',
    method: 'post',
    data,
  })
}
// 详情
export function getSchedulingPlan(params) {
  return request({
    url: '/prjstd/control/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editSchedulingPlan(data) {
  return request({
    url: '/prjstd/control/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteSchedulingPlan(params) {
  return request({
    url: '/prjstd/control/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
