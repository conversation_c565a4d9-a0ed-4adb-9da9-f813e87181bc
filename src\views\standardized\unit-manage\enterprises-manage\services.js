import request from '@/utils/request'

// 物业单位列表
export function getPropertyMgrList(params) {
  return request({
    url: '/custom/property-mgr/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 候选组织机构
export function getOrgOption(params) {
  return request({
    url: '/custom/property-mgr/org/option',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 添加物业单位
export function addPropertyMgr(params) {
  return request({
    url: '/custom/property-mgr/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 删除物业单位
export function deletePropertyMgr(params) {
  return request({
    url: '/custom/property-mgr/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 单位下的工程列表
export function getProjectList(params) {
  return request({
    url: '/custom/property-mgr/project/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程候选列表
export function getProjectOption(params) {
  return request({
    url: '/custom/property-mgr/project/option',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 添加工程
export function addProject(params) {
  return request({
    url: '/custom/property-mgr/project/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 删除工程
export function deleteProject(params) {
  return request({
    url: '/custom/property-mgr/project/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
