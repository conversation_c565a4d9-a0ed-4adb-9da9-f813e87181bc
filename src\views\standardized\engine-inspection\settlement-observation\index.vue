<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item :label="observationType == 1 ? '沉降观测名称' : '位移观测名称'">
        <a-input
          v-model="queryParam.observationName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="所属工程">
        <a-tree-select
          :disabled="isDetail"
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-item>

      <a-form-item label="年份">
        <a-date-picker
          mode="year"
          format="YYYY"
          v-model="queryParam.year"
          placeholder="请选择"
          allow-clear
          :open="yearShowOne"
          style="width: 100%"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
        ></a-date-picker>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="observationType == 1 ? columns1 : columns2"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          :isInModal="isDetail"
          :modalHeight="modalHeight"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :projectOptions="projectOptions"
          :observationType="observationType"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :projectOptions="projectOptions"
          :observationType="observationType"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getObservationPage, deleteObservation } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'SettlementObservation',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      type: {
        type: Number,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭

        observationType: this.type, //判断沉降观测、位移观测
        projects: [],
        projectOptions: [],
        isChecked: false,
        showForm: false,
        showFormDetails: false,
        archivesOptions: [],

        list: [],
        tableTitle: '沉降观测',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          observationName: '',
          observationType: 1,
          year: null,
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          sort: [],
        },
        columns1: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '沉降观测名称',
            field: 'observationName',
          },
          {
            title: '所属工程',
            field: 'projectName',
          },
          {
            title: '年份',
            field: 'year',
          },
          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 138,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    {!this.isDetail && (
                      <span>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleEdit(row)}>修改</a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete(row)}>删除</a>
                      </span>
                    )}
                  </span>
                )
              },
            },
          },
        ],
        columns2: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '位移观测名称',
            field: 'observationName',
          },
          {
            title: '所属工程',
            field: 'projectName',
          },
          {
            title: '年份',
            field: 'year',
          },
          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 138,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    {!this.isDetail && (
                      <span>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleEdit(row)}>修改</a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete(row)}>删除</a>
                      </span>
                    )}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      const url = window.location.href
      if (url.includes('displacement-observation')) {
        this.observationType = 2
      } else if (url.includes('settlement-observation')) {
        this.observationType = 1
      }
      this.tableTitle = this.observationType == 1 ? '沉降观测' : '位移观测'
      this.queryParam.observationType = this.observationType
      this.getList()

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
    },
    mounted() {},
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getObservationPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.observationId)
        this.names = valObj.records.map(item => item.observationName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          observationName: '',
          // observationType: null,
          year: null,
          pageNum: 1,
          projectId: null,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.observationId ? [row?.observationId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteObservation({ observationIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
