<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      :maskClosable="false"
      modalWidth="460"
      @cancel="cancel"
      modalHeight="400"
    >
      <div slot="content">
        <a-form-model ref="form" :model="form" :rules="rules">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="检查时间">
                {{ form.checkDate }}
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="检查报告">
                <!-- <UploadFile
                  :fileUrl.sync="form.attaches"
                  :multiple="false"
                  listType="picture-card"
                  folderName="projectCover"
                /> -->
                <!-- <UploadFile
                  v-if="form.reports"
                  :fileUrl.sync="form.reports"
                  :multiple="true"
                  disabled
                  listType="text"
                  folderName="projectCover"
                /> -->
                <div v-if="form.reports">
                  <div class="url" v-for="(item, index) in form.reports" :key="index" :title="item.attachName">
                    <a :href="item.attachUrl" :key="index" target="_blank">
                      {{ item.attachName }}
                    </a>
                  </div>
                </div>
                <div v-else>暂无数据</div>
              </a-form-model-item>
              <!-- <div class="upload-tip">仅限1张图片，png、jpg、jpeg格式，10M以内，建议图片比例：1:1。</div> -->
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script>
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'ViewModal',
    components: { AntModal, UploadFile },
    props: ['viewData'],
    data() {
      return {
        updateInterval: null,

        loading: false,
        modalLoading: false,
        currentLength: 0,

        formTitle: '',
        type: 0,

        previewEditType: 0,
        form: { checkDate: null, reports: null },
        open: false,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    beforeDestroy() {
      // 组件销毁前清除定时器
      if (this.updateInterval) {
        clearInterval(this.updateInterval)
      }
    },
    methods: {
      handleInputChange(value) {
        // 更新当前输入的字符长度
        this.currentLength = this.form.qrName.length
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      remove() {
        this.$refs.tinyMceRef.remove()
      },
      /** 新增按钮操作 */
      handlecView(row) {
        this.open = true
        this.formTitle = '检查报告查看'

        this.modalLoading = true
        console.log('row', row)
        //附件显示
        // this.form = { ...res.data, attaches: [res.data.qrUrl] }
        this.form.reports = row.reports ? row.reports : null
        this.form.checkDate = row.checkDate ? row.checkDate : null
        console.log('this.form 123', this.form)

        this.modalLoading = false
      },
      // 操作完成后
      onOperationComplete() {
        this.$emit('ok')
      },
    },
  }
</script>
<style lang="less" scoped>
  .url {
    width: 400px; /* 设置固定宽度 */
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 显示省略号 */
    color: #5384fe;
  }
</style>
