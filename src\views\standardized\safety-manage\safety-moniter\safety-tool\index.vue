<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <TreeGeneral
        style="width: 220px"
        ref="treeGeneralRef"
        :treeOptions="treeOptions"
        @onTreeMounted="onTreeMounted"
        @select="node => clickTreeNode(node, 'category')"
      />
      <!--:current-keys="treeSelectKey" v-if="treeOptions.dataSource.length" -->
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="名称">
          <a-input v-model="queryParam.toolName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
        </a-form-item>

        <a-form-item label="检查类型">
          <a-select
            show-search
            allow-clear
            placeholder="请选择"
            v-model="queryParam.toolType"
            option-filter-prop="children"
          >
            <a-select-option v-for="item in toolTypeOptions" :key="item.key" :value="item.key">
              {{ item.value }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- <a-form-item label="工程名称">
          <a-tree-select
            :disabled="isDetail"
            v-model="queryParam.projectId"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :tree-data="projectOptions"
            show-search
            treeNodeFilterProp="title"
            allowClear
            placeholder="请选择"
            :replaceFields="{
              children: 'children',
              title: 'projectName',
              key: 'projectId',
              value: 'projectId',
            }"
            tree-default-expand-all
          ></a-tree-select>
        </a-form-item> -->

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            :rowConfig="{ isCurrent: true, isHover: true }"
            :tablePage="{
              pageNum: queryParam.pageNum,
              pageSize: queryParam.pageSize,
              total,
            }"
            :isInModal="isDetail"
            :modalHeight="modalHeight"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button" v-if="!isDetail">
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete()">
                <a-icon type="delete" />
                删除
              </a-button>
              <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
            </div>
          </VxeTable>
          <FormDrawer
            v-if="showForm"
            ref="formRef"
            :toolTypeOptions="toolTypeOptions"
            :projectOptions="projectOptions"
            :toolStatusOptions="toolStatusOptions"
            :toolType="toolType"
            :currentProjectId="currentProjectId"
            @ok="onOperationComplete"
            @close="showForm = false"
          />
          <FormDetails
            v-if="showFormDetails"
            ref="formDetailsRef"
            :toolTypeOptions="toolTypeOptions"
            :projectOptions="projectOptions"
            :toolStatusOptions="toolStatusOptions"
            :toolType="toolType"
            @ok="onOperationComplete"
            @close="showFormDetails = false"
          />

          <FormExamine
            v-if="showFormExamine"
            :projectOptions="projectOptions"
            :toolStatusOptions="toolStatusOptions"
            :toolType="toolType"
            ref="formExamineRef"
            @ok="onOperationComplete"
            @close="showFormExamine = false"
          />
          <FormCheck
            v-if="showRecordCheckModal"
            :toolStatusOptions="toolStatusOptions"
            ref="recordCheckModalRef"
            @ok="onOperationComplete"
            @close="showRecordCheckModal = false"
          />
        </template>
      </VxeTableForm>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree, getProjectCategoryTree } from '@/api/common'
  import { getToolpage, deleteTool, remindTool } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormExamine from './modules/FormExamine.vue'
  import FormCheck from './modules/FormCheck.vue'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import TreeGeneral from '@/components/TreeGeneral'

  export default {
    name: 'SafetyInspection',
    components: { TreeGeneral, VxeTable, VxeTableForm, FormDetails, FormDrawer, FormExamine, FormCheck },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      type: {
        type: String,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        showRecordCheckModal: false,

        treeSelectKey: [],
        treeOptions: {
          getDataApi: getProjectCategoryTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'key',
          },
        },
        localUserId: '',
        toolType: 1,
        showFormExamine: false,

        toolStatusOptions: [
          { key: 1, value: '未判定' },
          { key: 2, value: '合格' },
          { key: 3, value: '不合格' },
        ],
        isChecked: false,
        toolTypeOptions: [],
        toolTypes: [],
        toolStatuses: [],
        planTypes: [],
        projects: [],
        projectType: '', //工程类型
        currentProjectId: null,
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        planTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '安全工器具',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          sort: [],
          toolName: '',
          toolType: undefined,
          type: 0,
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '名称',
            field: 'toolName',
            minWidth: 120,
            showOverflow: true,
          },
          {
            title: '规格型号',
            field: 'specifications',
            minWidth: 90,
            // slots: {
            //   default: ({ row, rowIndex }) => {
            //     return this.planTypes[row.planType]?.value || ''
            //   },
            // },
          },

          {
            title: '采购时间',
            minWidth: 120,
            field: 'dateTime',
          },
          {
            title: '所属类型',
            field: 'toolType',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.toolTypes[row.toolType]?.value || ''
              },
            },
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 90,
          },
          {
            title: '检查日期',
            field: 'checkDate',
            minWidth: 90,
          },
          {
            title: '检查结论',
            field: 'status',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                let statusPointColor = 'common-status-incomplete'
                if (row.status == 2) {
                  statusPointColor = 'common-status-completed'
                } else if (row.status == 3) {
                  statusPointColor = 'common-status-abnormal'
                }
                return (
                  <div class='common-status-box' style={{ cursor: 'pointer' }}>
                    <i class={['common-status-icon', statusPointColor]}></i>
                    <span onClick={() => this.handlecCheck(row)}>{this.toolStatuses[row.status]?.value}</span>
                    {/* <a onClick={() => this.handleRecordNews(row)}>{this.toolStatuses[row.status]?.value}</a> */}
                  </div>
                )
              },
            },
          },
          {
            title: '下一个检查日期',
            field: 'lastDateTime',
            minWidth: 170,
          },
          {
            title: '到期提醒',
            field: 'isRemind',
            minWidth: 90,
            titleHelp: { content: '请谨慎操作。' },
            slots: {
              default: ({ row, rowIndex }) => {
                // disabled={row.status == 1}
                return (
                  <span style='display:flex;align-items:center;'>
                    <a-switch
                      size='small'
                      checked={row.isRemind == 1}
                      onChange={val => this.onSwitchChange(val, row)}
                    />
                  </span>
                )
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 200,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleExamine(row)}>检查</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.toolType = this.$route.meta.query?.type || this.type

      // type	菜单(1安全工器具 2消防检查)

      this.queryParam.type = this.toolType
      this.tableTitle = this.toolType == 1 ? '安全工器具' : this.toolType == 2 ? '消防检查' : '安全工器具'

      /*检查类型
      toolSafeType 安全工器具 toolFireType 消防检查
      */
      this.localUserId = localStorage.getItem('userId')
      if (this.toolType == '1') {
        getOptions('toolSafeType').then(res => {
          this.toolTypeOptions = res.data
          this.toolTypes = getFlatTreeMap(this.toolTypeOptions, 'key')
        })
      } else if (this.toolType == '2') {
        getOptions('toolFireType').then(res => {
          this.toolTypeOptions = res.data
          this.toolTypes = getFlatTreeMap(this.toolTypeOptions, 'key')
        })
      }

      this.getList()

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })

      this.toolStatuses = getFlatTreeMap(this.toolStatusOptions, 'key')
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        // this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },

      // 树加载完成后
      onTreeMounted(data) {
        // this.queryParam.projectId = data[0].key.substr(1, data[0].key.length)
        this.queryParam.projectId = null
        this.projectType = data[0].type
        // this.projectId = data[0].key.substr(1, data[0].key.length)
        console.log('*** 3737 type***', data[0].type)
        // this.queryParam.treeNodeType = data[0].type
        // this.queryParam.districtCode = ''
        // this.tableTitle = data[0].name

        this.treeOptions.dataSource = data

        // 获取工程树
        // getProjectTree({ objectCategoryId: this.queryParam.treeNodeId }).then(res => {
        //   this.projectOptions = res.data
        // })
        this.getList()
      },
      clickTreeNode(node, type) {
        const key = node.$options.propsData.dataRef.key
        let code = key.substr(1, key.length)
        this.queryParam.projectId = code == 3 ? null : code
        this.projectType = node.$options.propsData.dataRef.type
        // this.projectId = key.substr(1, key.length)
        // this.queryParam.treeNodeType = node.$options.propsData.dataRef.type
        console.log('*** 3737 type***', node.$options.propsData.dataRef.type)
        // this.tableTitle = node.$options.propsData.dataRef.title
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getToolpage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.toolId)
        this.names = valObj.records.map(item => item?.toolName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.planTimes = []
        this.queryParam = {
          ...this.queryParam,
          toolName: '',
          toolType: undefined,
          pageNum: 1,
          projectId: undefined,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        if (this.projectType == 'data') {
          this.currentProjectId = this.queryParam.projectId
        } else {
          this.currentProjectId = null
        }
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 审核 */
      handleExamine(record) {
        this.showFormExamine = true
        this.$nextTick(() => this.$refs.formExamineRef.handle(record))
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        if (this.projectType == 'data') {
          this.currentProjectId = this.queryParam.projectId
        } else {
          this.currentProjectId = null
        }
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /* 状态记录 */
      handlecCheck(row, type) {
        this.showRecordCheckModal = true
        this.$nextTick(() => this.$refs.recordCheckModalRef.handlecCheck(row))
      },
      /* 是否到期提醒 */
      onSwitchChange(val, row) {
        const newsIds = row.toolId ? [row.toolId] : this.ids
        let isRemind = row.isRemind ? 0 : 1
        console.log(isRemind)
        this.confirmIns = this.$confirm({
          title: `确认${val ? '设置到期提醒' : '取消到期提醒'}?`,
          // content: '当前选中名称为"' + newsNames + '"的数据',
          content: row.isRemind ? '将会取消到期提醒,请谨慎操作。' : '将会设置到期提醒,请谨慎操作。',
          onOk: () => {
            remindTool({ toolId: row.toolId, isRemind: isRemind }).then(res => {
              this.$message.success('设置成功')
              this.getList()
            })
          },
          onCancel() {},
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.toolId ? [row?.toolId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteTool({ toolIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      margin-bottom: 5px;

      .ant-tabs-nav-container {
        height: auto;

        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
