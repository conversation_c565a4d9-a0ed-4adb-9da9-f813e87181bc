<template>
  <div>
    <InverseBarChart
      v-if="barChart.dataSource.length && !lineChart.dataSource.length"
      :dataSource="barChart.dataSource"
      :custom="barChart.custom"
      height="150px"
      :key="barChartKey"
    />

    <LineEchart
      v-if="lineChart.dataSource.length && !barChart.dataSource.length"
      :dataSource="lineChart.dataSource"
      :custom="lineChart.custom"
      height="250px"
      :key="lineChartKey"
    />
    <BarAndLineChart
      v-if="barChart.dataSource.length && lineChart.dataSource.length"
      :key="chartKey"
      :dataSource="[...barChart.dataSource, ...lineChart.dataSource]"
      :siteTerminalData="siteTerminalData"
      height="400px"
    />
  </div>
</template>

<script lang="jsx">
  import { LineEchart } from '@/components/Echarts'
  import InverseBarChart from '@/components/Echarts/InverseBarChart.vue'
  import BarAndLineChart from './barAndlineMix.vue'

  export default {
    name: 'AllCharts',
    components: { LineEchart, InverseBarChart, BarAndLineChart },
    props: ['dataSource', 'siteTerminalData'],
    data() {
      return {
        chartKey: 9999,
        barChartKey: 1,
        lineChartKey: 999,
        lineChart: {
          dataSource: [],
          custom: {
            shortValue: true,
            legend: { left: 'center' },
            rYLabel: '流量(m³/s)',
            yLabel: '水位(m)',
            showAreaStyle: false,
            dataZoom: false,
          },
        },
        barChart: {
          dataSource: [],
          custom: {
            shortValue: true,
            legend: { left: 'center' },
            yLabel: '雨量(mm)',
            rYLabel: '累计雨量(mm)',
            inverseTop: true,
          },
        },
      }
    },
    watch: {
      dataSource: {
        handler(newVal, oldVal) {
          this.chartKey += 1
          this.barChartKey += 1
          this.lineChartKey += 1

          this.barChart.dataSource = []
          this.lineChart.dataSource = []

          this.siteTerminalData
            .filter(el => el.indexCode != 'all')
            .forEach(el => {
              if (el.indexCode == 'rainfall') {
                this.barChart.dataSource = [
                  {
                    name: '雨量',
                    data: newVal.map(el => [el.dateTime, el.rain]),
                  },
                  {
                    name: '累计雨量',
                    yAxisIndex: 1,
                    type: 'line',
                    data: newVal.map(el => [el.dateTime, el.sumRain]),
                  },
                ]
              }

              if (el.indexCode == 'waterLevel') {
                this.lineChart.dataSource.push({
                  name: '水位',
                  data: newVal.map(el => [el.dateTime, el.waterLevel]),
                })
                this.lineChart.custom = {
                  ...this.lineChart.custom,
                  yLabel: '水位(m)',
                  rYLabel: false,
                }
              }

              if (el.indexCode == 'flow') {
                if (this.siteTerminalData.some(item => item.indexCode == 'waterLevel')) {
                  this.lineChart.dataSource.push({
                    name: '流量',
                    yAxisIndex: 1,
                    data: newVal.map(el => [el.dateTime, el.flow]),
                  })

                  this.lineChart.custom = {
                    ...this.lineChart.custom,
                    rYLabel: '流量(m³/s)',
                    yLabel: '水位(m)',
                  }
                } else {
                  this.lineChart.dataSource.push({
                    name: '流量',
                    data: newVal.map(el => [el.dateTime, el.flow]),
                  })
                  this.lineChart.custom = {
                    ...this.lineChart.custom,
                    yLabel: '流量(m³/s)',
                    rYLabel: false,
                  }
                }
              }
            })

          // this.lineChart.dataSource = [
          //   {
          //     name: '流量',
          //     data: newVal.map(el => [el.dateTime, el.flow])
          //   },
          //   {
          //     name: '水位',
          //     yAxisIndex: 1,
          //     data: newVal.map(el => [el.dateTime, el.waterLevel])
          //   }
          // ]
        },
        deep: true,
      },
    },
    created() {},
    methods: {},
  }
</script>

<style lang="less" scoped></style>
