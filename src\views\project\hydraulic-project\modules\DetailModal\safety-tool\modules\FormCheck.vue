<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      modalWidth="1100"
      @cancel="cancel"
      modalHeight="700"
    >
      <!-- bordered         -->
      <div slot="content">
        <a-table
          style="overflow-x: hidden !important"
          :columns="columns"
          rowKey="statusRecordId"
          :loading="loading"
          :data-source="list"
          :pagination="false"
          :scroll="{ y: 400 }"
        ></a-table>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
      </template>
    </ant-modal>
    <!--  报告附件显示-->
    <ViewModal v-if="showViewModal" ref="recordViewModalRef" @close="showViewModal = false" />
  </div>
</template>
<script lang="jsx">
  import { checkPage } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'
  import ViewModal from './ViewModal.vue'

  export default {
    name: 'FormCheck',
    components: { AntModal, ViewModal },
    props: ['projectOptions', 'inspectionTypeOptions', 'planTypeOptions', 'toolStatusOptions'],
    data() {
      return {
        showViewModal: false,
        tableTitle: '',
        loading: false,
        modalLoading: false,
        list: [],
        formTitle: '',
        open: false,
        operateOptions: [
          { value: 1, label: '保存' },
          { value: 2, label: '发布' },
          { value: 3, label: '撤回' },
        ],
        columns: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            align: 'center',
            width: '55px',
          },
          {
            title: '检查日期',
            dataIndex: 'checkDate',
            // align: 'center',
            width: '186px',
            minWidth: '186px',
            // ellipsis: true
          },
          {
            title: '检查人员',
            dataIndex: 'createdUserName',
            // align: 'center',
            width: '200px',
            minWidth: '200px',
            ellipsis: true,
          },
          {
            title: '查看报告',
            dataIndex: 'operate',
            // align: 'left',
            width: '100px',
            minWidth: '100px',
            // ellipsis: true,
            customRender: (v, record, i) => {
              return (
                <a onClick={() => this.handlecView(record)} target='_blank'>
                  {' '}
                  查看{' '}
                </a>
              )
            },
          },

          {
            title: '检查结论',
            dataIndex: 'status',
            // align: 'center',
            width: '100px',
            minWidth: '100px',
            // ellipsis: true,
            customRender: (v, record, i) => {
              return <div> {this.statusFormat(record.status)} </div>
            },
          },
          {
            title: '情况说明',
            dataIndex: 'content',
            align: 'center',
            width: '186px',
            minWidth: '186px',
            ellipsis: true,
          },
        ],
        queryParam: {
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          toolId: null,
          sort: [],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      operateFormat(value) {
        if (value) {
          return this.operateOptions.find(item => item.value == value)?.label
        } else {
          return '-'
        }
      },
      statusFormat(value) {
        if (value) {
          return this.toolStatusOptions.find(item => item.key == value)?.value
        } else {
          return '-'
        }
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handlecView(row) {
        this.showViewModal = true
        this.$nextTick(() => this.$refs.recordViewModalRef.handlecView(row))
      },
      /** 新增按钮操作 */
      handlecCheck(row) {
        this.open = true
        if (row != undefined) {
          this.modalLoading = true
          this.loading = true
          this.formTitle = '检查记录'
          this.queryParam.toolId = row.toolId
          checkPage(this.queryParam).then(res => {
            if (res.code == 200) {
              this.list = res.data?.data
              this.modalLoading = false
              this.loading = false
            }
          })
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
</style>
