import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/custom/water/charge/page',
    method: 'post',
    data,
  })
}

export function getYear() {
  return request({
    url: '/custom/water/charge/getYearList',
    method: 'post',
  })
}

// 增加
export function addWaterCharge(data) {
  return request({
    url: '/custom/water/charge/add',
    method: 'post',
    data,
  })
}
// 详情
export function getWaterChargeById(params) {
  return request({
    url: '/custom/water/charge/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editWaterCharge(data) {
  return request({
    url: '/custom/water/charge/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteWaterCharge(params) {
  return request({
    url: '/custom/water/charge/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
