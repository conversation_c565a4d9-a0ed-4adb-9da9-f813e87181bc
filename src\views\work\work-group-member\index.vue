<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <TreeCategory
        style="width: 220px"
        :treeOptions="treeOptions"
        v-if="treeOptions.dataSource.length"
        ref="treeGeneralRef"
        @onTreeMounted="onTreeMounted"
        @select="clickTreeNode"
      />
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="姓名">
          <a-input v-model="queryParam.name" placeholder="请输入姓名" allow-clear @keyup.enter.native="handleQuery" />
        </a-form-item>

        <a-form-item label="岗位">
          <a-select v-model="queryParam.postId" allowClear placeholder="请选择">
            <a-select-option v-for="(d, index) in postList" :key="index" :value="d.postId">
              {{ d.postName }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="arrow-up" />
                选择人员
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>

    <select-user
      selectModel="multi"
      v-model="selectedUser"
      v-show="false"
      @ok="onOperationComplete"
      ref="selectUserRef"
    />
  </div>
</template>

<script lang="jsx">
  import { getObjectCategoryTree, getObjectCategoryPage } from './services'
  import { saveRoleUser, getPostData, deleteGroupUser } from './services'
  import FormDrawer from './modules/FormDrawer'
  // import TreePatrolCategory from '@/components/TreePatrolCategory/item.vue'
  import TreeCategory from './components/TreeCategory.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import SelectUser from '@/components/pt/selectUser/SelectUser' //选择人员

  export default {
    name: 'ObjectCategory',
    components: {
      VxeTableForm,
      VxeTable,
      TreeCategory,
      FormDrawer,
      SelectUser,
    },
    data() {
      return {
        treeOptions: {
          dataSource: [],
          replaceFields: {
            name: 'name',
            children: 'children',
            title: 'name',
            key: 'treeKey',
            value: 'name',
          },
        },
        showFormDrawer: false,
        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          deptId: null,
          groupId: null,
          name: '',
          pageNum: 1,
          pageSize: 10,
          postId: null,
          shiftId: null,
          sort: [],
        },
        postList: [], //岗位列表
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '班组',
            field: 'groupName',
            minWidth: 160,
          },
          {
            title: '姓名',
            field: 'name',
            minWidth: 100,
          },
          {
            title: '岗位',
            field: 'postName',
            minWidth: 180,
          },
          {
            title: '部门',
            field: 'deptName',
            minWidth: 240,
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
        selectedUser: '',
        currentSelectRoleId: '',
        isselectuser: null,
      }
    },
    computed: {},
    watch: {
      selectedUser(val) {
        let userIds = val.ids
        let params = {
          groupId: this.queryParam.groupId,
          userIds: userIds.split(','),
        }
        if (!this.queryParam.groupId) {
          this.$message.warn('班组id不能为空', 3)
          return
        }
        saveRoleUser(params).then(response => {
          this.$message.success('添加成功', 3)
          this.getRoleUserListByRoleId(this.currentSelectRoleId)
          this.getList()
        })
      },
    },

    beforeDestroy() {
      clearInterval(this.refreshData)
      this.refreshData = null
    },
    created() {
      getObjectCategoryTree().then(res => {
        this.treeOptions.dataSource = (res.data || []).map((ele, index) => ({
          ...ele,
          name: ele.shiftName,
          treeKey: ele.shiftName + ele.shiftId + index,
          children: ele.groups.map(el => ({
            ...el,
            name: el.groupName,
            treeKey: el.groupName + el.groupId + ele.shiftId + index,
            isLeaf: true,
          })),
        }))
      })

      this.getpostList()
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        //treeNodeId
        this.queryParam.pageSize = pageSize
        if (this.queryParam.shiftId) {
          this.getList()
        }
      },
      //查询岗位列表
      getpostList() {
        getPostData().then(response => {
          this.postList = response.data
        })
      },

      getRoleUserListByRoleId(roleId) {
        this.subLoading = true
        if (!roleId) {
          return
        } else {
          this.currentSelectRoleId = roleId
          roleId = typeof roleId == 'string' ? '10000' : roleId
          this.querySubParam.roleId = roleId
        }

        if (this.isAdd == 1) {
          getRoleUserList(this.querySubParam).then(response => {
            this.subList = response.data.data
            this.subTotal = response.data.total
            this.subLoading = false
          })
        } else if (this.isAdd == 2) {
          this.subList = []
          this.subLoading = false
          this.subTotal = 0
        }
        this.subIds = []
        this.selectedSubRowKeys = []
        this.subMultiple = true
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getObjectCategoryPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          objectCategoryCode: undefined,
          objectCategoryName: undefined,
          pageNum: 1,
          sort: [],
          postId: null,
          name: '',
        }
        this.handleQuery()
      },
      // 树加载完成后
      onTreeMounted(data) {
        // console.log('树加载完后', data)
        this.queryParam.shiftId = data[0].shiftId
        this.tableTitle = data[0].title
        this.getList()
      },

      // 操作完成后
      onOperationComplete(row, params) {
        this.getList()
        this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.groupUserId)
        this.names = valObj.records.map(item => item.name)
        this.isChecked = !!valObj.records.length
      },

      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      //树点击事件
      clickTreeNode(node) {
        // console.log('clickTreeNode', node)
        this.isselectuser = node.$options.propsData.dataRef.children
        this.queryParam.shiftId = node.$options.propsData.dataRef.shiftId
        this.queryParam.groupId = node.$options.propsData.dataRef.groupId
        this.tableTitle = node.$options.propsData.dataRef.title
        this.queryParam.pageNum = 1
        this.getList()
      },

      //选择人员
      handleAdd() {
        // console.log(this.isselectuser)
        if (this.isselectuser == undefined) {
          this.$nextTick(() => this.$refs.selectUserRef.showSelectUser())
        } else {
          this.$message.warning('' + this.tableTitle + '班次无法选择人员，请选择对应班组！')
        }
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const groupUserIds = row.groupUserId ? [row.groupUserId] : this.ids
        const names = row.name || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteGroupUser({ groupUserIds: groupUserIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.getList()
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
