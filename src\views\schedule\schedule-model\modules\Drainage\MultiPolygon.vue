<template></template>

<script setup lang="jsx">
  import { ref, reactive, onMounted, computed, watch, nextTick, useAttrs } from 'vue'
  import { MapboxOverlay } from '@deck.gl/mapbox'
  import { GeoJsonLayer } from '@deck.gl/layers'
  import { hexToRgb } from '@/utils/toDeckglRgb.js'
  // import { mapBoundGeo } from '@/utils/mapBounds.js'

  const props = defineProps(['geojson', 'mapIns', 'checkedList'])
  const attrs = useAttrs()
  const stateRef = ref({
    deckOverlay: null,
  })

  stateRef.value.deckOverlay = new MapboxOverlay({
    id: 'deck-geojson-layer-overlay',
    layers: [],
  })
  props.mapIns.addControl(stateRef.value.deckOverlay)

  const updateProps = () => {
    stateRef.value.deckOverlay.setProps({
      layers: [
        new GeoJsonLayer({
          id: 'geojson-layer-multi-polygon',
          data: JSON.parse(JSON.stringify(props.geojson)),
          filled: true,
          pickable: false,
          stroked: false,

          getFillColor: d => {
            if (props.checkedList.includes(d.properties.gridcode)) {
              return [...hexToRgb(attrs.colorAndLabelMap[d.properties.gridcode].color), 255]
            }

            // 透明
            return [...hexToRgb('#ffffff'), 0]
          },
        }),
      ],
    })
  }

  watch(
    () => props.geojson,
    newVal => {
      nextTick(() => {
        updateProps()
      })
    },
  )

  watch(
    () => props.checkedList,
    newVal => {
      nextTick(() => {
        updateProps()
      })
    },
  )
</script>

<style scoped lang="less"></style>
