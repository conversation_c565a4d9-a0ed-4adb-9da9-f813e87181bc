<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="土壤湿度矢量图层" prop="humidityUrl">
                <!-- <a-input allowClear v-model="form.humidityUrl" placeholder="请输入" /> -->
                <a-select
                  v-model="form.humidityUrl"
                  allowClear
                  placeholder="请选择"
                  :options="geoServerList"
                  show-search
                  :filter-option="filterOption"
                ></a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="含水率矢量图层" prop="moistureUrl">
                <!-- <a-input allowClear v-model="form.moistureUrl" placeholder="请输入" /> -->
                <a-select
                  v-model="form.moistureUrl"
                  allowClear
                  placeholder="请选择"
                  :options="geoServerList"
                  show-search
                  :filter-option="filterOption"
                ></a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="预警时间" prop="warnDate">
                <a-date-picker v-model="form.warnDate" show-time />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'
  import { addDamageWarn, updateDamageWarn } from '../services'

  export default {
    name: 'FormModal',
    props: {
      geoServerList: {
        type: Array,
        default: () => [],
      },
    },
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        // 默认密码
        formTitle: '',
        // 表单参数
        form: {
          id: undefined,
          humidityUrl: undefined,
          moistureUrl: undefined,
          warnDate: undefined,
        },
        open: false,

        rules: {},
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      handleShow(row) {
        this.open = true
        this.formTitle = row ? '修改' : '新增'
        if (row) {
          this.form = {
            ...row,
            warnDate: row?.warnDate ? moment(row?.warnDate) : undefined,
          }
        }
      },
      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true

            if (this.form.id) {
              updateDamageWarn({ ...this.form, warnDate: this.form.warnDate.format('YYYY-MM-DD HH:mm:ss') })
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(err => (this.loading = false))
            } else {
              addDamageWarn({ ...this.form, warnDate: this.form.warnDate.format('YYYY-MM-DD HH:mm:ss') })
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(err => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
