import request from '@/utils/request'

// 列表分页查询
export function getRiskPage(data) {
  return request({
    url: '/prjstd/risKs/page',
    method: 'post',
    data,
  })
}
// 增加
export function addRisk(data) {
  return request({
    url: '/prjstd/risKs/add',
    method: 'post',
    data,
  })
}
// 详情
export function getRisk(params) {
  return request({
    url: '/prjstd/risKs/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function updateRisk(data) {
  return request({
    url: '/prjstd/risKs/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteRisk(params) {
  return request({
    url: '/prjstd/risKs/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
