<template>
  <div class="query-box">
    <div class="card-filter"></div>
    <div class="query-row">
      <i class="circle"></i>
      河道水位&lt;3m
    </div>
    <div class="query-row">
      <i class="circle alert"></i>
      河道水位≥3m&&lt;5.4m
    </div>
    <div class="query-row">
      <i class="circle ensure"></i>
      河道水位≥5.4m
    </div>
  </div>
</template>
<script lang="jsx">
  export default {
    name: 'Describe',

    methods: {},
  }
</script>

<style lang="less" scoped>
  .query-box {
    width: 174px;
    height: 84px;
    position: absolute;
    top: 30px;
    right: 3px;
    font-weight: 400;
    font-size: 14px;
    padding-top: 8px;
    z-index: 222;
    color: #fff;
    border-radius: 6px;
    background: rgba(22, 45, 72, 0.84);
    .query-row {
      width: 100%;
      margin-left: 7px;
      .circle {
        width: 15px;
        height: 2px;
        vertical-align: middle;
        display: inline-block;
        background: #00d3ff;
        &.alert {
          background: #ffd52f;
        }
        &.ensure {
          background: #f61441;
        }
      }
    }
  }
</style>
