import request from '@/utils/request'

// 工程管理_维修养护_维养计划-列表分页查询
export function getPlansPage(data) {
  return request({
    url: '/prjstd/plans/page',
    method: 'post',
    data,
  })
}
// 增加
export function addPlans(data) {
  return request({
    url: '/prjstd/plans/add',
    method: 'post',
    data,
  })
}
// 详情
export function getPlansById(params) {
  return request({
    url: '/prjstd/plans/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editPlans(data) {
  return request({
    url: '/prjstd/plans/update',
    method: 'post',
    data,
  })
}
// 删除
export function deletePlans(params) {
  return request({
    url: '/prjstd/plans/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

export function getProjectListByPropertyId(params) {
  return request({
    url: '/prjstd/maintenance/getProjectList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
