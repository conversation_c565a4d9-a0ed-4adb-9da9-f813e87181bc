<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :modalWidth="windowSize.width"
    :modalHeight="windowSize.height"
    @cancel="cancel"
    @onFullScreen="onFullScreen"
    :footer="null"
    ref="detailModalRef"
  >
    <div slot="content" layout="vertical" class="detail-content">
      <div class="detail-tab">
        <a-tabs v-model="tabVal" type="line" :tab-position="'left'" :style="{ height: '100%' }" @change="onTabChange">
          <a-tab-pane
            v-for="(ele, i) in displayInfoOptions"
            :key="ele.key"
            :tab="projectInfo.includes(ele.key) ? ele.option1 : ele.value"
          >
            <!-- 河流 -->
            <River
              v-if="ele.key === 'riverInfoRL002'"
              :displayCode="ele.key"
              :riverSystemId="recordInfo.riverSystemId"
            />

            <!-- 流域 -->
            <Catchment
              v-if="ele.key === 'riverInfoRL001'"
              :displayCode="ele.key"
              :riverSystemId="recordInfo.riverSystemId"
            />

            <!-- 湖泊 -->
            <Lake
              v-if="ele.key === 'riverInfoRL003'"
              :displayCode="ele.key"
              :riverSystemId="recordInfo.riverSystemId"
            />

            <!-- 划界确权 -->
            <Demarcation
              ref="demarcationRef"
              v-if="ele.key === 'demarcation'"
              :displayCode="ele.key"
              :projectId="recordInfo.riverSystemId"
            />

            <!-- 工程文件预览 -->
            <FileViewList
              v-if="
                ele.key === 'appearance' ||
                ele.key === 'registration' ||
                ele.key === 'safetyAppraisal' ||
                ele.key === 'label'
              "
              :displayCode="ele.key"
              :projectId="recordInfo.riverSystemId"
            />

            <!-- 管理单位 -->
            <ManageUnit
              v-if="ele.key === 'unitManagement'"
              :displayCode="ele.key"
              :projectId="recordInfo.riverSystemId"
              :projectName="modalTitle"
            />

            <!-- 应急管理 -->
            <EmergencyManage
              v-if="ele.key === 'emergencyResponse'"
              :displayCode="ele.key"
              :projectId="recordInfo.riverSystemId"
            />

            <!-- 安全管理 -->
            <SafetyManage
              v-if="ele.key === 'safetyInspection'"
              :displayCode="ele.key"
              :projectId="recordInfo.riverSystemId"
            />

            <!-- 工程检查 -->
            <EngineInspection
              v-if="ele.key === 'inspectionEngineering'"
              :displayCode="ele.key"
              :projectId="recordInfo.riverSystemId"
            />

            <!-- 控制运行 -->
            <Scheduling
              v-if="ele.key === 'controlOperation'"
              :displayCode="ele.key"
              :projectId="recordInfo.riverSystemId"
            />

            <!-- 维修养护 -->
            <MaintenanceUpkeep
              v-if="ele.key === 'maintenance'"
              :displayCode="ele.key"
              :projectId="recordInfo.riverSystemId"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import Demarcation from './Demarcation.vue'
  import FileViewList from './FileViewList.vue'

  import River from './river/index.vue'
  import Catchment from './catchment/index.vue'
  import Lake from './lake/index.vue'

  import ManageUnit from './manageUnit/index.vue'
  import EmergencyManage from './emergency-manage/index.vue'
  import SafetyManage from './safety-manage/index.vue'
  import EngineInspection from './engine-inspection/index.vue'
  import Scheduling from './scheduling/index.vue'
  import MaintenanceUpkeep from './maintenance-upkeep/index.vue'

  import { getDisplayCodes } from '../../services'
  import { getOptions } from '@/api/common'

  export default {
    name: 'CreateForm',
    props: {},
    components: {
      AntModal,
      Demarcation,
      FileViewList,

      River,
      Catchment,
      Lake,

      ManageUnit,
      EmergencyManage,
      SafetyManage,
      EngineInspection,
      Scheduling,
      MaintenanceUpkeep,
    },
    data() {
      return {
        open: false,
        modalTitle: '',
        tabVal: '',
        windowSize: {},
        displayInfoOptions: [],
        recordInfo: {},
        projectInfo: ['riverInfoRL001', 'riverInfoRL002', 'riverInfoRL003'], // 基本信息key集合
      }
    },
    created() {
      this.windowSize = {
        width: `${parseInt(window.innerWidth * 0.9)}`,
        height: `${parseInt(window.innerHeight * 0.95)}`,
      }
    },
    computed: {},
    watch: {},
    methods: {
      onTabChange(val) {
        this.tabVal = val
      },

      /** 打开 */
      handleDetail(record) {
        this.modalTitle = record.riverSystemName
        this.recordInfo = record

        getDisplayCodes({ riverSystemId: record?.riverSystemId }).then(resp => {
          console.log('*211 getDisplayCodes', resp)
          if (!resp.data?.length) {
            this.$message.info('无展示信息')
            return
          }
          this.open = true
          getOptions('displayInfoTypes').then(res => {
            this.displayInfoOptions = [...this.displayInfoOptions, ...res.data.filter(el => resp.data.includes(el.key))]
            console.log('*218 this.displayInfoOptions', this.displayInfoOptions)

            this.tabVal = this.displayInfoOptions[0].key
          })
        })
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      onFullScreen() {
        this.$refs.demarcationRef?.[0].onFullScreen()
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 10px;
    background-color: #eef0f3 !important;
    border-radius: 0 0 4px 4px;
    .modal-content {
      width: 100%;
      height: 100%;
    }
  }

  .detail-content {
    width: 100%;
    height: 100%;
    position: relative;

    .detail-tab {
      height: 100%;

      ::v-deep .ant-tabs-content {
        height: 100%;
        padding-left: 0px;
        border-left: none;
        .ant-tabs-tabpane-active {
          height: 100%;
        }
      }
      ::v-deep .ant-tabs.ant-tabs-left {
        background-color: #fff;
        .ant-tabs-left-bar {
          border-right: 10px solid #eef0f3;
        }
      }
    }
  }
</style>
./polder/index.vue
