<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="onTabChange">
        <a-tab-pane key="1" tab="按站点">
          <SingleTree
            v-if="treeTabKey === '1' && !!categoryTreeOptions.dataSource.length"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="categoryTreeOptions"
            :currentKeys="currentKeys"
            @onTreeMounted="onTreeMounted"
            @select="(keys, item) => clickTreeNode(keys, item)"
          />
        </a-tab-pane>

      </a-tabs>
    </div>

    <div class="tree-table-right-panel">
      <RainWaterLine v-if="site && site.key" :site="site" />
    </div>
  </div>
</template>

<script lang="jsx">
  import { getCategoryTree, getRiverSiteTree, getDistrictSiteTree, getRealTimeList } from './services'
  import SingleTree from '@/components/TreeGeneral/singleTree.vue'
  import RainWaterLine from './modules/RainWaterLine/index.vue'

  export default {
    name: 'ProcessLine',
    components: {
      SingleTree,
      RainWaterLine,
    },
    data() {
      return {
        routeType: 1,
        treeTabKey: '1',
        categoryTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'id',
          },
        },
        currentKeys: [],
        site: null,
      }
    },
    created() {
      const url = window.location.href
      if (url.includes('comprehensive-monitoring/multi-process-line')) {
        this.routeType = 2
      } else if (url.includes('water-rain/process-line')) {
        this.routeType = 1
      }
      let param = { labels: this.routeType }
      getCategoryTree(param).then(res => {
        this.categoryTreeOptions.dataSource = res.data
      })

    
    },
    computed: {},
    watch: {},
    methods: {
      // 树加载完成后
      onTreeMounted(selectedKeys, item) {
        this.currentKeys = selectedKeys
        this.site = { ...item, key: item.key.slice(1, item.key.length) }
      },
      clickTreeNode(selectedKeys, item) {
        this.currentKeys = selectedKeys
        this.site = { ...item, key: item.key.slice(1, item.key.length) }
      },

      onTabChange(key) {
        this.treeTabKey = key
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
