<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="危险源名称" prop="riskName">
              <a-input v-model="form.riskName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="危险类型" prop="riskType">
              <a-select placeholder="请输入" v-model="form.riskType" :options="riskTypeOptions"></a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="危险等级" prop="riskLevel">
              <a-select placeholder="请输入" v-model="form.riskLevel" :options="riskLevelOptions"></a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="危险区域" ref="riskAddress" prop="riskAddress">
              <a-input allowClear v-model="form.riskAddress" placeholder="请输入">
                <a-icon
                  style="font-size: 20px; cursor: pointer"
                  @click="onMapOpen"
                  slot="addonAfter"
                  type="environment"
                  theme="twoTone"
                />
              </a-input>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="日期" prop="riskDate">
              <a-date-picker
                v-model="form.riskDate"
                format="YYYY-MM-DD"
                placeholder="请选择"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="照片">
              <UploadFile
                :fileUrl.sync="form.phoneAttaches"
                :multiple="true"
                listType="picture-card"
                folderName="safety-manage"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="附件" prop="positionAttaches">
              <UploadFile
                :fileUrl.sync="form.positionAttaches"
                :multiple="true"
                listType="text"
                folderName="check-report"
              />
            </a-form-model-item>
          </a-col>
        </a-row>

        <MapModal ref="mapModalRef" @close="showMapModal = false" @confirm="onMapModalConfirm" />
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addRisk, updateRisk, getRisk } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'
  import MapModal from '@/components/MapBox/MapboxModal.vue'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile, MapModal },
    props: ['projectOptions', 'riskTypeOptions', 'riskLevelOptions'],
    data() {
      return {
        showMapModal: false,

        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          id: undefined,
          riskName: undefined,
          riskType: undefined,
          riskLevel: undefined,
          riskAddress: undefined,
          projectId: undefined,
          riskDate: undefined,
          phoneAttaches: [],
          positionAttaches: [],
        },
        open: false,
        rules: {
          riskName: [{ required: true, message: '危险源名称不能为空', trigger: 'blur' }],
          riskType: [{ required: true, message: '危险类型不能为空', trigger: 'change' }],
          riskLevel: [{ required: true, message: '危险等级不能为空', trigger: 'change' }],
          riskAddress: [{ required: true, message: '危险区域不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
          riskDate: [
            {
              required: true,
              message: '日期不能为空',
              trigger: 'change',
            },
          ],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 打开地图
      onMapOpen() {
        this.showMapModal = true
        const mapInfo = {
          longitude: undefined,
          latitude: undefined,
          location: this.form.riskAddress,
        }
        this.$nextTick(() => this.$refs.mapModalRef.handleOpen(mapInfo))
      },
      onMapModalConfirm(mapInfo) {
        this.form.riskAddress = mapInfo.location
        this.$refs.riskAddress.onFieldChange()
        this.showMapModal = false
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        this.form.id = row?.id
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getRisk({ id: row.id }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                phoneAttaches: res.data.phoneAttaches?.map(el => el.attachUrl),
                positionAttaches: res.data.positionAttaches?.map(el => el.attachUrl),
              }

              this.modalLoading = false
            }
          })
        }
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const params = {
              ...this.form,
              riskDate: moment(this.form.riskDate).format('YYYY-MM-DD'),
            }
            if (!!this.form.id) {
              updateRisk(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('close')
                    this.$emit('ok')
                  }
                })
                .finally(() => (this.loading = false))
            } else {
              addRisk(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('close')
                    this.$emit('ok')
                  }
                })
                .finally(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
