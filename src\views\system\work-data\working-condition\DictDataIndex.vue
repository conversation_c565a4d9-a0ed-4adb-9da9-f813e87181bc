<template>
  <div>
    <a-card :bordered="false">
      <a-table
        :loading="loading"
        rowKey="dictDataId"
        size="middle"
        :columns="columns"
        :data-source="list"
        @refresh="getList"
        :pagination="false"
      >
        <span slot="createTime" slot-scope="text, record">
          {{ parseTime(record.createdTime) }}
        </span>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="jsx">
  export default {
    inject: ['reload'],
    name: 'DictData',
    props: {
      dictCode: {
        type: String,
        require: true
      },
      title: {
        type: String,
        default: '子表'
      },
      refreshData: {
        type: Number,
        require: true
      },
      sonData: {
        type: Array,
        require: true
      }
    },
    components: {},
    data() {
      return {
        showAddSubModal: false,
        showEditSubModal: false,
        list: [],
        selectedRowKeys: [],
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        ids: [],
        dictLabels: [],
        loading: false,
        total: 0,
        dictId: 0,
        // 状态数据字典
        statusOptions: [
          { dictLabel: '正常', dictValue: '0', isDisabled: '0' },
          { dictLabel: '停用', dictValue: '1', isDisabled: '1' }
        ],
        queryDictCode: undefined,
        columns: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
          },
          {
            title: '监测指标',
            dataIndex: 'indexName',
            ellipsis: true,
          },
          {
            title: '监测值',
            dataIndex: 'indexValue',
            ellipsis: true,
          },
          {
            title: '最后上传时间',
            dataIndex: 'dateTime',
            ellipsis: true,
          },
          {
            title: '通讯状态',
            dataIndex: 'trafficStatus',
            ellipsis: true,
            // scopedSlots: { customRender: 'name' },
            customRender: text => {
              return (
                  <div class='common-status-box'>
                    <i class={['common-status-icon', text == 1 ? 'common-status-completed' : 'common-status-abnormal']}></i>
                    <span>{text === 1 ? '正常' : '故障'}</span>
                  </div>
                )
              return text === 1 ? '正常' : '故障'
            }
          }
        ]
      }
    },
    filters: {},
    created() {
      this.queryDictCode = this.dictCode
      this.getList()
      // this.getDicts("sys_normal_disable").then((response) => {
      //   this.statusOptions = response.data;
      // });
    },
    computed: {},
    watch: {},
    methods: {
      getList() {
        // if (this.refreshData == 2 || this.refreshData == 3) {
        //   this.reload
        //   this.list = []
        // }
        // let tempDictCode = ''
        // this.showAddSubModal = false
        // this.showEditSubModal = false
        // this.loading = true
        // tempDictCode = this.queryDictCode
        // listData(tempDictCode).then(response => {
        //   this.list = response.data
        //   this.loading = false
        // })
        for (let i = 0; i < this.sonData.length; i++) {
          if (this.queryDictCode == this.sonData[i].deviceCode) {
            this.list = this.sonData[i].indexDetails
          }
        }
      },
      // 状态字典翻译
      statusFormat(row) {
        return this.selectDictLabel(this.statusOptions, row.status)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      handleSubAdd() {
        this.showAddSubModal = true
        this.$nextTick(() => this.$refs.dictDataAddForm.handleAdd())
      },
      handleSubUpdate(record) {
        this.showEditSubModal = true
        this.$nextTick(() => this.$refs.dictDataEditForm.handleUpdate(record))
      },

      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        this.ids = this.selectedRows.map(item => item.dictDataId)
        this.dictLabels = this.selectedRows.map(item => item.dictValue)
        this.single = selectedRowKeys.length !== 1
        this.multiple = !selectedRowKeys.length
      }
    }
  }
</script>
