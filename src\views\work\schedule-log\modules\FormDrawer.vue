<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="600"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="类别" prop="logType">
                <a-select
                  v-model="form.logType"
                  allowClear
                  :options="logTypeOptions.filter(el => el.value)"
                  placeholder="请选择"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="时间" prop="logDate">
                <a-date-picker
                  v-model="form.logDate"
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  style="width: 100%"
                  @change="changeLogDate"
                />
              </a-form-model-item>
            </a-col>
            <!-- <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="标题" prop="logTitle">
                <a-input allowClear v-model="form.logTitle" placeholder="请输入" />
              </a-form-model-item>
            </a-col> -->

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="班组" prop="groupId">
                <a-select
                  v-model="form.groupId"
                  allowClear
                  :options="groupOptions"
                  @change="changeLogGroup"
                  placeholder="请选择"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="班次" prop="shiftId">
                <a-select v-model="form.shiftId" allowClear :options="shiftOptions" placeholder="请选择" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="日志" prop="logContent">
                <a-textarea v-model="form.logContent" :maxLength="200" rows="5" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="附件">
                <UploadFile
                  :fileUrl.sync="form.urls"
                  :multiple="true"
                  listType="picture-card"
                  folderName="schedule-log"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions, getSysUserPage } from '@/api/common'
  import { workLogAdd, workLogUpdate, getWorkLogGroups, getWorkLogShifts } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'FormDrawer',
    props: ['logTypeOptions'],
    components: { AntModal, UploadFile },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,

        // 默认密码
        formTitle: '',
        groupOptions: [],
        shiftOptions: [],
        // 表单参数
        form: {
          logId: undefined,
          logDate: moment().format('yyyy-MM-DD'),
          // logTitle: undefined,
          logType: undefined,
          groupId: null,
          shiftId: null,
          logContent: undefined,
          urls: []
        },
        open: false,
        rules: {
          logDate: [{ required: true, message: '时间不能为空', trigger: 'change' }],
          // logTitle: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
          logType: [{ required: true, message: '类别不能为空', trigger: 'change' }],
          groupId: [{ required: true, message: '班组不能为空', trigger: 'change' }],
          shiftId: [{ required: true, message: '班次不能为空', trigger: 'change' }]
        }
      }
    },
    methods: {
      handleClose() {
        this.open = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.formTitle = '新增'
        this.form.logType = this.$props.logTypeOptions[1].value
        this.changeLogDate(moment().format('yyyy-MM-DD'))
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.modalLoading = true
        if (record) {
          this.form = { ...record, urls: record.attachVOS?.map(el => el.attachUrl) }
          this.getLogGroupData()
          this.getLogShiftData()
          this.modalLoading = false
        }
      },

      changeLogDate(val) {
        this.form.groupId = null
        this.form.shiftId = null
        if (val == null) {
          return
        }
        this.form.logDate = val
        this.getLogGroupData()
      },
      changeLogGroup(val) {
        this.form.shiftId = null
        if (val == null) {
          return
        }
        this.form.groupId = val
        this.getLogShiftData()
      },
      getLogGroupData() {
        getWorkLogGroups({ logDate: this.form.logDate }).then(res => {
          this.groupOptions = (res.data || []).map(el => ({ ...el, label: el.groupName, value: el.groupId }))
          this.form.groupId = this.form.groupId == null ? this.groupOptions[0]?.groupId : this.form.groupId
          if (this.form.groupId != null) {
            this.getLogShiftData()
          }
        })
      },
      getLogShiftData() {
        getWorkLogShifts({ groupId: this.form.groupId, logDate: this.form.logDate }).then(res => {
          this.shiftOptions = (res.data || []).map(el => ({ ...el, label: el.shiftName, value: el.shiftId }))
          this.form.shiftId = this.form.shiftId == null ? this.shiftOptions[0]?.shiftId : this.form.shiftId
        })
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.form.logId !== undefined) {
              workLogUpdate(this.form)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            } else {
              workLogAdd(this.form)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      }
    }
  }
</script>
