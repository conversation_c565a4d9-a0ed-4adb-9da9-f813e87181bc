<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="班组名称">
        <a-input v-model="queryParam.groupName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="班次">
        <a-select v-model="queryParam.shiftId" allowClear :options="shiftList" placeholder="请选择"></a-select>
      </a-form-item>

      <a-form-item label="所属部门">
        <a-tree-select
          v-model="queryParam.deptId"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="deptOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'deptName',
            key: 'deptId',
            value: 'deptId'
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="班组管理"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>

        <FormDrawer
          v-if="showFormDrawer"
          :shiftList="shiftList"
          :deptOptions="deptOptions"
          ref="formDrawerRef"
          @ok="getList"
          @close="showFormDrawer = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getWorkGroupPage, getShiftPage, groupDelete } from './services'
  import { getTreeByLoginOrgId } from '@/api/common'
  import FormDrawer from './modules/FormDrawer'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import getFlatTreeMap from '@/utils/getMapFlatTree.js'

  export default {
    name: 'ObjectCategory',
    components: {
      VxeTableForm,
      VxeTable,
      FormDrawer
    },
    data() {
      return {
        showFormDrawer: false,
        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          groupName: undefined,
          shiftId: undefined,
          deptId: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: []
        },
        shiftList: [], //班次列表
        deptOptions: [], //部门树选项
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '班组名称',
            field: 'groupName',
            minWidth: 100
          },
          {
            title: '部门',
            field: 'deptName',
            minWidth: 100
          },
          {
            title: '班次',
            field: 'shiftNames',
            minWidth: 100
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 200
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    computed: {},
    watch: {},
    beforeDestroy() {},
    created() {
      getShiftPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.shiftList = (res.data.data || []).map(el => ({ label: el.shiftName, value: el.shiftId }))
      })

      getTreeByLoginOrgId().then(res => {
        this.deptOptions = res?.data
      })
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getWorkGroupPage(this.queryParam).then(response => {
          this.list = (response.data.data || []).map(ele => {
            return {
              ...ele,
              shiftNames: this.shiftList
                .filter(el => ele.shiftIds?.includes(el.value))
                .map(el => el.label)
                .join(',')
            }
          })

          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          groupName: undefined,
          shiftId: undefined,
          deptId: undefined,
          pageNum: 1,
          sort: []
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.groupId)
        this.names = valObj.records.map(item => item.groupName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      /* 新增 */
      handleAdd() {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(this.queryParam.parentId))
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const groupIds = row.groupId ? [row.groupId] : this.ids
        const names = row.groupName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return groupDelete({ groupIds: groupIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.getList()
            })
          },
          onCancel() {}
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
