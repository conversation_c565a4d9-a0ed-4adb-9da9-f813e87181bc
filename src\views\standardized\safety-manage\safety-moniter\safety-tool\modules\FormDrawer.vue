<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="名称" prop="toolName">
              <a-input v-model="form.toolName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="规格型号">
              <a-input v-model="form.specifications" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="采购时间" prop="dateTime">
              <a-date-picker v-model="form.dateTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="检查时间" prop="checkDate">
              <a-date-picker v-model="form.checkDate" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属类型" prop="toolType">
              <a-select show-search placeholder="请输入" v-model="form.toolType" option-filter-prop="children">
                <a-select-option v-for="item in toolTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addTool, editTool, getToolById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'toolTypeOptions', 'toolStatusOptions', 'toolType', 'currentProjectId'],
    data() {
      return {
        isEdit: false,
        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          dateTime: '',
          checkDate: '',
          specifications: '',
          toolName: '',
          toolType: '',
          type: this.toolType,
          isProperty: undefined,
          projectId: this.currentProjectId,
        },
        open: false,
        rules: {
          toolName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
          toolType: [{ required: true, message: '所属类型不能为空', trigger: 'change' }],
          dateTime: [{ required: true, message: '采购时间不能为空', trigger: 'change' }],
          checkDate: [{ required: true, message: '检查时间不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '工程名称不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        this.form.checkDate = moment(new Date()).format('YYYY-MM-DD')
        if (row != undefined) {
          this.modalLoading = true
          this.isEdit = true
          this.formTitle = '修改'
          getToolById({ toolId: row.toolId }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
              }
              this.form.toolType = String(this.form.toolType)
              this.modalLoading = false
            }
          })
        }
      },

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.type = this.toolType

            this.form.dateTime = moment(this.form.dateTime).format('YYYY-MM-DD')
            this.form.checkDate = moment(this.form.checkDate).format('YYYY-MM-DD')
            if (this.form.toolId == null) {
              addTool(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('close')
                    this.$emit('ok')
                  }
                })
                .finally(() => (this.loading = false))
            } else {
              editTool(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('close')
                    this.$emit('ok')
                  }
                })
                .finally(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
  .item-group {
    margin-top: 10px;

    .item {
      margin-top: 10px;
      padding: 10px;
      background: #f2f2f2;
      .item-title {
        font-size: 14px;
        font-weight: 700;
      }
    }
  }
</style>
