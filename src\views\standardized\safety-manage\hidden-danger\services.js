import request from '@/utils/request'

// 列表分页查询
export function getHiddenPage(data) {
  return request({
    url: '/prjstd/hidden/page',
    method: 'post',
    data,
  })
}
// 删除
export function deleteHidden(params) {
  return request({
    url: '/prjstd/hidden/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 增加
export function addHidden(data) {
  return request({
    url: '/prjstd/hidden/add',
    method: 'post',
    data,
  })
}
// 详情
export function getHidden(params) {
  return request({
    url: '/prjstd/hidden/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function updateHidden(data) {
  return request({
    url: '/prjstd/hidden/update',
    method: 'post',
    data,
  })
}

// 上报
export function reportedHidden(data) {
  return request({
    url: '/prjstd/hidden/reported',
    method: 'post',
    data,
  })
}

// 修改草稿状态为待处置
export function updateHiddenStatus(params) {
  return request({
    url: '/prjstd/hidden/updateStatus',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 处置
export function disposeHidden(data) {
  return request({
    url: '/prjstd/hidden/dispose',
    method: 'post',
    data,
  })
}

// 办结
export function concludeHidden(data) {
  return request({
    url: '/prjstd/hidden/conclude',
    method: 'post',
    data,
  })
}
