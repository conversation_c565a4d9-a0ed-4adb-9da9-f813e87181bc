<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1300"
    @cancel="cancel"
    modalHeight="880"
    :maskClosable="false"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <a-steps :current="step" size="small" style="padding: 4px 220px">
        <a-step title="基本信息" />
        <a-step title="降雨过程" />
        <a-step title="初始状态" />
        <a-step title="调度过程" />
        <a-step title="模拟结果" />
      </a-steps>

      <keep-alive>
        <Basic
          v-if="step == 0"
          ref="basicRef"
          v-bind="$attrs"
          @saveData="saveData"
          :inWaterEchoData="inWaterEchoData"
        />

        <RainfallProcess v-if="step == 1" ref="rainfallProcessRef" :baseInfo="baseInfo" @saveData="saveData" />

        <InitState v-if="step == 2" ref="initStateRef" :baseInfo="baseInfo" @saveData="saveData" />

        <DispatchProcess v-if="step == 3" ref="dispatchProcessRef" :baseInfo="baseInfo" @saveData="saveData" />

        <Calculation
          v-if="step == 4"
          ref="calculationRef"
          :baseInfo="baseInfo"
          @saveData="saveData"
          :schedulingId="currentSchedulingId"
        />
      </keep-alive>
    </div>
    <template slot="footer">
      <a-button @click="cancel" v-if="step === 0">取消</a-button>
      <a-button @click="preStep" v-if="step === 1 || step === 2 || step === 3 || step === 4" :disabled="isDisabledBtn">
        上一步
      </a-button>
      <a-button type="primary" @click.stop="onSubmit" :loading="loading" :disabled="isDisabledBtn">
        {{ primaryBtnText }}
      </a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { addForecast } from '../../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import Basic from './Basic.vue'
  import RainfallProcess from './RainfallProcess.vue'
  import InitState from './InitState.vue'
  import DispatchProcess from './DispatchProcess/index.vue'
  import Calculation from './Calculation.vue'

  export default {
    name: 'AddModal',
    components: { AntModal, Basic, RainfallProcess, InitState, DispatchProcess, Calculation },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '新增',
        step: 0,
        baseInfo: {},
        rainfall: {},
        chSiteWaters: [],
        dispatchProcess: {},

        currentSchedulingId: null,

        isDisabledBtn: false,
        inWaterEchoData: null,
      }
    },
    created() {},
    computed: {
      primaryBtnText() {
        if (this.step === 3) {
          return '开始计算'
        } else if (this.step === 4) {
          return '保存'
        } else {
          return '下一步'
        }
      },
    },
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      preStep() {
        this.step--
      },
      handleShow(params, type) {
        this.open = true

        if (type === 'copy') {
          // getInParameter({ chSimId: params.chSimId }).then(res => {
          //   this.inWaterEchoData = res.data
          //   this.projectFlows = res.data.projectFlows
          // })
        }
      },

      saveData(params) {
        if (!!params) {
          switch (this.step) {
            case 0:
              if (!!this.inWaterEchoData) {
                if (
                  this.inWaterEchoData.simulateType !== params.simulateType ||
                  this.inWaterEchoData.resvrDispId !== params.resvrDispId ||
                  this.inWaterEchoData.startTime !== params.startTime ||
                  this.inWaterEchoData.endTime !== params.endTime ||
                  this.inWaterEchoData.dispathType !== params.dispathType ||
                  this.inWaterEchoData.fcstRange !== params.fcstRange
                ) {
                  this.baseInfo = params
                  this.chSiteWaters = []
                }
              } else {
                this.baseInfo = params
              }
              this.$nextTick(() => (this.step += 1))
              return
            case 1:
              this.rainfall = params
              this.$nextTick(() => (this.step += 1))
              return
            case 2:
              this.chSiteWaters = params
              this.$nextTick(() => (this.step += 1))
              return
            case 3:
              this.dispatchProcess = params

              const paramInfo = {
                ...this.baseInfo,
                rains: this.rainfall.rains,
                futureRains: this.rainfall.futureRains,
                chSiteWaters: this.chSiteWaters,
                inOutWaters: this.dispatchProcess.inOutWaters,
                irrBase: this.dispatchProcess.irrBase,
                projectFlows: this.dispatchProcess.projectFlows,
              }
              this.loading = true
              addForecast(paramInfo).then(res => {
                this.$message.success('开始计算', 3)
                this.currentSchedulingId = res.data
                this.loading = false
                this.$nextTick(() => (this.step += 1))
              })
              return
            case 4:
              this.$emit('close')
              this.$emit('ok')
              return
            default:
              return
          }
        }
      },
      onSubmit() {
        switch (this.step) {
          case 0:
            this.$refs.basicRef.save()
            return
          case 1:
            this.$refs.rainfallProcessRef.save()
            return
          case 2:
            this.$refs.initStateRef.save()
            return
          case 3:
            this.$refs.dispatchProcessRef.save()
            return
          case 4:
            this.$refs.calculationRef.save()
            return
          default:
            return
        }
        return
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
</style>
