<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属组织" prop="organization">
                <a-tree-select
                  v-model="form.organization"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="configTreeOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  @change="treeSelectChange"
                  :replaceFields="{
                    children: 'children',
                    title: 'deptName',
                    key: 'deptId',
                    value: 'deptId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="仓库编码" prop="warehouseCode">
                <a-input allowClear v-model="form.warehouseCode" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="仓库名称" prop="warehouseName">
                <a-input allowClear v-model="form.warehouseName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="仓管员" prop="stockmanId">
                <a-select v-model="form.stockmanId" placeholder="请选择">
                  <a-select-option v-for="(d, index) in userOptions" :key="index" :value="d.userId">
                    {{ d.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="状态" prop="status">
                <a-select v-model="form.status" placeholder="请选择">
                  <a-select-option v-for="(d, index) in statusOptions" :key="index" :value="d.key">
                    {{ d.value }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions, getSysUserPage } from '@/api/common'
  import { addConfig, updateConfig } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormDrawer',
    props: ['configTreeOptions', 'statusOptions'],
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        parentCategoryOptions: [],
        displayOptions: [],

        // 默认密码
        formTitle: '',

        userOptions: [],
        // 表单参数
        form: {
          warehouseId: undefined,
          organization: undefined,
          warehouseCode: undefined,
          warehouseName: undefined,
          stockmanId: undefined,
          status: undefined,
        },
        open: false,

        rules: {
          organization: [{ required: true, message: '所属组织不能为空', trigger: 'change' }],
          warehouseCode: [{ required: true, message: '仓库编码不能为空', trigger: 'blur' }],
          warehouseName: [{ required: true, message: '仓库名称不能为空', trigger: 'blur' }],
          stockmanId: [{ required: true, message: '仓管员不能为空', trigger: 'change' }],
          status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
        },
      }
    },
    filters: {},
    created() {
      // 字典获取下拉
      // getOptions('displayInfoTypes').then(res => {
      //   this.displayOptions = res?.data || []
      // })
    },
    computed: {},
    watch: {},
    methods: {
      /** 用户列表 根据组织机构显示 **/
      treeSelectChange() {
        getSysUserPage({
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          isDisabled: 0,
          deptId: this.form.organization,
        }).then(res => {
          this.userOptions = res.data.data
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.formTitle = '新增'
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.form = {
          warehouseId: record.warehouseId,
          organization: record.organization,
          warehouseCode: record.warehouseCode,
          warehouseName: record.warehouseName,
          stockmanId: parseInt(record.stockmanId),
          status: record.status,
        }
        this.treeSelectChange()
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const params = {
              warehouseId: this.form.warehouseId,
              organization: this.form.organization,
              warehouseCode: this.form.warehouseCode,
              warehouseName: this.form.warehouseName,
              stockmanId: this.form.stockmanId,
              stockmanName: this.userOptions.find(el => el.userId == this.form.stockmanId).name,
              status: this.form.status,
            }

            if (this.form.warehouseId !== undefined) {
              updateConfig(params)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            } else {
              addConfig(params)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
