<template>
  <div style="height: 100%">
    <VxeTable
      ref="vxeTableRef"
      :tableKey="tableKey"
      :tableTitle="tableTitle"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="true"
      :rowConfig="{ isHover: false }"
      min-height="82px"
      max-height="280px"
      size="small"
      @refresh="getList"
      @selectChange="selectChange"
    >
      <div class="table-operations" slot="button">
        <a-button type="primary" @click="handleAdd()">
          <a-icon type="plus" />
          添加人员
        </a-button>
        <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
          <a-icon type="delete" />
          删除
        </a-button>
        <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
      </div>
    </VxeTable>
    <UserModal
      v-if="isShowModal"
      :row="row"
      ref="UserModalRef"
      @ok="onOperationComplete"
      @close="isShowModal = false"
    />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import { getUserList, deleteUser } from '../services'
  // import SolutionModal from './SolutionModal.vue'
  import UserModal from './UserModal.vue'

  export default {
    name: 'ExpandTable',
    components: {
      VxeTable,
      UserModal,
    },
    props: ['row'],
    data() {
      return {
        isShowModal: false,
        tableTitle: '人员列表',
        tableKey: 1,
        loading: true,
        list: [],
        selectIds: [],
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', align: 'center', width: 50 },
          {
            title: '姓名',
            field: 'name',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '手机号码',
            field: 'mobile',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '部门',
            field: 'deptName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '岗位',
            field: 'postNames',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.tableTitle = this.row.projectName + '-人员列表'
      this.getList()
    },
    mounted() {},
    methods: {
      getList() {
        this.loading = true
        this.selectChange({ records: [] })
        getUserList({
          dispatchProjectId: this.row.dispatchProjectId,
        }).then(response => {
          this.list = response.data
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
      },

      handleAdd() {
        this.isShowModal = true
        this.$nextTick(() => this.$refs.UserModalRef.handle())
      },
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteUser({ ids: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      onOperationComplete() {
        this.isShowModal = false
        this.getList()
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 10px;
    }
  }

  ::v-deep .vxe-table--render-default .vxe-table--body-wrapper {
    height: auto !important;
  }
</style>
