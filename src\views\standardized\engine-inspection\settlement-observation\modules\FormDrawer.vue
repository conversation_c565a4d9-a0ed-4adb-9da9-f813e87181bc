<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item :label="observationType == 1 ? '沉降观测名称' : '位移观测名称'" prop="observationName">
              <a-input v-model="form.observationName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="年份">
              <a-date-picker
                mode="year"
                format="YYYY"
                v-model="form.year"
                placeholder="请选择"
                allow-clear
                :open="yearShowOne"
                style="width: 100%"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
              ></a-date-picker>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="申请文件" prop="positionAttaches">
              <UploadFile
                :fileUrl.sync="form.positionAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addObservation, editObservation, getObservationById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'observationType'],
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          observationName: '',
          observationType: null,
          projectId: undefined,
          year: null,
          positionAttaches: [],
        },
        open: false,
        rules: {
          observationName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
          positionAttaches: [{ required: true, message: '申请附件不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getObservationById({ observationId: row.observationId }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                positionAttaches: res.data.positionAttaches?.map(el => el.attachUrl),
              }
              this.form.year = moment(`${res.data.year}-01-01`)
              this.modalLoading = false
            }
          })
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.year = moment(this.form.year).format('YYYY')
            if (this.form.observationId == null) {
              this.form.observationType = this.observationType
              addObservation(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editObservation(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 15px 15px;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }

  ::v-deep .ant-tabs-tabpane {
    // height: calc(100vh - 160px);
  }

  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }

  ::v-deep .attaches {
    // margin-top: 100px;
  }
</style>
