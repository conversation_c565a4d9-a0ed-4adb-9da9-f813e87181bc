import request from '@/utils/request'

// 工程检查_安全检查-列表分页查询
export function getInspectionPage(data) {
  return request({
    url: '/prjstd/inspection/page',
    method: 'post',
    data,
  })
}
// 增加
export function addInspection(data) {
  return request({
    url: '/prjstd/inspection/add',
    method: 'post',
    data,
  })
}
// 详情
export function getInspectionById(params) {
  return request({
    url: '/prjstd/inspection/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editInspection(data) {
  return request({
    url: '/prjstd/inspection/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteInspection(params) {
  return request({
    url: '/prjstd/inspection/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 审核
export function checkInspection(params) {
  return request({
    url: '/prjstd/inspection/check',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
