<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :modalWidth="windowSize.width"
    :modalHeight="windowSize.height"
    @cancel="cancel"
    @onFullScreen="onFullScreen"
    :footer="null"
    ref="detailModalRef"
  >
    <div slot="content" layout="vertical" class="detail-content">
      <div class="detail-tab">
        <a-tabs v-model="tabVal" type="line" :tab-position="'left'" :style="{ height: '100%' }" @change="onTabChange">
          <a-tab-pane
            v-for="(ele, i) in displayInfoOptions"
            :key="ele.key"
            :tab="projectInfo.includes(ele.key) ? ele.option1 : ele.value"
          >
            <!-- 水源地 -->
            <Source
              v-if="ele.key === 'siteInfoEX010'"
              :displayCode="ele.key"
              :otherObjectId="recordInfo.otherObjectId"
            />
            <!-- 取水口  -->
            <Intake
              v-if="ele.key === 'siteInfoEX011'"
              :displayCode="ele.key"
              :otherObjectId="recordInfo.otherObjectId"
            />
            <!-- 水资源分区 -->
            <Wrz v-if="ele.key === 'siteInfoEX001'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />
            <!-- 取用水户 -->
            <Wiu v-if="ele.key === 'siteInfoEX013'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />
            <!-- 险工险段 -->
            <Dpds v-if="ele.key === 'siteInfoEX009'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />
            <!-- 退排水口 -->
            <Pdo v-if="ele.key === 'siteInfoEX012'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />
            <!-- 岸线功能分区 -->
            <Slfz v-if="ele.key === 'siteInfoEX005'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />
            <!-- 河湖管理范围 -->
            <Rlmd v-if="ele.key === 'siteInfoEX004'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />
            <!-- 水土保持区划 -->
            <Wscz v-if="ele.key === 'siteInfoEX003'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />
            <!-- 采砂分区 -->
            <Sep v-if="ele.key === 'siteInfoEX006'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />

            <!-- 堤段 -->
            <Disc v-if="ele.key === 'siteInfoEX008'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />
            <!-- 河段 -->
            <Rea v-if="ele.key === 'siteInfoEX007'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />
            <!-- 水功能区 -->
            <Wfz v-if="ele.key === 'siteInfoEX002'" :displayCode="ele.key" :otherObjectId="recordInfo.otherObjectId" />

            <!-- 划界确权 -->
            <Demarcation
              ref="demarcationRef"
              v-if="ele.key === 'demarcation'"
              :displayCode="ele.key"
              :projectId="recordInfo.otherObjectId"
            />

            <!-- 工程文件预览 -->
            <FileViewList
              v-if="
                ele.key === 'appearance' ||
                ele.key === 'registration' ||
                ele.key === 'safetyAppraisal' ||
                ele.key === 'label'
              "
              :displayCode="ele.key"
              :projectId="recordInfo.otherObjectId"
            />

            <!-- 管理单位 -->
            <ManageUnit
              v-if="ele.key === 'unitManagement'"
              :displayCode="ele.key"
              :projectId="recordInfo.otherObjectId"
              :projectName="modalTitle"
            />

            <!-- 应急管理 -->
            <EmergencyManage
              v-if="ele.key === 'emergencyResponse'"
              :displayCode="ele.key"
              :projectId="recordInfo.otherObjectId"
            />

            <!-- 安全管理 -->
            <SafetyManage
              v-if="ele.key === 'safetyInspection'"
              :displayCode="ele.key"
              :projectId="recordInfo.otherObjectId"
            />

            <!-- 工程检查 -->
            <EngineInspection
              v-if="ele.key === 'inspectionEngineering'"
              :displayCode="ele.key"
              :projectId="recordInfo.otherObjectId"
            />

            <!-- 控制运行 -->
            <Scheduling
              v-if="ele.key === 'controlOperation'"
              :displayCode="ele.key"
              :projectId="recordInfo.otherObjectId"
            />

            <!-- 维修养护 -->
            <MaintenanceUpkeep
              v-if="ele.key === 'maintenance'"
              :displayCode="ele.key"
              :projectId="recordInfo.otherObjectId"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import Demarcation from './Demarcation.vue'
  import FileViewList from './FileViewList.vue'

  import Source from './source/index.vue' //水源地
  import Intake from './intake/index.vue' //取水口
  import Wrz from './wrz/index.vue' //水资源分区
  import Wiu from './wiu/index.vue' //取水用户
  import Dpds from './dpds/index.vue' //险工险段
  import Pdo from './pdo/index.vue' //退排水口
  import Slfz from './slfz/index.vue' //岸线功能分区
  import Rlmd from './rlmd/index.vue' //河湖管理范围
  import Wscz from './wscz/index.vue' //水土保持区划

  import Sep from './sep/index.vue' //采砂分区
  import Disc from './disc/index.vue' //堤段
  import Rea from './rea/index.vue' //河段
  import Wfz from './wfz/index.vue' //水功能区

  import ManageUnit from './manageUnit/index.vue'
  import EmergencyManage from './emergency-manage/index.vue'
  import SafetyManage from './safety-manage/index.vue'
  import EngineInspection from './engine-inspection/index.vue'
  import Scheduling from './scheduling/index.vue'
  import MaintenanceUpkeep from './maintenance-upkeep/index.vue'

  import { getDisplayCodes } from '../../services'
  import { getOptions } from '@/api/common'

  export default {
    name: 'CreateForm',
    props: {},
    components: {
      AntModal,
      Demarcation,
      FileViewList,

      Source,
      Intake,
      Wrz,
      Wiu,
      Dpds,
      Pdo,

      Slfz,
      Rlmd,
      Wscz,
      Sep,
      Disc,
      Rea,
      Wfz,

      ManageUnit,
      EmergencyManage,
      SafetyManage,
      EngineInspection,
      Scheduling,
      MaintenanceUpkeep,
    },
    data() {
      return {
        open: false,
        modalTitle: '',
        tabVal: '',
        windowSize: {},
        displayInfoOptions: [],
        recordInfo: {},
        projectInfo: [
          'siteInfoEX010',
          'siteInfoEX011',
          'siteInfoEX001',
          'siteInfoEX002',
          'siteInfoEX003',
          'siteInfoEX004',
          'siteInfoEX005',
          'siteInfoEX006',
          'siteInfoEX007',
          'siteInfoEX008',
          'siteInfoEX009',
          'siteInfoEX012',
          'siteInfoEX013',
        ], // 基本信息key集合
      }
    },
    created() {
      this.windowSize = {
        width: `${parseInt(window.innerWidth * 0.9)}`,
        height: `${parseInt(window.innerHeight * 0.95)}`,
      }
    },
    computed: {},
    watch: {},
    methods: {
      onTabChange(val) {
        this.tabVal = val
      },

      /** 打开 */
      handleDetail(record) {
        this.modalTitle = record.otherObjectName
        this.recordInfo = record
        this.open = true
        // getOptions('displayInfoTypes').then(res => {
        //   // this.displayInfoOptions = [...this.displayInfoOptions, ...res.data.filter(el => resp.data.includes(el.key))]
        //   this.displayInfoOptions = [...this.displayInfoOptions]
        //   this.tabVal = this.displayInfoOptions[0].key
        // })

        getDisplayCodes({ otherObjectId: record.otherObjectId }).then(resp => {
          console.log('*211 getDisplayCodes', resp)
          if (!resp.data?.length) {
            this.$message.info('无展示信息')
            return
          }
          this.open = true
          getOptions('displayInfoTypes').then(res => {
            this.displayInfoOptions = [...this.displayInfoOptions, ...res.data.filter(el => resp.data.includes(el.key))]
            this.tabVal = this.displayInfoOptions[0].key
          })
        })
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      onFullScreen() {
        this.$refs.demarcationRef?.[0].onFullScreen()
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 10px;
    background-color: #eef0f3 !important;
    border-radius: 0 0 4px 4px;
    .modal-content {
      width: 100%;
      height: 100%;
    }
  }

  .detail-content {
    width: 100%;
    height: 100%;
    position: relative;

    .detail-tab {
      height: 100%;

      ::v-deep .ant-tabs-content {
        height: 100%;
        padding-left: 0px;
        border-left: none;
        .ant-tabs-tabpane-active {
          height: 100%;
        }
      }
      ::v-deep .ant-tabs.ant-tabs-left {
        background-color: #fff;
        .ant-tabs-left-bar {
          border-right: 10px solid #eef0f3;
        }
      }
    }
  }
</style>
./polder/index.vue
