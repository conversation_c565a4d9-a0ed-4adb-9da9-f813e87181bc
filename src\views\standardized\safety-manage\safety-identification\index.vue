<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="鉴定名称">
        <a-input v-model="queryParam.safetyName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="工程等级">
        <a-select
          allowClear
          v-model="queryParam.projectLevel"
          placeholder="请选择"
          :options="projectLevelOptions"
        ></a-select>
      </a-form-item>

      <a-form-item label="状态">
        <a-select
          allowClear
          v-model="queryParam.safetyStatus"
          placeholder="请选择"
          :options="safetyStatusOptions"
        ></a-select>
      </a-form-item>

      <a-form-model-item label="所属工程">
        <a-tree-select
          :disabled="isDetail"
          v-model="queryParam.projectId"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>

            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormModal
          v-if="showFormModal"
          :projectLevelOptions="projectLevelOptions"
          :safetyStatusOptions="safetyStatusOptions"
          :resultsApplicationOptions="resultsApplicationOptions"
          :projectOptions="projectOptions"
          ref="formModalRef"
          @ok="onOperationComplete"
          @close="showFormModal = false"
        />
        <DetailModal
          v-if="showDetailModal"
          :projectLevelOptions="projectLevelOptions"
          :safetyStatusOptions="safetyStatusOptions"
          :resultsApplicationOptions="resultsApplicationOptions"
          ref="detailModalRef"
          @close="showDetailModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'
  import { getSafetyPage, deleteSafety } from './services'
  import { getOptions, getProjectTree } from '@/api/common'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormModal from './modules/FormModal.vue'
  import DetailModal from './modules/DetailModal.vue'

  export default {
    name: 'SafetyIdentification',
    components: {
      VxeTable,
      VxeTableForm,
      FormModal,
      DetailModal,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      type: {
        type: String,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        showFormModal: false,
        showDetailModal: false,
        projectLevelOptions: [],
        safetyStatusOptions: [],
        resultsApplicationOptions: [],
        projectOptions: [],

        list: [],
        tableTitle: '安全鉴定',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          safetyName: '',
          projectLevel: undefined,
          safetyStatus: undefined,
          projectId: this.projectId,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '鉴定名称',
            field: 'safetyName',
            minWidth: 140,
          },
          {
            title: '鉴定单位',
            field: 'safetyUnit',
            minWidth: 140,
          },
          {
            title: '鉴定开始时间',
            field: 'startTime',
            minWidth: 140,
            sortable: true,
          },
          {
            title: '工程等级',
            field: 'projectLevel',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.projectLevelOptions.find(el => el.value == row.projectLevel)?.label
              },
            },
          },
          {
            title: '成果应用',
            field: 'resultsApplication',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.resultsApplicationOptions.find(el => el.value == row.resultsApplication)?.label
              },
            },
          },
          {
            title: '状态',
            field: 'safetyStatus',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  row.safetyStatus !== null && (
                    <div class='common-status-box'>
                      <i
                        class={[
                          'common-status-icon',
                          row.safetyStatus == '4' ? 'common-status-completed' : 'common-status-waiting',
                        ]}
                      ></i>
                      <span>{this.safetyStatusOptions.find(el => el.value == row.safetyStatus)?.label}</span>
                    </div>
                  )
                )
              },
            },
          },
          {
            title: '结束时间',
            field: 'endTime',
            minWidth: 140,
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 140,
          },
          {
            title: '创建人',
            field: 'createdUserName',
            minWidth: 100,
          },
          {
            title: '创建时间',
            field: 'createdTime',
            minWidth: 170,
          },
          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                    {!this.isDetail && (
                      <span>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleUpdate(row)}>修改</a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete(row)}>删除</a>
                      </span>
                    )}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getOptions('projectLevel').then(res => {
        this.projectLevelOptions = res.data.map(el => ({
          label: el.value,
          value: el.key,
        }))
      })
      getOptions('safetyStatus').then(res => {
        this.safetyStatusOptions = res.data.map(el => ({
          label: el.value,
          value: el.key,
        }))
      })

      getOptions('resultsApplication').then(res => {
        this.resultsApplicationOptions = res.data.map(el => ({
          label: el.value,
          value: el.key,
        }))
      })

      // 获取工程树
      getProjectTree({}).then(res => {
        this.projectOptions = res.data
      })

      this.getList()
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showFormModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getSafetyPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          pageNum: 1,
          projectId: undefined,
          projectLevel: undefined,
          safetyName: '',
          safetyStatus: undefined,
          sort: [],
        }
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.safetyId)
        this.names = valObj.records.map(item => item.safetyName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({
          property: item.field,
          direction: item.order,
        }))
        this.getList()
      },
      /* 新增 */
      handleAdd() {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleAdd(this.queryParam.parentId))
      },
      /* 修改 */
      handleUpdate(row) {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleUpdate(row))
      },
      // 详情
      handleDetail(row) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleDetail(row))
      },

      // 导出
      handleExport() {},

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const safetyIds = row.safetyId ? [row.safetyId] : this.ids
        const names = row.safetyName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteSafety({ safetyIds: safetyIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
