<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="560"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="班组名称" prop="groupName">
                <a-input allowClear v-model="form.groupName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属组织" prop="deptId">
                <a-tree-select
                  v-model="form.deptId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="deptOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'deptName',
                    key: 'deptId',
                    value: 'deptId'
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="班次" prop="shiftIds">
                <a-select
                  v-model="form.shiftIds"
                  allowClear
                  mode="multiple"
                  :options="shiftList"
                  placeholder="请选择"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="备注" prop="remark">
                <a-textarea v-model="form.remark" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions, getSysUserPage } from '@/api/common'
  import { groupAdd, groupUpdate } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormDrawer',
    props: ['shiftList', 'deptOptions'],
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        parentCategoryOptions: [],
        displayOptions: [],

        // 默认密码
        formTitle: '',

        userOptions: [],
        // 表单参数
        form: {
          groupId: undefined,
          groupName: undefined,
          deptId: undefined,
          shiftIds: [],
          remark: undefined
        },
        open: false,
        rules: {
          groupName: [{ required: true, message: '班组名称不能为空', trigger: 'change' }],
          deptId: [{ required: true, message: '所属组织不能为空', trigger: 'change' }],
          shiftIds: [{ required: true, message: '班次不能为空', trigger: 'change' }]
        }
      }
    },
    filters: {},
    created() {
      getSysUserPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.userOptions = res.data.data
      })
    },
    computed: {},
    watch: {},
    methods: {
      handleClose() {
        this.open = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.formTitle = '新增'
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'
        this.modalLoading = true
        if (record) {
          const row = JSON.parse(JSON.stringify(record))
          this.form = {
            groupId: row.groupId,
            groupName: row.groupName,
            deptId: row.deptId,
            shiftIds: row.shiftIds,
            remark: row.remark
          }
          this.modalLoading = false
        }
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.form.groupId !== undefined) {
              const params = {
                groupId: this.form.groupId,
                groupName: this.form.groupName,
                deptId: this.form.deptId,
                shiftIds: this.form.shiftIds,
                remark: this.form.remark
              }
              groupUpdate(params)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            } else {
              const params = {
                groupName: this.form.groupName,
                deptId: this.form.deptId,
                shiftIds: this.form.shiftIds,
                remark: this.form.remark
              }
              groupAdd(params)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      }
    }
  }
</script>
