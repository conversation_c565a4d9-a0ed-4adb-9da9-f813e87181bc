<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="500"
    @cancel="cancel"
    modalHeight="220"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="年份" prop="year">
              <a-date-picker
                allow-clear
                :value="form.year"
                format="YYYY"
                valueFormat="YYYY"
                mode="year"
                @panelChange="
                  val => {
                    form.year = val
                  }
                "
                placeholder="请选择"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24" :span="24">
            <a-form-model-item label="骨干工程计划用水量(万m³)" prop="planWaterRate">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.planWaterRate"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addWaterBackbone, editWaterBackbone, getWaterBackboneById } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal },
    props: [],
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        loading: false,
        modalLoading: false,
        form: {
          id: null,
          planWaterRate: null,
          year: null,
        },
        open: false,
        rules: {
          year: [{ required: true, message: '年份不能为空', trigger: 'blur' }],
          planWaterRate: [{ required: true, message: '骨干工程计划用水量不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getWaterBackboneById({
            id: row.id,
          }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
              }
              this.form.year = moment(res?.data?.year, 'YYYY')
            }
            this.modalLoading = false
          })
        }
      },
      //

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const params = {
              ...this.form,
              year: moment(this.form.year).format('YYYY'),
            }
            if (this.form.id == null) {
              addWaterBackbone(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editWaterBackbone(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
