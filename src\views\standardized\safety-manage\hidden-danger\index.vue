<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-model-item label="隐患名称">
        <a-input
          v-model="queryParam.hiddenName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-model-item>
      <a-form-model-item label="隐患类型">
        <a-select
          allow-clear
          placeholder="请输入"
          v-model="queryParam.hiddenType"
          :options="hiddenTypeOptions"
        ></a-select>
      </a-form-model-item>
      <a-form-model-item label="状态">
        <a-select
          placeholder="请输入"
          allow-clear
          v-model="queryParam.status"
          :options="hiddenStatusOptions"
        ></a-select>
      </a-form-model-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              隐患上报
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :hiddenTypeOptions="hiddenTypeOptions"
          :userOptions="userOptions"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :hiddenTypeOptions="hiddenTypeOptions"
          :hiddenStatusOptions="hiddenStatusOptions"
          :userOptions="userOptions"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
        <DisposeModal
          v-if="showDisposeModal"
          ref="disposeModalRef"
          @ok="onOperationComplete"
          @close="showDisposeModal = false"
        />
        <ConcludeModal
          v-if="showConcludeModal"
          ref="concludeModalRef"
          @ok="onOperationComplete"
          @close="showConcludeModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree, getSysUserPage } from '@/api/common'
  import { getHiddenPage, deleteHidden } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import DisposeModal from './modules/DisposeModal.vue'
  import ConcludeModal from './modules/ConcludeModal.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'HiddenDanger',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
      DisposeModal,
      ConcludeModal,
    },
    data() {
      return {
        hiddenTypeOptions: [],
        hiddenStatusOptions: [
          { label: '草稿', value: 0 },
          { label: '待处置', value: 1 },
          { label: '待办结', value: 2 },
          { label: '通过', value: 3 },
          { label: '不通过', value: 4 },
        ],
        userOptions: [],
        projectOptions: [],

        showForm: false,
        showFormDetails: false,
        showDisposeModal: false,
        showConcludeModal: false,

        isChecked: false,
        list: [],
        tableTitle: '隐患处理',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          hiddenName: null,
          hiddenType: undefined,
          status: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },

          {
            title: '隐患名称',
            minWidth: 120,
            field: 'hiddenName',
            showOverflow: true,
          },
          {
            title: '隐患类型',
            minWidth: 100,
            showOverflow: true,
            slots: {
              default: ({ row }) => {
                return this.hiddenTypeOptions.find(el => el.value == row.hiddenType)?.label
              },
            },
          },
          {
            title: '隐患地点',
            minWidth: 120,
            field: 'hiddenAddress',
            showOverflow: true,
          },
          {
            title: '上报时间',
            minWidth: 170,
            field: 'createdTime',
            showOverflow: true,
          },
          {
            title: '上报人',
            minWidth: 170,
            field: 'createdUserName',
            showOverflow: true,
          },
          {
            title: '状态',
            minWidth: 100,
            showOverflow: true,
            slots: {
              default: ({ row }) => {
                return this.hiddenStatusOptions.find(el => el.value == row.status)?.label
              },
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 190,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    {row.status === 1 && row.disposeUserId == localStorage.getItem('userId') && (
                      <span>
                        <a onClick={() => this.handleDispose(row)}>处置</a>
                        <a-divider type='vertical' />
                      </span>
                    )}

                    {row.status === 2 && (
                      <span v-permission={`${this.$route.meta.menuId}-conclude`}>
                        <a onClick={() => this.handleConclude(row)}>办结</a>
                        <a-divider type='vertical' />
                      </span>
                    )}

                    {row.status === 0 && (
                      <span>
                        <a onClick={() => this.handleEdit(row)}>修改</a>
                        <a-divider type='vertical' />
                      </span>
                    )}

                    {row.status > 0 && (
                      <span>
                        <a onClick={() => this.handleDetails(row)}>查看</a>
                        <a-divider type='vertical' />
                      </span>
                    )}

                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      getOptions('hiddenType').then(res => {
        this.hiddenTypeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
      })

      getSysUserPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.userOptions = res.data.data.map(el => ({ label: el.name, value: el.userId }))
      })

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        // this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })

      this.getList()
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.loading = true
        this.selectChange({ records: [] })
        getHiddenPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.hiddenId)
        this.names = valObj.records.map(item => item.hiddenName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          hiddenName: null,
          hiddenType: undefined,
          status: undefined,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /* 处置 */
      handleDispose(record) {
        this.showDisposeModal = true
        this.$nextTick(() => this.$refs.disposeModalRef.handle(record))
      },
      /* 办结 */
      handleConclude(record) {
        this.showConcludeModal = true
        this.$nextTick(() => this.$refs.concludeModalRef.handle(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.hiddenId ? [row?.hiddenId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteHidden({ hiddenIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
