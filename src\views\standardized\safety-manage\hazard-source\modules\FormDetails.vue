<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-row class="form-row" v-if="!!form" :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">危险源名称：</label>
            <span class="common-value-text">
              {{ form.riskName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">危险类型：</label>
            <span class="common-value-text">
              {{ riskTypeOptions.find(el => el.value == form.riskType)?.label }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">危险等级：</label>
            <span class="common-value-text">
              {{ riskLevelOptions.find(el => el.value == form.riskLevel)?.label }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">危险区域：</label>
            <span class="common-value-text">
              {{ form.riskAddress }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">
              {{ form.projectName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">日期：</label>
            <span class="common-value-text">
              {{ form.riskDate }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="item">
            <label class="common-label-text">个人照片：</label>
          </div>
          <UploadFile
            :fileUrl="form.phoneAttaches?.map(el => el.attachUrl)"
            :multiple="true"
            listType="picture-card"
            :onlyView="true"
            disabled
          />
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 20px">
            <div class="common-label-text">附件：</div>
            <div
              class="file-item"
              v-for="(el, i) in form.positionAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getRisk } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'FormDetails',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'riskTypeOptions', 'riskLevelOptions'],
    data() {
      return {
        modalLoading: false,
        formTitle: '',
        open: false,

        form: null,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      downLoad(url) {
        window.open(url)
      },

      details(row) {
        this.open = true
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '查看'
          getRisk({ id: row.id }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              this.form.projectName = row.projectName
              this.modalLoading = false
            }
          })
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
