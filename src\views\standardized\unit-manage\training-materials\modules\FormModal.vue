<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="培训名称" prop="trainingName">
                <a-input allowClear v-model="form.trainingName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="执行情况" prop="statusImplementation">
                <a-select
                  allowClear
                  v-model="form.statusImplementation"
                  placeholder="请选择"
                  :options="statusImplementationOptions"
                ></a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="计划经费(万元)" prop="planExpense">
                <a-input-number v-model="form.planExpense" :min="0" style="width: 100%" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="实际经费(万元)" prop="actualExpense">
                <a-input-number v-model="form.actualExpense" :min="0" style="width: 100%" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="培训日期" prop="trainingTime">
                <a-date-picker
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  v-model="form.trainingTime"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="管理单位" prop="unitId">
                <a-tree-select
                  v-model="form.unitId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="unitOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'deptName',
                    key: 'deptId',
                    value: 'deptId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
                <!-- <a-select
                  allowClear
                  show-search
                  option-filter-prop="children"
                  v-model="form.unitId"
                  placeholder="请选择"
                  :options="unitOptions"
                ></a-select> -->
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="培训内容" prop="trainingContent">
                <a-textarea v-model="form.trainingContent" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="培训计划" prop="trainingPlan">
                <a-textarea v-model="form.trainingPlan" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <!-- prop="proofAttaches" -->
              <a-form-model-item label="附件">
                <UploadFile
                  :fileUrl.sync="form.proofAttaches"
                  :multiple="true"
                  listType="text"
                  folderName="projectCover"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { addTraining, getTraining, updateTraining } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'FormModal',
    props: ['projectOptions', 'statusImplementationOptions', 'unitOptions'],
    components: { AntModal, UploadFile },
    data() {
      return {
        open: false,
        loading: false,
        modalLoading: false,
        formTitle: '',
        // 表单参数
        form: {
          trainingId: undefined,
          trainingName: '',
          statusImplementation: undefined,
          planExpense: undefined,
          actualExpense: undefined,
          trainingTime: '',
          projectId: undefined,
          trainingContent: '',
          trainingPlan: '',
          proofAttaches: [],
        },

        rules: {
          trainingName: [{ required: true, message: '培训名称不能为空', trigger: 'blur' }],
          statusImplementation: [{ required: true, message: '执行情况不能为空', trigger: 'change' }],
          planExpense: [{ required: true, message: '计划经费不能为空', trigger: 'blur' }],
          actualExpense: [{ required: true, message: '实际经费不能为空', trigger: 'blur' }],
          trainingTime: [{ required: true, message: '培训时间不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
          // proofAttaches: [{ required: true, message: '证明材料不能为空', trigger: 'change' }]
        },
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(parentId) {
        this.open = true
        this.formTitle = '新增'
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'

        this.modalLoading = true
        getTraining({ trainingId: record.trainingId }).then(res => {
          this.form = { ...res.data, proofAttaches: res.data.proofAttaches?.map(el => el.attachUrl) }
          this.modalLoading = false
        })
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const params = {
              ...this.form,
            }

            if (this.form.trainingId !== undefined) {
              updateTraining(params)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            } else {
              addTraining(params)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped></style>
