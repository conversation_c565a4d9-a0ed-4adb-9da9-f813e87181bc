import axios from 'axios'
import { mapBoundGeo } from '@/utils/mapBounds'

export default function addLine(mapIns) {
  Promise.all([
    axios(
      // 灌区的边界
      `${process.env.VUE_APP_GEOSERVER_BASE}/sthgq/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=sthgq:HP004&maxFeatures=50&outputFormat=application/json`,
    ),

    axios(
      `${process.env.VUE_APP_GEOSERVER_BASE}/district/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=district:zhejiang&filter=<PropertyIsEqualTo><PropertyName>code</PropertyName><Literal>330481</Literal></PropertyIsEqualTo>`,
    ),
  ]).then(res => {
    mapIns.addLayer({
      id: 'gq-001-line',
      type: 'line',
      source: {
        type: 'geojson',
        data: res[0].data, //区划的面数据
      },
      paint: {
        'line-color': '#03FFCD',
        'line-width': 2,
      },
    })

    mapBoundGeo(res[1].data, mapIns, { top: 20, bottom: 20, left: 20, right: 20 })
    //蒙版边界
    mapIns.addLayer({
      id: 'mb-002-line',
      type: 'line',
      source: {
        type: 'geojson',
        data: res[1].data, //区划的面数据
      },
      paint: {
        'line-color': 'rgba(9, 236, 255, 1)',
        'line-width': 6,
      },
      layout: {
        visibility: 'visible',
      },
    })

    // 蒙版图层   //通过边界数据反选 达到挖洞效果
    mapIns.addLayer({
      id: 'mb-003-tag',
      type: 'fill',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [-180, 90],
                [180, 90],
                [180, -90],
                [-180, -90],
              ],
              res[1].data.features[0].geometry.coordinates[0][0],
            ],
          },
        },
      },
      paint: { 'fill-color': 'rgba(255,255,255,0.4)' }, //{ 'fill-color': 'rgba(0,0,0,0.4)' },
      layout: { visibility: 'visible' },
    })
  })
}
