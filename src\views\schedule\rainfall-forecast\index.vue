<template>
  <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff">
    <div class="header">
      <span class="tabs">
        <a :class="{ 'tab-active': currentHeaderTab == 1 }" @click="currentHeaderTabChange(1)">降雨监测</a>
        <a :class="{ 'tab-active': currentHeaderTab == 2 }" @click="currentHeaderTabChange(2)">降雨预报</a>
      </span>
    </div>
    <div style="display: flex; flex: 1">
      <div style="width: 34%; min-width: 460px; display: flex; flex-direction: column; padding: 10px 24px">
        <!-- 降雨监测查询条件 -->
        <div v-if="currentHeaderTab == 1" style="padding: 15px 20px 16px; display: flex; flex-direction: column">
          <div class="flood-tabs">
            <label class="name">统计类型</label>
            <span class="tabs">
              <a :class="{ 'tab-active': currentTab == 1 }" @click="currentTabChange(1)">逐时</a>
              <a :class="{ 'tab-active': currentTab == 2 }" @click="currentTabChange(2)">逐日</a>
              <a :class="{ 'tab-active': currentTab == 3 }" @click="currentTabChange(3)">逐月</a>
              <!-- <a :class="{ 'tab-active': currentTab == 4 }" @click="currentTabChange(4)">瞬时</a> -->
            </span>
          </div>
          <div style="display: flex; align-items: center; margin-top: 10px">
            <span style="min-width: 56px; margin-right: 10px">选择时间</span>
            <a-range-picker
              style="min-width: 170px; width: 100%"
              v-if="currentTab == 1"
              v-model="hourRange"
              show-time
              format="YYYY-MM-DD HH"
              :allowClear="false"
              :disabled-date="disabledDate"
              valueFormat="YYYY-MM-DD HH"
              @change="onChangeHour"
            />
            <a-range-picker
              style="min-width: 170px; width: 100%"
              v-if="currentTab == 2"
              v-model="dayRange"
              format="YYYY-MM-DD"
              :allowClear="false"
              :disabled-date="disabledDate"
              valueFormat="YYYY-MM-DD"
              @change="onChangeDay"
            />
            <a-range-picker
              style="min-width: 170px; width: 100%"
              v-else-if="currentTab == 3"
              :placeholder="['开始', '结束']"
              format="YYYY-MM"
              valueFormat="YYYY-MM"
              :value="monthRange"
              :mode="modeMonth"
              :disabled-date="disabledDate"
              @panelChange="handlePanelChangeMonth"
            />
          </div>

          <div style="display: flex; margin-top: 10px">
            <span style="min-width: 56px; margin-right: 10px">站点名称</span>
            <a-select
              styel="width:100%"
              allowClear
              v-model="siteCode"
              placeholder="请选择"
              :options="siteOptions"
              @change="onChangeSite"
            ></a-select>
            <a-button
              icon="search"
              @click="getList"
              size="small"
              type="primary"
              style="margin-left: auto; float: right; margin-right: 10px"
            >
              查询
            </a-button>
            <a-button icon="reload" @click="reset" size="small" style="">重置</a-button>
          </div>
        </div>
        <!-- 降雨预报查询条件 -->
        <div v-if="currentHeaderTab == 2" style="padding: 15px 20px 16px; display: flex; flex-direction: column">
          <div class="forecast-tabs">
            <label class="name">预报时间</label>
            <span class="tabs">
              <a :class="{ 'tab-active': currentForecastTab == 1 }" @click="currentForecastTabChange(1)">
                {{ forecastTypeList[1] }}
              </a>
              <a :class="{ 'tab-active': currentForecastTab == 2 }" @click="currentForecastTabChange(2)">
                {{ forecastTypeList[2] }}
              </a>
              <a :class="{ 'tab-active': currentForecastTab == 3 }" @click="currentForecastTabChange(3)">
                {{ forecastTypeList[3] }}
              </a>
            </span>
          </div>
          <!--
          <div style="margin-top: 10px">{{ forecastTypeList[currentForecastTab] }}降雨概率：{{ forecastRate }}</div>-->
          <div style="display: flex; margin-top: 10px">
            <span style="min-width: 56px; margin-right: 10px">站点名称</span>
            <a-select
              styel="width:100%"
              allowClear
              v-model="siteCode"
              placeholder="请选择"
              :options="siteOptions"
              @change="onChangeSite"
            ></a-select>
          </div>
        </div>

        <div class="flood-box">
          <div class="flood-table-box" v-if="currentHeaderTab == 1">
            <div class="chart-title">时段累积降雨量：{{ monitorSum }}mm</div>
            <BarEchart :dataSource="chartConfig.dataSource" :custom="chartConfig.custom" key="1" height="280px" />

            <div style="padding-top: 10px; display: flex; flex: 1; flex-direction: column">
              <VxeTable
                style="height: 100%"
                :key="tableKey"
                ref="vxeTableRef"
                border="full"
                :isDrop="false"
                :isShowTableHeader="false"
                :columns="columns"
                :tableData="monitorList"
                :loading="loading"
                show-footer
                :footer-method="footerMethod"
                :footer-row-style="{
                  background: '#F8F8F9',
                  fontWeight: 600,
                  color: '#333',
                }"
              ></VxeTable>
            </div>
          </div>
          <div class="flood-table-box" v-if="currentHeaderTab == 2">
            <div class="chart-title">时段累积降雨量：{{ monitorSum }}mm</div>
            <BarEchart :dataSource="chartConfig.dataSource" :custom="chartConfig.custom" key="1" height="280px" />

            <div style="padding-top: 10px; display: flex; flex: 1; flex-direction: column">
              <VxeTable
                style="height: 100%"
                :key="tableKey"
                ref="vxeTableRef"
                border="full"
                :isDrop="false"
                :isShowTableHeader="false"
                :columns="columns"
                :tableData="forecastList"
                :loading="loading"
                show-footer
                :footer-method="footerMethod"
                :footer-row-style="{
                  background: '#F8F8F9',
                  fontWeight: 600,
                  color: '#333',
                }"
              ></VxeTable>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 66%; position: relative; border-right: 1px solid #e5e6eb">
        <RightMap
          v-if="rainfallClass.length > 0"
          :rainfallClass="rainfallClass"
          :geojsonList="geojsonList"
          :isRainContourDebug="isRainContourDebug"
        />

        <div class="legend">
          <div style="margin-bottom: 5px; font-weight: bold">
            图例
            <span style="font-weight: normal; font-size: 12px; margin-left: 5px">单位：mm</span>
          </div>
          <span class="legend-item" v-for="(el, i) in rainfallClass" :key="i">
            <div
              style="width: 24px; height: 12px; border-radius: 4px; margin-right: 10px; margin-top: 3px"
              :style="{ background: el.color }"
            ></div>
            {{ el.label }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getValueByKey } from '@/api/common'
  import { futureRainConvert, futureRainList, getContourSurface } from './services'

  import moment from 'moment'
  import { BarEchart } from '@/components/Echarts'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import { loading } from 'vxe-pc-ui'
  import { decimalFilter } from '@/utils/dealNumber'

  import RightMap from './rightMap.vue'

  export default {
    name: 'RainfallForecast',
    components: { BarEchart, VxeTable, VxeTableForm, RightMap },
    data() {
      return {
        chartData: [],
        siteCode: null,
        siteOptions: [],

        isRainContourDebug: false,
        rainfallClass: [],
        loading: false,
        tableKey: 1,
        columns: [],

        forecastList: [], //降雨预报列表
        monitorSum: 0, //降雨监测 总数
        monitorList: [], //降雨监测列表

        list: [],
        currentForecastTab: 1,
        forecastRate: '10%',
        forecastTypeList: { 1: '未来12小时', 2: '未来24小时', 3: '未来72小时' },
        currentHeaderTab: 1,
        geojsonList: null,
        startTime: null,
        endTime: null,

        chartConfig: {
          dataSource: [],
          custom: {
            yLabel: '单位(mm)',
            toolbox: {
              feature: {
                saveAsImage: {},
              },
            },
            shortValue: true,
            legend: { left: 'center' },
            showAreaStyle: false,
            dataZoom: true,
          },
        },

        currentTab: 1,
        autoPredMinute: 0,
        hourRange: [moment(), moment()],
        dayRange: [moment(), moment()],
        monthRange: [moment(), moment()],
        yearRange: [moment(), moment()],
        modeMonth: ['month', 'month'],
        modeYear: ['year', 'year'],
      }
    },
    computed: {},
    watch: {},
    created() {
      this.initTime()
      getValueByKey('mode.inwater.auto.min').then(res => {
        this.autoPredMinute = Number(res.data)
      })
      getValueByKey('is_rain_contour_debug').then(res => {
        this.isRainContourDebug = res.data == 'yes' ? true : false
      })

      getOptions('rainfall_class_range').then(res => {
        this.rainfallClass = res.data.map(el => ({
          ...el,
          label: el.option2 ? `${el.option1}~${el.option2}` : `>${el.option1}`,
          color: el.value,
          value: el.key,
        }))
      })
      this.hourRange = [moment(this.startTime), moment(this.endTime)]
      this.getList()
    },
    methods: {
      initTime() {
        const now = moment()
        const isBefore8AM = now.hour() < 8
        let yesterday8AM = null
        let today8AM = null
        if (isBefore8AM) {
          // 如果当前时间在早上8点之前
          yesterday8AM = now.subtract(1, 'day').hour(8).minute(0).second(0).millisecond(0)
          this.startTime = yesterday8AM.format('YYYY-MM-DD HH:mm:ss')
          this.endTime = moment(this.startTime).add(24, 'hours').format('YYYY-MM-DD HH:59:59')
        } else {
          // 如果当前时间在早上8点之后
          today8AM = now.hour(8).minute(0).second(0).millisecond(0)
          this.startTime = today8AM.format('YYYY-MM-DD HH:mm:ss')
          this.endTime = moment(new Date()).format('YYYY-MM-DD HH:00:00')
        }
      },
      disabledDate(val) {
        return val > moment().subtract(0, 'day')
      },
      //切换日期
      onChangeHour() {
        this.startTime = this.hourRange[0] + ':00:00'
        this.endTime = this.hourRange[1] + ':59:59'
      },
      onChangeDay() {
        this.startTime = this.dayRange[0]
        this.endTime = this.dayRange[1]
      },
      handlePanelChangeMonth(value, mode) {
        this.monthRange = value
        this.startTime = value[0].startOf('month').format('YYYY-MM')
        this.endTime = value[1].endOf('month').format('YYYY-MM')

        this.modeMonth = [mode[0] === 'date' ? 'month' : mode[0], mode[1] === 'date' ? 'month' : mode[1]]
      },
      handlePanelChangeYear(value, mode) {
        this.yearRange = value
        this.startTime = value[0].format('yyyy') + '-01-01 00:00:00'
        this.endTime = value[1].format('yyyy') + '-12-31 23:59:59'
      },
      /*  切换降雨监测/降雨预报*/
      currentHeaderTabChange(val) {
        this.currentHeaderTab = val
        if (this.currentHeaderTab == 1) {
          this.currentTabChange(1)
        } else {
          this.currentForecastTabChange(1)
        }
        this.getList()
      },
      /*  切换降雨监测 逐时/逐日/逐月*/
      currentTabChange(val) {
        this.currentTab = val
        if (this.currentTab == 1) {
          this.initTime()
          this.hourRange = [moment(this.startTime), moment(this.endTime)]
        } else if (this.currentTab == 2) {
          this.startTime = moment().format('YYYY-MM-DD')
          this.endTime = moment().format('YYYY-MM-DD')
          this.dayRange = [moment(this.startTime), moment(this.endTime)]
        } else if (this.currentTab == 3) {
          this.startTime = moment().startOf('month').format('YYYY-MM')
          this.endTime = moment().endOf('month').format('YYYY-MM')
        }
      },
      /*  切换降雨预报 未来12h小时 /未来24小时 /未来72小时 */
      currentForecastTabChange(val) {
        this.currentForecastTab = val
        this.getList()
      },

      footerMethod() {
        // 接收二维数组
        return this.gridOptions.footerData
      },
      meanNum(list, field) {
        let count = 0
        list.forEach(item => {
          count += Number(item[field])
        })
        return count / list.length
      },
      footerMethod({ columns, data }) {
        const footerData = [
          columns.map((column, _columnIndex) => {
            if (this.currentHeaderTab == 1) {
              if (_columnIndex === 0) {
                return '累积降雨量(mm)：'
              }
              if (_columnIndex == 1) {
                return this.monitorList.reduce((sum, item) => {
                  if (item[this.columns[1].field] !== null) {
                    sum += item[this.columns[1].field]
                  }
                  return decimalFilter(sum, 1)
                }, 0)
                // return this.list.reduce((sum, item) => sum + item.rain, 0) // this.meanNum(data, column.wlv)
              }
              if (_columnIndex == 2) {
                return this.monitorList.reduce((sum, item) => {
                  if (item[this.columns[2].field] !== null) {
                    sum += item[this.columns[2].field]
                  }
                  return decimalFilter(sum, 1)
                }, 0)
              }
              return null
            } else if (this.currentHeaderTab == 2) {
              if (_columnIndex === 0) {
                return '累积降雨量(mm)：'
              }
              if (_columnIndex == 1) {
                return this.forecastList.reduce((sum, item) => {
                  if (item[this.columns[1].field] !== null) {
                    sum += item[this.columns[1].field]
                  }
                  return decimalFilter(sum, 1)
                }, 0)
              }
              if (_columnIndex == 2) {
                return this.forecastList.reduce((sum, item) => {
                  if (item[this.columns[2].field] !== null) {
                    sum += item[this.columns[2].field]
                  }
                  return decimalFilter(sum, 1)
                }, 0)
              }
              return null
            }
          }),
        ]
        return footerData
      },
      getList() {
        if (this.currentHeaderTab == 1) {
          futureRainList({
            type: this.currentTab,
            startTime: this.startTime,
            endTime: this.endTime,
          }).then(res => {
            let list = res.data
            let columns = [
              // { type: 'seq', title: '序号', width: 50 },
              {
                title: '时间',
                field: 'dateTime',
                fixed: 'left',
                minWidth: 136,
              },
            ]

            this.monitorList = list.map(item => {
              const newItem = {
                dateTime: item.dateTime,
              }
              item.sites.forEach(site => {
                newItem[`${site.siteId}#rain`] = site.rain
              })
              return newItem
            })

            list[0].sites.forEach(el => {
              columns.push({
                title: el.siteName,
                field: `${el.siteId}#rain`,
                minWidth: 130,
              })
            })
            this.columns = columns
            this.siteOptions = list[0].sites.map(site => ({
              ...site,
              value: site.siteId,
              label: site.siteName,
            }))
            this.chartData = res.data
            if (!this.siteCode) {
              this.siteCode = this.siteOptions[0].value
              this.getChartData(this.siteOptions[0].value)
            } else {
              this.getChartData(this.siteCode)
            }
          })
        } else if (this.currentHeaderTab == 2) {
          this.startTime = moment().format('YYYY-MM-DD HH:00:00')
          this.endTime =
            this.currentForecastTab == 1
              ? moment().add(12, 'hours').format('YYYY-MM-DD HH:59:59')
              : this.currentForecastTab == 2
                ? moment().add(24, 'hours').format('YYYY-MM-DD HH:59:59')
                : this.currentForecastTab == 3
                  ? moment().add(72, 'hours').format('YYYY-MM-DD HH:59:59')
                  : ''
          futureRainConvert({
            startTime: this.startTime,
            endTime: this.endTime,
          }).then(res => {
            let list = res.data

            this.monitorSum = 0
            let columns = [
              // { type: 'seq', title: '序号', width: 50 },
              {
                title: '时间',
                field: 'dateTime',
                fixed: 'left',
                minWidth: 136,
              },
            ]
            this.forecastList = list.map(item => {
              const newItem = {
                dateTime: item.dateTime,
              }
              item.sites.forEach(site => {
                newItem[`${site.siteId}#rain`] = site.rain
              })
              return newItem
            })
            list[0].sites.forEach(el => {
              columns.push({
                title: el.siteName,
                field: `${el.siteId}#rain`,
                minWidth: 130,
              })
            })
            this.columns = columns
            this.siteOptions = list[0].sites.map(site => ({
              ...site,
              value: site.siteId,
              label: site.siteName,
            }))
            this.chartData = list
            if (!this.siteCode) {
              this.siteCode = this.siteOptions[0].value
              this.getChartData(this.siteOptions[0].value)
            } else {
              this.getChartData(this.siteCode)
            }
          })
        }

        let startDate =
          this.currentTab == 2
            ? moment(this.startTime).format('YYYY-MM-DD 00')
            : this.currentTab == 3
              ? moment(this.startTime).startOf('month').format('YYYY-MM-DD 00')
              : moment(this.startTime).format('YYYY-MM-DD HH')
        let endDate =
          this.currentTab == 2
            ? moment(this.endTime).format('YYYY-MM-DD 23')
            : this.currentTab == 3
              ? moment(this.endTime).endOf('month').format('YYYY-MM-DD 23')
              : moment(this.endTime).format('YYYY-MM-DD HH')
        let params = {
          startTime: startDate, // '2025-03-21 12',
          endTime: endDate, //'2025-03-21 23',
          isFuture: this.currentHeaderTab == 2 ? true : false,
        }
        getContourSurface(params).then(res => {
          this.geojsonList = res.data
        })
      },
      reset() {
        this.currentTab = 1
        this.initTime()

        this.hourRange = [moment(this.startTime), moment(this.endTime)]
        this.getList()
      },
      onChangeSite(item) {
        this.getChartData(item)
      },
      getChartData(val) {
        let newList = this.chartData?.map(item => {
          const site = item.sites.find(site => site.siteId == val)
          return {
            dateTime: item.dateTime,
            ...site, // 展开找到的 site 对象
          }
        })
        let rainSum = newList.reduce((sum, item) => {
          return sum + (item.rain || 0) // 如果 rain 是 null/undefined，按 0 计算
        }, 0)

        this.monitorSum = decimalFilter(rainSum, 1)

        this.chartConfig.dataSource = [
          {
            name: '时段累积降雨量',
            data:
              this.currentTab == 1 && this.currentHeaderTab == 1
                ? newList?.map(el => [moment(el.dateTime).format('MM-DD HH'), decimalFilter(el.rain, 1)])
                : this.currentTab != 1 && this.currentHeaderTab == 1
                  ? newList?.map(el => [moment(el.dateTime).format('MM-DD'), decimalFilter(el.rain, 1)])
                  : newList?.map(el => [moment(el.dateTime).format('MM-DD HH'), decimalFilter(el.rain, 1)]),
          },
        ]
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('./index.less');
</style>
