<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane key="1" tab="工程目录">
          <TreeGeneral
            v-if="treeTabKey === '1'"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="treeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'category')"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="设备分类">
          <TreeGeneral
            v-if="treeTabKey === '2'"
            :key="2"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="districtTreeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'district')"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="设备名称">
          <a-input
            v-model="queryParam.deviceName"
            placeholder="请输入设备名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>
        <a-form-item label="通讯状态">
          <a-select allowClear v-model="queryParam.trafficStatus" placeholder="请选择">
            <a-select-option v-for="(d, index) in trafficStatus" :key="index" :value="d.ids">
              {{ d.stateName }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
            class="components-table-demo-nested"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="redo" />
                刷新
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getTreeByCode, getSitePage, deleteSite } from './services'
  import { getDistrictTree, getProjectTree, getDeviceCategoryTree, getProjectCategoryTree } from '@/api/common'
  import TreeGeneral from '@/components/TreeGeneral'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import VxeTable from '@/components/VxeTable'
  import DictDataIndex from './DictDataIndex'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'SiteManage',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      DictDataIndex,
    },
    data() {
      return {
        treeTabKey: '1',
        treeOptions: {
          getDataApi: getProjectCategoryTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'key',
          },
        },
        districtTreeOptions: {
          getDataApi: getDeviceCategoryTree,
          replaceFields: {
            children: 'children',
            title: 'deviceCategoryName',
            key: 'deviceCategoryId',
            value: 'deviceCategoryId',
          },
        },
        showFormDrawer: false,
        projectOptions: [],

        list: [],
        tableTitle: '',
        ids: [],
        names: [],
        loading: false,
        total: 0,

        // queryParam: {
        //   siteCode: undefined,
        //   siteName: undefined,
        //   objectCategoryId: undefined,
        //   districtCode: undefined,
        //   pageNum: 1,
        //   pageSize: 10,
        //   sort: []
        // },
        queryParam: {
          deviceCategoryId: undefined,
          deviceName: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
          trafficStatus: undefined,
          treeNodeId: undefined,
          treeNodeType: '',
        },
        districtTypes: {},
        siteTypes: {},
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            type: 'expand',
            width: 40,
            slots: {
              content: ({ row, rowIndex }) => {
                return (
                  <dict-data-index
                    style='padding:10px;background-color:#FAFAFA'
                    title={row.deviceName}
                    dictCode={row.deviceCode}
                    sonData={this.list}
                    refreshData={this.refreshData}
                  />
                )
              },
            },
          },
          {
            title: '设备编码',
            field: 'deviceCode',
            showOverflow: 'tooltip',
            minWidth: 120,
          },
          {
            title: '设备名称',
            field: 'deviceName',
            showOverflow: 'tooltip',
            minWidth: 180,
          },
          {
            title: '所属工程',
            field: 'projectName',
            showOverflow: 'tooltip',
            minWidth: 180,
          },
          {
            title: '指标数量',
            field: 'indexCount',
            minWidth: 180,
          },
          {
            title: '最后上传时间',
            field: 'dateTime',
            minWidth: 180,
          },
          {
            title: '通讯状态',
            field: 'trafficStatus',
            minWidth: 180,
          },
        ],
        trafficStatus: [
          { ids: 1, stateName: '正常' },
          { ids: 2, stateName: '故障' },
        ],
      }
    },
    created() {
      // getDistrictTree().then(res => {
      getDeviceCategoryTree().then(res => {
        this.districtTreeOptions.dataSource = res.data
        this.districtTypes = getFlatTreeMap(res.data, 'districtCode')
      })
    },
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        //treeNodeId
        this.queryParam.pageSize = pageSize

        if (this.treeTabKey === '1') {
          if (this.queryParam.treeNodeId || this.queryParam.districtCode) {
            this.getList()
          }
        }
        if (this.treeTabKey === '2') {
          if (this.queryParam.objectCategoryId || this.queryParam.deviceCategoryId) {
            this.getList()
          }
        }
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        if (this.treeTabKey === '2') {
          this.queryParam.treeNodeId = ''
          this.queryParam.treeNodeType = ''
        } else if (this.treeTabKey === '1') {
          this.queryParam.objectCategoryId = ''
          this.queryParam.deviceCategoryId = ''
        }
        this.selectChange({ records: [] })
        getSitePage(this.queryParam).then(response => {
          this.list = response.data.data
          for (let i = 0; i < this.list.length; i++) {
            let molecule = 0
            let denominator = 0
            if (this.list[i].indexDetails != null) {
              denominator = this.list[i].indexDetails.length
              for (let j = 0; j < this.list[i].indexDetails.length; j++) {
                if (this.list[i].indexDetails[j].trafficStatus == 1) {
                  molecule = molecule + 1
                }
              }
            }
            this.list[i].indexCount = denominator
            this.list[i].trafficStatus = molecule + '/' + denominator
          }
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          deviceName: undefined,
          trafficStatus: undefined,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.siteId)
        this.names = valObj.records.map(item => item.siteName)
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 树加载完成后
      onTreeMounted(data) {
        if (this.treeTabKey === '1') {
          this.queryParam.treeNodeId = ''
          this.queryParam.districtCode = ''
          this.tableTitle = data[0].title

          this.siteTypes = getFlatTreeMap(data, 'objectCategoryId')
          this.treeOptions.dataSource = data
        }
        if (this.treeTabKey === '2') {
          this.queryParam.objectCategoryId = ''
          this.queryParam.deviceCategoryId = ''
          this.tableTitle = data[0].title
        }

        this.getList()
        // 获取工程树
        getProjectTree({ objectCategoryId: null }).then(res => {
          this.projectOptions = res.data
        })
      },

      clickTreeNode(node, type) {
        //工程目录
        if (type === 'category') {
          const key = node.$options.propsData.dataRef.key
          this.queryParam.treeNodeId = key.substr(1, key.length)
          //this.queryParam.treeNodeId = node.$options.propsData.eventKey
          this.queryParam.treeNodeType = node.$options.propsData.dataRef.type
        }
        //设备分类
        if (type === 'district') {
          this.queryParam.deviceCategoryId = node.$options.propsData.eventKey
        }

        this.tableTitle = node.$options.propsData.dataRef.title
        this.queryParam.pageNum = 1
        this.getList()
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
