<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1300"
    @cancel="cancel"
    modalHeight="620"
  >
    <div slot="content">
      <div style="height: 100%">
        <VxeTable
          ref="vxeTableRef"
          :columns="columns"
          tableTitle="预警消息"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          :rowConfig="{ isCurrent: true, isHover: true }"
        ></VxeTable>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getWarnMsgPushDetail } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'FormMaintenance',
    components: { AntModal, VxeTable, VxeTableForm },
    data() {
      return {
        formTitle: '预警消息详情',
        open: false,
        msgId: null,
        list: [],
        loading: false,
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '推送方式',
            field: 'pushMethod',
            slots: {
              default: ({ row }) => {
                return <span>{this.pushMethodFilter(row.pushMethod)}</span>
              },
            },
          },
          {
            title: '目标账号',
            field: 'address',
          },
          {
            title: '接收人',
            field: 'name',
          },
          {
            title: '发送状态',
            field: 'status',
            slots: {
              default: ({ row }) => {
                if (row.status == 1) {
                  return <span>正在发送</span>
                } else if (row.status == 2) {
                  return <span>成功</span>
                } else if (row.status == 3) {
                  return <span>失败</span>
                }
              },
            },
          },
          {
            title: '推送时间',
            field: 'createdTime',
          },
        ],
      }
    },
    methods: {
      // 取消按钮
      warnMsgDetails(msgId) {
        this.open = true
        this.msgId = msgId

        this.modalLoading = true
        this.getList()
      },
      getList() {
        {
          getWarnMsgPushDetail({ msgId: this.msgId.toString() }).then(res => {
            this.modalLoading = false
            this.list = res?.data
          })
        }
      },
      cancel() {
        this.open = false
        this.$emit('close')
      },
      pushMethodFilter(val) {
        if (val.length === 0) {
          return ''
        }
        const typeOptions = {
          sms: '短信',
          email: '邮件',
        }
        return typeOptions[val]
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  ::v-deep .vxe-table-box,
  ::v-deep .vxe-table-content .vxe-table-box .vxe-table-box-content {
    height: 500px !important;
  }
</style>
