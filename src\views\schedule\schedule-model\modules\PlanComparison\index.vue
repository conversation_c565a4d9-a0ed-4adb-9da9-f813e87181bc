<template>
  <a-modal
    :visible="visible"
    title="方案对比"
    :width="1600"
    :footer="null"
    @cancel="handleCancel"
    :destroyOnClose="true"
  >
    <div class="plan-comparison-container">
      <a-row :gutter="24" style="margin-right: 0;">
        <!-- 左侧方案 -->
        <a-col :span="12">
          <div class="plan-section">
            <PlanDetail 
              :planData="plan1"
              :detailData="detailData1"
              :loading="loading1"
              :fcstRangeOptions="fcstRangeOptions"
              :dispatchTypeOptions="dispatchTypeOptions"
              :sourceTypeOptions="sourceTypeOptions"
              :modelcode="plan1?.modelId || plan1?.schedulingId"
              planTitle="方案一"
            />
          </div>
        </a-col>
        
        <!-- 右侧方案 -->
        <a-col :span="12">
          <div class="plan-section">
            <PlanDetail 
              :planData="plan2"
              :detailData="detailData2"
              :loading="loading2"
              :fcstRangeOptions="fcstRangeOptions"
              :dispatchTypeOptions="dispatchTypeOptions"
              :sourceTypeOptions="sourceTypeOptions"
              :modelcode="plan2?.modelId || plan2?.schedulingId"
              planTitle="方案二"
            />
          </div>
        </a-col>
      </a-row>
    </div>
  </a-modal>
</template>

<script>
import { getEleVo } from '../../services.js'
import PlanDetail from './PlanDetail.vue'

export default {
  name: 'PlanComparison',
  components: {
    PlanDetail
  },
  props: {
    fcstRangeOptions: {
      type: Array,
      default: () => []
    },
    dispatchTypeOptions: {
      type: Array,
      default: () => []
    },
    sourceTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      plan1: null,
      plan2: null,
      detailData1: null,
      detailData2: null,
      loading1: false,
      loading2: false
    }
  },
  methods: {
    async handleShow(selectedPlans) {
      this.visible = true
      this.plan1 = selectedPlans[0] || null
      this.plan2 = selectedPlans[1] || null
      
      console.log('方案对比数据:', {
        plan1: this.plan1,
        plan2: this.plan2,
        plan1ModelId: this.plan1?.modelId,
        plan1SchedulingId: this.plan1?.schedulingId,
        plan2ModelId: this.plan2?.modelId,
        plan2SchedulingId: this.plan2?.schedulingId
      })
      
      // 获取详细数据
      if (this.plan1) {
        await this.getDetailData(this.plan1.schedulingId, 1)
      }
      if (this.plan2) {
        await this.getDetailData(this.plan2.schedulingId, 2)
      }
    },
    
    async getDetailData(schedulingId, planIndex) {
      try {
        if (planIndex === 1) {
          this.loading1 = true
        } else {
          this.loading2 = true
        }
        
        const response = await getEleVo({ schedulingId })
        
        console.log(`方案${planIndex}详细数据:`, response.data)
        
        if (planIndex === 1) {
          this.detailData1 = response.data
          // 如果API返回了modelId，更新plan1
          if (response.data?.modelId) {
            this.plan1 = { ...this.plan1, modelId: response.data.modelId }
          }
          this.loading1 = false
        } else {
          this.detailData2 = response.data
          // 如果API返回了modelId，更新plan2
          if (response.data?.modelId) {
            this.plan2 = { ...this.plan2, modelId: response.data.modelId }
          }
          this.loading2 = false
        }
      } catch (error) {
        console.error('获取详细数据失败:', error)
        if (planIndex === 1) {
          this.loading1 = false
        } else {
          this.loading2 = false
        }
      }
    },
    
    handleCancel() {
      this.visible = false
      this.plan1 = null
      this.plan2 = null
      this.detailData1 = null
      this.detailData2 = null
      this.$emit('close')
    }
  }
}
</script>

<style lang="less" scoped>
.plan-comparison-container {
  height: 70vh;
  overflow-y: auto;
  .plan-section {
    height: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    background: #ffffff;
  }
}
</style>
