<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="title">基本信息</div>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="鉴定名称" prop="safetyName">
                <a-input allowClear v-model="form.safetyName" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="开始时间" prop="startTime">
                <a-date-picker
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  v-model="form.startTime"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="评价单位" prop="evaluationUnit">
                <a-input allowClear v-model="form.evaluationUnit" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="鉴定单位" prop="safetyUnit">
                <a-input allowClear v-model="form.safetyUnit" placeholder="请输入" />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="所属工程" prop="projectId">
                <a-tree-select
                  v-model="form.projectId"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="projectOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'projectName',
                    key: 'projectId',
                    value: 'projectId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label="发起原因" prop="reason">
                <a-textarea v-model="form.reason" placeholder="请输入" allowClear />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="title">评价附件</div>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label=" " prop="evaluationAttaches">
                <UploadFile
                  :fileUrl.sync="form.evaluationAttaches"
                  :multiple="true"
                  listType="text"
                  folderName="projectCover"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="title">审查附件</div>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label=" " prop="reviewAttaches">
                <UploadFile
                  :fileUrl.sync="form.reviewAttaches"
                  :multiple="true"
                  listType="text"
                  folderName="projectCover"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="title">审定附件</div>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <a-form-model-item label=" " prop="examinationAttaches">
                <UploadFile
                  :fileUrl.sync="form.examinationAttaches"
                  :multiple="true"
                  listType="text"
                  folderName="projectCover"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="title">结果</div>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="工程等级">
                <a-select allowClear v-model="form.projectLevel" placeholder="请选择">
                  <a-select-option v-for="(d, index) in projectLevelOptions" :key="index" :value="d.value">
                    {{ d.label }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="成果应用">
                <a-select allowClear v-model="form.resultsApplication" placeholder="请选择">
                  <a-select-option
                    v-for="(d, index) in resultsApplicationOptions"
                    :key="index"
                    :value="d.value"
                  >
                    {{ d.label }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="状态">
                <a-select allowClear v-model="form.safetyStatus" placeholder="请选择">
                  <a-select-option v-for="(d, index) in safetyStatusOptions" :key="index" :value="d.value">
                    {{ d.label }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="结束时间">
                <a-date-picker
                  format="YYYY-MM-DD"
                  valueFormat="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                  v-model="form.endTime"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { addSafety, getSafety, updateSafety } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'

  export default {
    name: 'FormModal',
    props: ['projectOptions', 'projectLevelOptions', 'safetyStatusOptions', 'resultsApplicationOptions'],
    components: { AntModal, UploadFile },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        formTitle: '',
        // 表单参数
        form: {
          safetyId: undefined,
          safetyName: undefined,
          startTime: undefined,
          evaluationUnit: undefined,
          safetyUnit: undefined,
          projectId: undefined,
          reason: undefined,
          evaluationAttaches: [],
          reviewAttaches: [],
          examinationAttaches: [],
          projectLevel: undefined,
          resultsApplication: undefined,
          safetyStatus: undefined,
          endTime: undefined,
        },

        rules: {
          safetyName: [{ required: true, message: '鉴定名称不能为空', trigger: 'blur' }],
          startTime: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],

          evaluationUnit: [{ required: true, message: '评价单位不能为空', trigger: 'blur' }],
          safetyUnit: [{ required: true, message: '鉴定单位不能为空', trigger: 'blur' }],
          reason: [{ required: true, message: '发起原因不能为空', trigger: 'blur' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
        },
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(parentId) {
        this.open = true
        this.formTitle = '新增'
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.modalLoading = true
        this.formTitle = '修改'

        getSafety({ safetyId: record.safetyId }).then(res => {
          this.form = {
            ...res.data,
            evaluationAttaches: res.data.evaluationAttaches?.map(el => el.attachUrl),
            examinationAttaches: res.data.examinationAttaches?.map(el => el.attachUrl),
            reviewAttaches: res.data.reviewAttaches?.map(el => el.attachUrl),
          }
          this.form.projectLevel = this.form.projectLevel ? String(this.form.projectLevel) : ''
          this.form.resultsApplication = this.form.resultsApplication
            ? String(this.form.resultsApplication)
            : ''
          this.form.safetyStatus = this.form.safetyStatus ? String(this.form.safetyStatus) : ''
          this.modalLoading = false
        })
      },
      disabledDate(current) {
        return current < new Date(this.form.startTime)
      },
      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const params = {
              ...this.form,
            }

            if (this.form.safetyId !== undefined) {
              updateSafety(params)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            } else {
              addSafety(params)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
