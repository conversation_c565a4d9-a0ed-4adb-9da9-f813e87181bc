import request from '@/utils/request'

// 安全监测-安全工器具（消防检查）分页查询
export function getToolpage(data) {
  return request({
    url: '/prjstd/toolFire/page',
    method: 'post',
    data,
  })
}
// 增加
export function addTool(data) {
  return request({
    url: '/prjstd/toolFire/add',
    method: 'post',
    data,
  })
}
// 详情
export function getToolById(params) {
  return request({
    url: '/prjstd/toolFire/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editTool(data) {
  return request({
    url: '/prjstd/toolFire/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteTool(params) {
  return request({
    url: '/prjstd/toolFire/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 是否到期提醒
export function remindTool(params) {
  return request({
    url: '/prjstd/toolFire/isRemind',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 预警测试
export function getWarToolFireList(params) {
  return request({
    url: '/prjstd/toolFire/getWarToolFireList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 检查
export function checkAdd(data) {
  return request({
    url: '/prjstd/toolFire/checkAdd',
    method: 'post',
    data,
  })
}
// 检查记录
export function checkPage(data) {
  return request({
    url: '/prjstd/toolFire/checkPage',
    method: 'post',
    data,
  })
}
