<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1300"
    @cancel="cancel"
    modalHeight="900"
    :maskClosable="false"
    :footer="null"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <div>
        <div style="font-size: 16px; font-weight: 700; margin-bottom: 6px">模型基本信息</div>
        <div style="margin-bottom: 10px">
          <div style="display: flex; align-items: center; line-height: 24px">
            <div>模型名称：</div>
            <div style="color: #888888">{{ detail.schedulingName }}</div>
          </div>
          <div style="display: flex; align-items: center; line-height: 24px">
            <div>预演范围：</div>
            <div style="color: #888888">{{ fcstRangeOptions?.find(el => el.value === +detail.fcstRange)?.label }}</div>
          </div>
          <div style="display: flex; align-items: center; line-height: 24px">
            <div>应用场景：</div>
            <div style="color: #888888">{{ sceneOptions.find(el => el.value === detail.scene)?.label }}</div>
          </div>
          <div style="display: flex; align-items: center; line-height: 24px">
            <div>预演时间：</div>
            <div style="color: #888888">{{ detail.startTime }} - {{ detail.endTime }}</div>
          </div>
          <div style="display: flex; align-items: center; line-height: 24px">
            <div>调度方式：</div>
            <div style="color: #888888">
              {{ dispatchTypeOptions?.find(el => el.value === detail.dispathType)?.label }}
            </div>
          </div>
        </div>

        <div style="font-size: 16px; font-weight: 700; margin-bottom: 6px">降雨信息</div>
        <div style="height: 600px">
          <Rainfall
            v-if="!!detail.inputParameter?.rains"
            :rains="detail.inputParameter?.rains"
            :futureRains="detail.inputParameter?.futureRains"
            :scene="detail.inputParameter?.scene"
          />
        </div>

        <div style="font-size: 16px; font-weight: 700; margin-top: 10px; margin-bottom: 6px">初始水位</div>
        <div style="height: 400px">
          <InitialWater
            v-if="!!detail.inputParameter?.chSiteWaters"
            :chSiteWaters="detail.inputParameter?.chSiteWaters"
          />
        </div>

        <div style="font-size: 16px; font-weight: 700; margin-top: 10px; margin-bottom: 6px">调度信息</div>
        <div style="height: 600px">
          <DispatchInfo v-if="!!detail.inputParameter?.dispathType" :detail="detail" />
        </div>
      </div>
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import { getSchedulingDetail } from '../../services'
  import { dispatchTypeOptions, sceneOptions } from '../../config.js'
  import Rainfall from './Rainfall.vue'
  import InitialWater from './InitialWater.vue'
  import DispatchInfo from './DispatchInfo/index.vue'

  export default {
    name: 'ViewParams',
    components: { AntModal, Rainfall, InitialWater, DispatchInfo },
    props: ['fcstRangeOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '模型参数',
        detail: {},
        dispatchTypeOptions,
        sceneOptions,
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleShow(row) {
        this.open = true
        this.modalLoading = true
        getSchedulingDetail({ schedulingId: row.schedulingId }).then(res => {
          this.detail = { ...res.data, inputParameter: JSON.parse(res.data.inputParameter) }
          this.modalLoading = false
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
</style>
<style lang="less" scoped></style>
