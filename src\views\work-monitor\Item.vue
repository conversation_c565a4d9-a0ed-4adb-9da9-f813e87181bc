<template>
  <div class="item">
    <div class="name">{{ item.projectName }}</div>
    <div class="tab">
      <div v-if="filteredDevices.length > 0" class="tab-box">
        <div
          class="tab-item"
          v-for="(el, idx) in filteredDevices
            ?.slice(pageNum * 2 - 2, pageNum * 2) || []"
          @click="onDeviceClick(el)"
          :style="
            activeDevice?.deviceCode === el.deviceCode
              ? { background: '#4C76F1', color: '#ffffff', 'margin-left': idx === 0 ? '0px' : '8px' }
              : { 'margin-left': idx === 0 ? '0px' : '8px' }
          "
        >
          <SvgIcon
            :iconClass="
              allIcons.includes(el.deviceCategoryCode)
                ? el.isOnline && el.isOpen == 1
                  ? el.deviceCategoryCode + '-online'
                  : el.isOnline && (el.isOpen == 0 || el.isOpen == null)
                    ? el.deviceCategoryCode + '-off'
                    : el.deviceCategoryCode
                : 'other'
            "
            style="font-size: 14px; min-width: 16px; margin-right: 8px"
          />
          <a-tooltip v-if="el.deviceName.length > 4">
            <template slot="title">
              {{ el.deviceName }}
            </template>
            <div class="tab-name">{{ el.deviceName }}</div>
          </a-tooltip>
          <div v-else class="tab-name">
            {{ el.deviceName }}
          </div>
        </div>
      </div>

      <div style="width: 36px">
        <div v-if="filteredDevices.length > 2" class="arrow-box" style="margin: 0 -10px 0 5px">
          <SvgIcon iconClass="arrow-left" class="arrow" @click="pageNum = pageNum === 1 ? 1 : pageNum - 1" />
          <!-- <span style="margin: 0 2px">{{ pageNum }}</span> -->
          <SvgIcon
            iconClass="arrow-right"
            class="arrow"
            @click="
              pageNum =
                pageNum === Math.ceil(filteredDevices.length / 2) ? Math.ceil(filteredDevices.length / 2) : pageNum + 1
            "
          />
        </div>
      </div>
    </div>

    <!-- 工程 -->
    <div class="content" v-if="!activeDevice">
      <div class="indicate-item" v-if="item.map.hasOwnProperty('runTime')">
        <span style="color: #4e5969">运行台时:&nbsp;</span>
        <span style="font-weight: 700">
          {{ item.map.runTime === null ? '--' : item.map.runTime }}
        </span>
      </div>
      <div class="indicate-item" v-if="item.map.hasOwnProperty('rateFlow')">
        <span style="color: #4e5969">翻水量:&nbsp;</span>
        <span style="font-weight: 700">
          {{ item.map.rateFlow === null ? '--' : item.map.rateFlow + '万m³' }}
        </span>
      </div>
      <div class="indicate-item" v-if="item.map.hasOwnProperty('floodFlow')">
        <span style="color: #4e5969">排涝量:&nbsp;</span>
        <span style="font-weight: 700">
          {{ item.map.floodFlow === null ? '--' : item.map.floodFlow + '万m³' }}
        </span>
      </div>

      <div class="indicate-item" v-if="Object.keys(item.map).length === 0">
        <span style="color: #4e5969">暂无数据</span>
      </div>
    </div>

    <!-- 工情设备 -->
    <div class="content" v-if="activeDevice?.type === 'AUTOMATION'">
      <div class="indicate-item" v-for="(el, index) in detailData?.renderConfig" :key="index">
        <span style="color: #4e5969">{{ el.label }}:&nbsp;</span>
        <span
          :style="
            el.field === 'isOpen' && detailData[el.field] ? { color: '#4C76F1', fontWeight: 700 } : { fontWeight: 700 }
          "
        >
          {{
            detailData[el.field] === null
              ? '--'
              : el.field === 'isOpen'
                ? detailData[el.field]
                  ? '开启'
                  : '关闭'
                : detailData[el.field]
          }}{{ el.unit || '' }}
        </span>
      </div>
    </div>

    <!-- 水雨情设备 -->
    <div class="content telemetry" v-if="activeDevice?.type === 'TELEMETRY'">
      <div class="indicate-item" v-for="(el, index) in detailData || []" :key="index">
        <span style="color: #4e5969">{{ el.indexName }}:&nbsp;</span>
        <span style="font-weight: 700">
          {{ el.indexCodeValue === null ? '--' : el.indexCodeValue }}{{ el.unit || '' }}
        </span>
      </div>

      <div class="indicate-item" v-if="detailData?.length === 0">
        <span style="color: #4e5969">暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { allIcons } from '@/assets/icons/index'
  import { getProjectDeviceDetail, getProjectDeviceTerminal } from './services.js'
  import { contentConfig } from './config.js'

  export default {
    name: 'Item',
    components: {},
    props: {
      item: {
        type: Object,
        required: true,
      },
      timeRange: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        pageNum: 1,
        activeDevice: null,
        allIcons: allIcons,
        detailData: null,
      }
    },
    computed: {
      filteredDevices() {
        return this.item.devices?.filter(el => 
          el.deviceCategoryCode == 'P_2' || 
          el.deviceCategoryCode == 'OIL_PUMP' || 
          el.deviceCategoryCode == 'P' || 
          el.deviceCategoryCode == 'G_1' || 
          el.deviceCategoryCode == 'G' || 
          el.deviceCategoryCode == 'G_2'
        ) || []
      }
    },
    created() {},
    methods: {
      onDeviceClick(el) {
        if (this.activeDevice?.deviceCode === el.deviceCode) {
          this.activeDevice = null
          return
        }
        this.activeDevice = el
        this.getDetail()
      },
      getDetail() {
        if (this.activeDevice.type === 'AUTOMATION') {
          getProjectDeviceDetail({
            deviceId: this.activeDevice.deviceId,
            startTime: this.timeRange[0],
            endTime: this.timeRange[1],
          }).then(res => {
            this.detailData = { ...res.data, renderConfig: contentConfig[res.data.deviceCategoryCode] }
          })
        }
        if (this.activeDevice.type === 'TELEMETRY') {
          getProjectDeviceTerminal({
            deviceId: this.activeDevice.deviceId,
          }).then(res => {
            this.detailData =
              res.data.length > 0 ? [...res.data, { indexName: '更新时间', indexCodeValue: res.data[0].dateTime }] : []
          })
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .item {
    padding: 20px;
    background: #fff;
    border-radius: 20px;
    user-select: none;
    display: flex;
    flex-direction: column;

    .name {
      font-size: 20px;
      padding-bottom: 20px;
      font-weight: 700;
      user-select: none;
    }

    .tab {
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .tab-box {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 100%;

        .tab-item {
          width: 50%;

          height: 100%;
          display: flex;
          align-items: center;
          // justify-content: center;
          cursor: pointer;
          margin-left: 4px;

          border-radius: 36px;
          padding: 0px 8px;
          background: #e2ebf3;
          border: 1px solid #c8cfdc;

          .tab-name {
            // width: calc(100% - 20px);
            display: -webkit-box;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            -webkit-line-clamp: 1;
            overflow: hidden;
          }
        }
      }

      .arrow-box {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 4px;

        .arrow {
          cursor: pointer;
          font-size: 20px;
          color: #86909c;
        }
      }
    }

    .content {
      margin-top: 20px;
      .indicate-item {
        margin-bottom: 10px;
        line-height: 20px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    .telemetry {
      flex: 1;
      overflow: auto;
    }
  }
</style>
