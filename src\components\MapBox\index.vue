<template>
  <div id="mapBox" ref="mapBoxRef"></div>
</template>

<script lang="jsx">
  import mapboxgl from 'mapbox-gl'
  import 'mapbox-gl/dist/mapbox-gl.css'
  // import MapboxLanguage from '@mapbox/mapbox-gl-language'
  import { getValueByKey } from '@/api/common'

  export default {
    name: 'MapBox',
    props: [],
    mapIns: null,
    components: {},
    data() {
      return {
        center: null,
      }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {
      getValueByKey('patrolCentralPoint').then(res => {
        this.center = res.data.split(',').map(el => +el)
        this.$nextTick(() => {
          this.createMap(this.$refs['mapBoxRef'])
        })
      })
    },
    methods: {
      createMap(mapNode) {
        mapboxgl.accessToken =
          'pk.eyJ1IjoiaGhjY2RldiIsImEiOiJjbTBxaDBhdmswYzZjMmpwdzE4eWU2d2NvIn0._XkHfjxUcOLIZ7bIJUcbWw'
        this.mapIns = new mapboxgl.Map({
          container: mapNode,
          style: {
            version: 8,
            sources: {
              composite: {
                url: 'mapbox://mapbox.mapbox-streets-v8',
                type: 'vector',
              },
            },
            sprite: 'mapbox://sprites/mapbox/streets-v8',
            glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
            layers: [
              {
                id: 'background',
                type: 'background',
                paint: {
                  'background-color': 'transparent',
                },
                interactive: true,
              },
            ],
          },
          center: this.center,
          zoom: 10,
          maxZoom: 17.49, // 天地图大于这个值时，图层会消失
          // minZoom: 4,
          pitch: 0, // 相对于地面3D视角的角度
          antialias: false, //抗锯齿，通过false关闭提升性能
          // maxBounds: [
          //   [73.66, 3.86],
          //   [135.05, 53.55],
          // ],
          ...this.$attrs?.options,
        })
        // ### 标签汉化 //style已经设置中文
        // this.mapIns.addControl(new MapboxLanguage({ defaultLanguage: 'zh-Hans' }))
        // ### 添加导航控制条
        this.mapIns.addControl(new mapboxgl.NavigationControl({ showCompass: false }), 'top-left')
        // 鼠标样式
        this.mapIns.getCanvas().style.cursor = 'auto'

        this.mapIns.on('load', () => {
          this.$listeners?.onMapMounted && this.$listeners?.onMapMounted(this.mapIns)
        })

        this.mapIns.on('style.load', () => {
          this.$listeners?.onMapStyleLoad && this.$listeners?.onMapStyleLoad(this.mapIns)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  #mapBox {
    width: 100%;
    height: 100%;
    position: relative;
  }
</style>
