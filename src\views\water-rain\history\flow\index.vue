<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane key="1" tab="按站点">
          <TreeGeneral
            v-if="treeTabKey === '1' && !!categoryTreeOptions.dataSource.length"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :currentKeys="currentKeys"
            :treeOptions="categoryTreeOptions"
            @onTreeMounted="onTreeMounted"
            @check="(keys, nodes) => clickTreeNode(keys, nodes)"
          />
        </a-tab-pane>
        
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <a-card :bordered="false" style="margin-bottom: 10px">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="40">
              <a-col :span="24">
                <a-form-item label="统计指标">
                  <a-radio-group v-model="tabVal" @change="onTabChange" buttonStyle="outline">
                    <a-radio-button v-for="(el, idx) in tabOptions" :key="idx" :value="el.value">
                      {{ el.label }}
                    </a-radio-button>
                  </a-radio-group>
                </a-form-item>
              </a-col>

              <a-col :span="17" v-if="tabVal == '1'">
                <a-form-item label="选择时间">
                  <div class="time-picker">
                    <a-date-picker
                      style="width: 30%"
                      v-model="queryParam.startDate"
                      format="YYYY-MM-DD"
                      valueFormat="YYYY-MM-DD"
                      :allowClear="false"
                    />
                    <a-select :options="hoursOptions" v-model="queryParam.startHour" style="width: 15%" />
                    <a-date-picker
                      style="width: 30%"
                      v-model="queryParam.endDate"
                      format="YYYY-MM-DD"
                      valueFormat="YYYY-MM-DD"
                      :allowClear="false"
                    />
                    <a-select :options="hoursOptions" v-model="queryParam.endHour" style="width: 15%" />
                  </div>
                </a-form-item>
              </a-col>

              <a-col :span="17" v-if="tabVal == '2'">
                <a-form-item label="选择日期">
                  <a-range-picker
                    v-model="queryParam.rangeDate"
                    format="YYYY-MM-DD"
                    :allowClear="false"
                    valueFormat="YYYY-MM-DD"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="5" v-if="tabVal == '3'">
                <a-form-item label="选择时间">
                  <a-select v-model="queryParam.type" :options="timeRangesOptions" @change="onTimeSelectChange" />
                </a-form-item>
              </a-col>

              <a-col :span="12" v-if="tabVal == '3'">
                <a-form-item label="" v-if="queryParam.type == '1'">
                  <div class="time-picker">
                    <a-date-picker
                      style="width: 30%"
                      v-model="queryParam.startDate"
                      format="YYYY-MM-DD"
                      valueFormat="YYYY-MM-DD"
                      :allowClear="false"
                    />
                    <a-select :options="hoursOptions" v-model="queryParam.startHour" style="width: 15%" />
                    <a-date-picker
                      style="width: 30%"
                      v-model="queryParam.endDate"
                      format="YYYY-MM-DD"
                      valueFormat="YYYY-MM-DD"
                      :allowClear="false"
                    />
                    <a-select :options="hoursOptions" v-model="queryParam.endHour" style="width: 15%" />
                  </div>
                </a-form-item>

                <a-form-item label="" v-if="queryParam.type == '2'">
                  <div class="time-picker">
                    <a-range-picker
                      style="width: 100%"
                      v-model="queryParam.rangeDate"
                      :allowClear="false"
                      format="YYYY-MM-DD"
                      valueFormat="YYYY-MM-DD"
                    />
                  </div>
                </a-form-item>

                <a-form-item label="" v-if="queryParam.type == '3'">
                  <div class="time-picker">
                    <a-range-picker
                      style="width: 100%"
                      v-model="queryParam.rangeDate"
                      :mode="['month', 'month']"
                      :allowClear="false"
                      valueFormat="YYYY-MM"
                      format="YYYY-MM"
                    />
                  </div>
                </a-form-item>
              </a-col>

              <a-col :span="7">
                <span class="table-page-search-submitButtons">
                  <a-button type="primary" icon="search" @click="this.handleQuery">查询</a-button>
                  <a-button style="margin-left: 8px" icon="redo" @click="this.resetQuery">重置</a-button>

                  <a-button
                    style="margin-left: 8px"
                    type="primary"
                    icon="download"
                    :loading="exportLoading"
                    @click="handleExport"
                  >
                    导出
                  </a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>

      <div class="history-content">
        <div class="view-tab-box" v-if="tabVal !== '3'">
          <a-radio-group v-model="viewTabVal" button-style="solid" @change="onViewTabChange">
            <a-radio-button value="1">表单</a-radio-button>
            <a-radio-button value="2">图表</a-radio-button>
          </a-radio-group>
        </div>

        <div class="history-table" style="padding-top: 24px" v-if="viewTabVal == '1'">
          <VxeTable
            :key="tableKey"
            v-if="columns.length"
            ref="vxeTableRef"
            border="full"
            :isDrop="false"
            :isShowTableHeader="false"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :otherHeight="166"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
            :show-footer="tabVal != '3' ? false : true"
            :footer-method="footerMethod"
            :merge-footer-items="[{ row: 0, col: 0, rowspan: 0, colspan: 3 }]"
            :footer-row-style="{ background: '#F8F8F9' }"
          >
            <div class="vxe-table-footer" v-if="tabVal != '3'" slot="footer">
              <div class="footer-box" v-if="tabVal == '1'">
                <div class="footer-item">
                  时段最大流速(m/s)：{{ statisticsInfo.maxInfo.value }}　{{ statisticsInfo.maxInfo.name }}　{{
                    statisticsInfo.maxInfo.time
                  }}
                </div>
                <div class="footer-item">
                  时段最低流速(m/s)：{{ statisticsInfo.minInfo.value }}　{{ statisticsInfo.minInfo.name }}　{{
                    statisticsInfo.minInfo.time
                  }}
                </div>
              </div>
              <div class="footer-box" v-if="tabVal == '2'">
                <div class="footer-item">
                  时段最大流量(m³/s)：{{ statisticsInfo.maxInfo.value }}　{{ statisticsInfo.maxInfo.name }}　{{
                    statisticsInfo.maxInfo.tim
                  }}
                </div>
                <div class="footer-item">
                  时段最低流量(m³/s)：{{ statisticsInfo.minInfo.value }}　{{ statisticsInfo.minInfo.name }}　{{
                    statisticsInfo.minInfo.time
                  }}
                </div>
              </div>
            </div>
          </VxeTable>
        </div>
        <div class="history-chart" v-if="viewTabVal == '2'">
          <div class="chart-info-box" v-if="tabVal == '1'">
            <div class="chart-info-item">
              时段最大流速(m/s)：{{ statisticsInfo.maxInfo.value }}　{{ statisticsInfo.maxInfo.name }}　{{
                statisticsInfo.maxInfo.time
              }}
            </div>
            <div class="chart-info-item">
              时段最低流速(m/s)：{{ statisticsInfo.minInfo.value }}　{{ statisticsInfo.minInfo.name }}　{{
                statisticsInfo.minInfo.time
              }}
            </div>
          </div>
          <div class="chart-info-box" v-if="tabVal == '2'">
            <div class="chart-info-item">
              时段最大流量(m³/s)：{{ statisticsInfo.maxInfo.value }}　{{ statisticsInfo.maxInfo.name }}　{{
                statisticsInfo.maxInfo.time
              }}
            </div>
            <div class="chart-info-item">
              时段最低流量(m³/s)：{{ statisticsInfo.minInfo.value }}　{{ statisticsInfo.minInfo.name }}　{{
                statisticsInfo.minInfo.time
              }}
            </div>
          </div>
          <LineEchart :key="tableKey" :dataSource="chartData" :custom="chartCustom" height="570px" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { pageVelocity, pageFlow, pageAmount } from './services'
  import { getSiteDistrictTree, getSiteRiverTree, getSiteCategoryTree } from '@/api/common'
  import TreeGeneral from '@/components/TreeGeneral/multiTree.vue'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import { hoursOptions, timeRangesOptions } from './config'
  import { LineEchart } from '@/components/Echarts'
  import { dealNumber, getFixedNum } from '@/utils/dealNumber.js'
  import * as _ from 'lodash'
  import * as XLSX from 'xlsx-js-style'
  import MergeCell from '@/utils/mergeCell'

  export default {
    name: 'Flow',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      LineEchart,
    },
    data() {
      return {
        treeTabKey: '1',
        exportLoading: false,
        currentKeys: [],
        nodes: [],
        categoryTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
          },
        },
  
        tabVal: '1',

        tabOptions: [
          { label: '指标流速', value: '1' },
          { label: '流量', value: '2' },
          { label: '水量', value: '3' },
        ],

        hoursOptions: hoursOptions,
        timeRangesOptions: timeRangesOptions,
        viewTabVal: '1',
        pageIndex: 0,
        pageSize: 10,
        queryParam: {
          siteIds: [],

          startDate: moment().subtract(12, 'hour').format('YYYY-MM-DD'),
          startHour: moment().subtract(12, 'hour').format('HH:00:00'),
          endDate: moment().format('YYYY-MM-DD'),
          endHour: moment().format('HH:00:00'),

          rangeDate: [moment().subtract(1, 'months'), moment()],
          type: '1',
          pageNum: 1,
          pageSize: 10,
        },

        tableKey: 1,
        list: [],
        loading: false,
        total: 0,
        columns: [],
        statisticsInfo: {
          maxInfo: null,
          minInfo: null,
        },
        chartData: [{ name: '', data: [] }],
        chartCustom: {
          shortValue: true,
          rYAxis: true,
          showAreaStyle: false,
          legend: { left: '2%' },
          yLabel: '',
          rYLabel: '',
        },
      }
    },
    created() {
      let param = { labels: '1', indexCode: 'flow' }
      getSiteCategoryTree(param).then(res => {
        this.categoryTreeOptions.dataSource = res.data
      })

      this.resetTime()
    },
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.pageSize = pageSize
        this.pageIndex++
        if (this.pageIndex < 2) {
          this.getList()
        }
      },
      /** 查询列表 */
      getList(isExport, callback) {
        if (!isExport) this.loading = true

        if (this.tabVal == '1') {
          const params = {
            ...this.queryParam,
            pageSize: isExport ? Number.MAX_SAFE_INTEGER : this.queryParam.pageSize,
            type: '4',
            startTime: `${this.queryParam.startDate} ${this.queryParam.startHour}`,
            endTime: `${this.queryParam.endDate} ${this.queryParam.endHour}`,
            startDate: undefined,
            startHour: undefined,
            endDate: undefined,
            endHour: undefined,
            rangeDate: undefined,
          }

          pageVelocity(params).then(res => {
            const columns = [
              { type: 'seq', title: '序号', width: 50 },
              {
                title: '监测时间',
                field: 'dateTime',
                fixed: 'left',
                minWidth: 150,
              },
              ...this.currentKeys.map(el => {
                const item = this.nodes.find(ele => ele.id == el)
                return {
                  title: item.name,
                  children: [
                    {
                      field: `${item.id.slice(1, item.id.length)}#flowWaterValue`,
                      title: '水位(m)',
                      minWidth: 90,
                    },
                    {
                      field: `${item.id.slice(1, item.id.length)}#flowVelocityValue`,
                      title: '指标流速(m/s)',
                      minWidth: 120,
                    },
                  ],
                }
              }),
            ]
            // 处理小数位
            const list = (res.data || []).map(el => {
              const ids = this.nodes
                .filter(ele => this.currentKeys.some(element => element == ele.id))
                .map(el => el.id.slice(1, el.id.length))

              let velocityArr = []
              Object.keys(el).forEach(key => {
                if (key.includes('flowVelocityValue')) {
                  velocityArr.push({ label: key, value: el[key] })
                }
              })

              const indicators = {}
              ids.forEach(ele => {
                indicators[`${ele}#flowWaterValue`] = getFixedNum(el[`${ele}#flowWaterValue`], 2)
                indicators[`${ele}#flowVelocityValue`] = getFixedNum(el[`${ele}#flowVelocityValue`], 2)
              })
              return {
                ...el,
                ...indicators,
                maxFlowVelocity: _.maxBy(velocityArr, 'value'),
                minFlowVelocity: _.minBy(velocityArr, 'value'),
              }
            })

            if (isExport) {
              callback(list)
            } else {
              this.tableKey += 1

              this.list = list
              this.columns = columns
              this.total = res.data?.[0]?.total || 0

              // 最大最小值
              const max = _.maxBy(this.list, 'maxFlowVelocity.value')
              const min = _.minBy(this.list, 'maxFlowVelocity.value')
              this.statisticsInfo = {
                maxInfo: max
                  ? {
                      value: getFixedNum(max.maxFlowVelocity.value, 3),
                      time: max.dateTime,
                      name: this.nodes.find(
                        ele => ele.id.slice(1, ele.id.length) == +max.maxFlowVelocity.label.split('#')[0],
                      )?.name,
                    }
                  : {},
                minInfo: min
                  ? {
                      value: getFixedNum(min.minFlowVelocity.value, 3),
                      time: min.dateTime,
                      name: this.nodes.find(
                        ele => ele.id.slice(1, ele.id.length) == +min.minFlowVelocity.label.split('#')[0],
                      )?.name,
                    }
                  : {},
              }

              let arr = []
              this.currentKeys.forEach(el => {
                const item = this.nodes.find(ele => ele.id == el)
                arr.push(
                  {
                    name: `${item.name}指标流速`,
                    data: this.list.map(ele => {
                      return [ele.dateTime, ele[`${item.id.slice(1, item.id.length)}#flowVelocityValue`]]
                    }),
                  },
                  {
                    name: `${item.name}水位`,
                    yAxisIndex: 1,
                    data: this.list.map(ele => {
                      return [ele.dateTime, ele[`${item.id.slice(1, item.id.length)}#flowWaterValue`]]
                    }),
                  },
                )
              })
              this.chartData = arr
              this.chartCustom = {
                ...this.chartCustom,
                yLabel: '指标流速(m/s)',
                rYLabel: '水位(m)',
                color: [
                  '#A073DC',
                  '#EF8432',
                  '#58A9FB',
                  '#B5E241',
                  '#4363d8',
                  '#911eb4',
                  '#00A316',
                  '#fabed4',
                  '#469990',
                  '#9A6324',
                  '#aaffc3',
                  '#808000',
                  '#000075',
                  '#a9a9a9',
                ],
              }

              this.loading = false
            }
          })
        }
        if (this.tabVal == '2') {
          const params = {
            ...this.queryParam,
            pageSize: isExport ? Number.MAX_SAFE_INTEGER : this.queryParam.pageSize,
            type: '2',
            startTime: moment(this.queryParam.rangeDate[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            endTime: moment(this.queryParam.rangeDate[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            startDate: undefined,
            startHour: undefined,
            endDate: undefined,
            endHour: undefined,
            rangeDate: undefined,
          }
          pageFlow(params).then(res => {
            // 处理小数位
            const list = (res.data?.data || []).map(el => {
              return {
                ...el,
                maxFlow: _.maxBy(el.historyValue, 'flowValue'),
                minFlow: _.minBy(el.historyValue, 'flowValue'),
                historyValue: el.historyValue.map(ele => {
                  return {
                    ...ele,
                    flowValue: dealNumber(ele.flowValue, 3),
                    waterLevelValue: getFixedNum(ele.waterLevelValue, 2),
                    flowVelocityValue: getFixedNum(ele.flowVelocityValue, 3),
                  }
                }),
              }
            })

            const columns = [
              { type: 'seq', title: '序号', width: 50 },
              {
                title: '站点名称',
                field: 'siteName',
                minWidth: 120,
              },
              {
                title: '站点类型',
                field: 'objectCategoryName',
                minWidth: 120,
              },
              ...(list?.[0]?.historyValue || []).map((el, i) => {
                return {
                  title: el.dateTime,
                  children: [
                    {
                      field: `historyValue[${i}].waterLevelValue`,
                      title: '水位(m)',
                      minWidth: 90,
                    },
                    {
                      field: `historyValue[${i}].flowVelocityValue`,
                      title: '平均流速(m/s)',
                      minWidth: 120,
                    },
                    {
                      field: `historyValue[${i}].flowValue`,
                      title: '流量(m³/s)',
                      minWidth: 120,
                    },
                  ],
                }
              }),
            ]

            if (isExport) {
              callback(list)
            } else {
              this.tableKey += 1
              this.list = list
              this.columns = columns
              // 最大最小值
              const max = _.maxBy(this.list, 'maxFlow.flowValue')
              const min = _.minBy(this.list, 'minFlow.flowValue')
              this.statisticsInfo = {
                maxInfo: max
                  ? {
                      value: dealNumber(max.maxFlow.flowValue),
                      time: max.maxFlow.dateTime,
                      name: max.siteName,
                    }
                  : {},
                minInfo: min
                  ? {
                      value: dealNumber(min.minFlow.flowValue),
                      time: min.minFlow.dateTime,
                      name: min.siteName,
                    }
                  : {},
              }

              this.total = res.data?.total || 0

              let arr = []
              this.list.forEach(el => {
                arr.push(
                  {
                    name: `${el.siteName}流量`,
                    data: el.historyValue.map(ele => {
                      return [ele.dateTime, ele.flowValue]
                    }),
                  },
                  {
                    name: `${el.siteName}水位`,
                    yAxisIndex: 1,
                    data: el.historyValue.map(ele => {
                      return [ele.dateTime, ele.waterLevelValue]
                    }),
                  },
                )
              })
              this.chartData = arr
              this.chartCustom = {
                ...this.chartCustom,
                yLabel: '流量(m³/s)',
                rYLabel: '水位(m)',
                color: [
                  '#74CF70',
                  '#EF8432',
                  '#58A9FB',
                  '#B5E241',
                  '#4363d8',
                  '#911eb4',
                  '#00A316',
                  '#fabed4',
                  '#469990',
                  '#dcbeff',
                  '#9A6324',
                  '#aaffc3',
                  '#808000',
                  '#000075',
                  '#a9a9a9',
                ],
              }

              this.loading = false
            }
          })
        }
        if (this.tabVal == '3') {
          const params = {
            ...this.queryParam,
            pageSize: isExport ? Number.MAX_SAFE_INTEGER : this.queryParam.pageSize,
            startTime: `${moment(this.queryParam.startDate).format('YYYY-MM-DD')} ${this.queryParam.startHour}`,
            endTime: `${moment(this.queryParam.endDate).format('YYYY-MM-DD')} ${this.queryParam.endHour}`,
            startDate: undefined,
            startHour: undefined,
            endDate: undefined,
            endHour: undefined,
            rangeDate: undefined,
          }
          if (this.queryParam.type == '2') {
            params.startTime = moment(this.queryParam.rangeDate[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
            params.endTime = moment(this.queryParam.rangeDate[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
          }

          if (this.queryParam.type == '3') {
            params.startTime = moment(this.queryParam.rangeDate[0]).startOf('month').format('YYYY-MM-DD HH:mm:ss')
            params.endTime = moment(this.queryParam.rangeDate[1]).endOf('month').format('YYYY-MM-DD HH:mm:ss')
          }

          pageAmount(params).then(res => {
            const list = (res.data?.data || []).map(el => {
              return {
                ...el,
                historyAmountWater: el.historyAmountWater.map(ele => {
                  return {
                    ...ele,
                    forwardDirectionValue: getFixedNum(ele.forwardDirectionValue, 2),
                    reverseDirectionValue: getFixedNum(ele.reverseDirectionValue, 2),
                  }
                }),
              }
            })

            const columns = [
              { type: 'seq', title: '序号', width: 50 },
              {
                title: '站点名称',
                field: 'siteName',
                minWidth: 120,
                fixed: 'left',
              },
              {
                title: '站点类型',
                field: 'objectCategoryName',
                minWidth: 120,
                fixed: 'left',
              },
              ...(this.list?.[0]?.historyAmountWater || []).map((el, i) => {
                return {
                  title: el.dateTime,
                  children: [
                    {
                      field: `historyAmountWater[${i}].forwardDirectionValue`,

                      title: '正向水量(10⁴m³)',
                      minWidth: 130,
                    },
                    {
                      field: `historyAmountWater[${i}].reverseDirectionValue`,

                      title: '逆向水量(10⁴m³)',
                      minWidth: 130,
                    },
                  ],
                }
              }),
            ]

            if (isExport) {
              callback(list)
            } else {
              this.tableKey += 1
              this.list = list
              this.columns = columns
              this.total = res.data?.total || 0
              this.loading = false
            }
          })
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetTime()
        this.queryParam = {
          ...this.queryParam,
          siteCode: undefined,
          siteName: undefined,
          pageNum: 1,
          // pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.handleQuery()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      sumNum(list, field) {
        let count = 0
        list.forEach(item => {
          count += Number(_.get(item, field))
        })
        return getFixedNum(count, 2)
      },
      footerMethod({ columns, data }) {
        const footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '总计水量（10⁴m³）：'
            }
            if (_columnIndex > 2) {
              return this.sumNum(data, column.property)
            }
            return null
          }),
        ]
        return footerData
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 树加载完成后
      onTreeMounted(checkedKeys, nodes) {
        this.currentKeys = checkedKeys
        this.nodes = nodes
        this.queryParam.siteIds = checkedKeys.map(el => el.slice(1, el.length))

        this.handleQuery()
      },

      clickTreeNode(checkedKeys, nodes) {
        this.currentKeys = checkedKeys
        this.nodes = nodes
        this.queryParam.siteIds = checkedKeys.map(el => el.slice(1, el.length))

        this.handleQuery()
      },

      onTabChange(val) {
        if (val.target.value == '3') {
          this.viewTabVal = '1'
        }
        this.resetTime()
        this.getList()
      },

      resetTime() {
        if (this.tabVal == '1') {
          this.queryParam = {
            ...this.queryParam,
            startDate: moment().subtract(12, 'hour').format('YYYY-MM-DD'),
            startHour: moment().subtract(12, 'hour').format('HH:00:00'),
            endDate: moment().format('YYYY-MM-DD'),
            endHour: moment().format('HH:00:00'),
          }
        }
        if (this.tabVal == '2') {
          this.queryParam.rangeDate = [moment().subtract(1, 'months'), moment()]
        }
        if (this.tabVal == '3') {
          this.queryParam = {
            ...this.queryParam,
            type: '1',
            startDate: moment().subtract(12, 'hour').format('YYYY-MM-DD'),
            startHour: moment().subtract(12, 'hour').format('HH:00:00'),
            endDate: moment().format('YYYY-MM-DD'),
            endHour: moment().format('HH:00:00'),
          }
        }
      },

      onTimeSelectChange(val) {
        if (val == '1') {
          //逐时
          this.queryParam = {
            ...this.queryParam,
            type: '1',
            startDate: moment().subtract(12, 'hour').format('YYYY-MM-DD'),
            startHour: moment().subtract(12, 'hour').format('HH:00:00'),
            endDate: moment().format('YYYY-MM-DD'),
            endHour: moment().format('HH:00:00'),
          }
        }
        if (val == '2') {
          //逐日
          this.queryParam = {
            ...this.queryParam,
            type: '2',
            rangeDate: [moment().subtract(1, 'months'), moment()],
          }
        }
        if (val == '3') {
          //逐月
          this.queryParam = {
            ...this.queryParam,
            type: '3',
            rangeDate: [moment().subtract(12, 'months').format('YYYY-MM'), moment().format('YYYY-MM')],
          }
        }
      },

      onViewTabChange(e) {
        if (e.target.value == '1') {
          // this.queryParam.pageSize = 10
          this.handleQuery()
        } else {
          this.queryParam.pageSize = Number.MAX_SAFE_INTEGER
          this.handleQuery()
        }
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        this.getList(true, data => {
          this.exportLoading = false

          const { headerValue, headerMerge, bodyValue, deep, dealBorderAndCenter } = new MergeCell(
            this.columns.slice(1, this.columns.length),
            data,
          )
          const header = dealBorderAndCenter(headerValue)
          const body = dealBorderAndCenter(bodyValue)

          const ws = XLSX.utils.json_to_sheet([])
          ws['!merges'] = headerMerge
          ws['!cols'] = Object.keys(bodyValue[0]).map((el, i) => ({ wpx: i === 0 ? 160 : 90 }))

          XLSX.utils.sheet_add_aoa(ws, header, { origin: 'A1' }) // 将js数据转换为工作表
          XLSX.utils.sheet_add_aoa(ws, body, { origin: `A${deep + 2}` }) // 将js数据转换为工作表

          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

          XLSX.writeFile(wb, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}.xlsx`)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('../history.less');

  .view-tab-box {
    padding: 10px 10px 0;
  }

  .time-picker {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .history-chart {
    text-align: right;
    .chart-info-box {
      color: #000000a6;
      font-size: 12px;
      display: inline-block;
      text-align: left;
      padding-right: 20px;
      width: 400px;
    }
  }

  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;

      .ant-tabs-nav-container {
        height: auto;

        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
