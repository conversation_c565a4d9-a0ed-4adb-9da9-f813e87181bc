<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">{{ observationType == 1 ? '沉降观测名称：' : '位移观测名称：' }}</label>
            <span class="common-value-text">
              {{ form.observationName }}
            </span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">
              {{ form.projectName }}
            </span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">年份：</label>
            <span class="common-value-text">
              {{ form.year }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div>
            申请文件:
            <div
              class="file-item"
              v-for="(el, i) in form.positionAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getObservationById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    components: { AntModal },
    props: ['projectOptions', 'observationType'],
    data() {
      return {
        modalLoading: false,
        formTitle: '',
        form: {
          observationId: null,
          observationName: '',
          year: null,
          positionAttaches: [],
          projectId: null,
        },
        open: false,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },

      downLoad(url) {
        window.open(url)
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '查看'
          getObservationById({ observationId: row.observationId }).then(res => {
            if (res.code == 200) {
              this.form = res.data

              this.modalLoading = false
            }
          })
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
