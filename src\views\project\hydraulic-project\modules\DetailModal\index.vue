<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :modalWidth="`${windowSize.width}`"
    :modalHeight="`${windowSize.height}`"
    @cancel="cancel"
    @onFullScreen="onFullScreen"
    :footer="null"
    ref="detailModalRef"
  >
    <div slot="content" layout="vertical" class="detail-content">
      <div class="detail-tab">
        <a-tabs v-model="tabVal" type="line" :tab-position="'left'" :style="{ height: '100%' }" @change="onTabChange">
          <a-tab-pane
            v-for="(ele, i) in displayInfoOptions"
            :key="ele.key"
            :tab="projectInfo.includes(ele.key) ? ele.option1 : ele.value"
          >
            <!-- 圩区 -->
            <Polder v-if="ele.key === 'projectInfoPolder'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 水闸 -->
            <Sluice v-if="ele.key === 'projectInfoSluice'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 泵站 -->
            <Pump v-if="ele.key === 'projectInfoPump'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 闸站 -->
            <Gate v-if="ele.key === 'projectInfoGate'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 堤防 -->
            <Embankment
              v-if="ele.key === 'projectInfoHP017'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />
            <!-- 水电站 -->
            <Hyst v-if="ele.key === 'projectInfoHP003'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 水库大坝 -->
            <Dam v-if="ele.key === 'projectInfoHP002'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 水库 -->
            <Res v-if="ele.key === 'projectInfoHP001'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 灌区 -->
            <Irr v-if="ele.key === 'projectInfoHP004'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 渠(沟)道 -->
            <Chan v-if="ele.key === 'projectInfoHP005'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 取水井-->
            <Well v-if="ele.key === 'projectInfoHP006'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 农村供水-->
            <Cws v-if="ele.key === 'projectInfoHP013'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 塘坝-->
            <Pond v-if="ele.key === 'projectInfoHP015'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 蓄滞洪区-->
            <Fsda v-if="ele.key === 'projectInfoHP016'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 引调水-->
            <Wadl v-if="ele.key === 'projectInfoHP012'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 淤地坝-->
            <Sd v-if="ele.key === 'projectInfoHP020'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 渡槽-->
            <Flum v-if="ele.key === 'projectInfoHP008'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 倒虹吸-->
            <Insi v-if="ele.key === 'projectInfoHP009'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 涵洞-->
            <Culv v-if="ele.key === 'projectInfoHP011'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 治河工程-->
            <Grpj v-if="ele.key === 'projectInfoHP019'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 窖池-->
            <Pit v-if="ele.key === 'projectInfoHP014'" :displayCode="ele.key" :projectId="recordInfo.projectId" />
            <!-- 橡胶坝-->
            <Ruda v-if="ele.key === 'projectInfoHP021'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 骨干工程 闸站 -->
            <Cprj v-if="ele.key === 'projectInfoHP000'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 划界确权 -->
            <Demarcation
              ref="demarcationRef"
              v-if="ele.key === 'demarcation'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 工程文件预览 -->
            <FileViewList
              v-if="
                ele.key === 'appearance' ||
                ele.key === 'registration' ||
                ele.key === 'safetyAppraisal' ||
                ele.key === 'label'
              "
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 管理单位 -->
            <ManageUnit
              v-if="ele.key === 'unitManagement'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :projectName="modalTitle"
            />

            <!-- 应急管理 -->
            <EmergencyManage
              v-if="ele.key === 'emergencyResponse'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 安全管理 -->
            <SafetyManage v-if="ele.key === 'safetyManage'" :displayCode="ele.key" :projectId="recordInfo.projectId" />

            <!-- 工程检查 -->
            <EngineInspection
              v-if="ele.key === 'inspectionEngineering'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 控制运行 -->
            <Scheduling
              v-if="ele.key === 'controlOperation'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 维修养护 -->
            <MaintenanceUpkeep
              v-if="ele.key === 'maintenance'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
            />

            <!-- 安全检查 -->
            <!-- windowSize.height - 64 的原因同下 windowSize.height-->
            <SafetyInspection
              v-if="ele.key === 'safetyInspection'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              type="3"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 日常巡查 -->
            <DailyPatrol
              v-if="ele.key === 'dailyPatrol'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              type="2"
              :modalHeight="windowSize.height - 64"
            />
            <!-- 定期检查 -->
            <SafetyInspection
              v-if="ele.key === 'regularInspection'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              type="1"
              :modalHeight="windowSize.height - 64"
            />
            <!-- 专项检查 -->
            <SafetyInspection
              v-if="ele.key === 'specialInspection'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              type="2"
              :modalHeight="windowSize.height - 64"
            />
            <!-- 日常养护 -->
            <DailyMaintenance
              v-if="ele.key === 'dailyMaintenance'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />
            <!-- 专项维修 -->
            <SpecialMaintenance
              v-if="ele.key === 'specialMaintenance'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />
            <!-- 应急抢修 -->
            <EmergencyRepair
              v-if="ele.key === 'emergencyRepair'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 安全鉴定 -->
            <SafetyIdentification
              v-if="ele.key === 'safetyIdentification'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 安全工器具 -->
            <SafetyAppliances
              v-if="ele.key === 'safetyAppliances'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 安全监测 安全工器具/消防检查 -->
            <!-- SafetyTool -->

            <SafetyTool
              v-if="ele.key === 'safetyTool'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              type="1"
              :modalHeight="windowSize.height - 64"
            />
            <!--消防检查  -->
            <SafetyTool
              v-if="ele.key === 'fireCheck'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              type="2"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 高压预试 -->
            <PressurePretest
              v-if="ele.key === 'pressurePretest'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 沉降观测 -->
            <SettlementObservation
              v-if="ele.key === 'settlementObservation'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :type="1"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 位移观测 -->
            <SettlementObservation
              v-if="ele.key === 'displacementObservation'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :type="2"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 调度规则	 -->
            <SchedulingRule
              v-if="ele.key === 'schedulingRule'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 控运计划 -->
            <SchedulingPlan
              v-if="ele.key === 'schedulingPlan'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />

            <!-- 人员配备 -->
            <Staffing
              v-if="ele.key === 'staffing'"
              :displayCode="ele.key"
              :projectId="recordInfo.projectId"
              :isDetail="true"
              :modalHeight="windowSize.height - 64"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import Demarcation from './Demarcation.vue'
  import FileViewList from './FileViewList.vue'

  import Polder from './polder/index.vue' //圩垸
  import Sluice from './sluice/index.vue' //水闸
  import Pump from './pump/index.vue' //泵站
  import Gate from './gate/index.vue' //闸站
  import Embankment from './embankment/index.vue' // 堤防
  import Hyst from './hyst/index.vue' // 水电站
  import Dam from './dam/index.vue' // 水库大坝
  import Res from './res/index.vue' // 水库
  import Irr from './irr/index.vue' // 灌区
  import Chan from './chan/index.vue' // 渠(沟)道
  import Well from './well/index.vue' //取水井
  import Cws from './cws/index.vue' //农村供水
  import Pond from './pond/index.vue' //塘坝
  import Fsda from './fsda/index.vue' //蓄滞洪区
  import Wadl from './wadl/index.vue' //引调水

  import Sd from './sd/index.vue' // 淤地坝
  import Flum from './flum/index.vue' // 渡槽
  import Insi from './insi/index.vue' // 倒虹吸
  import Culv from './culv/index.vue' // 涵洞
  import Grpj from './grpj/index.vue' // 治河工程
  import Pit from './pit/index.vue' // 窖池
  import Ruda from './ruda/index.vue' //橡胶坝

  import Cprj from './cprj/index.vue' //骨干工程 闸站

  import ManageUnit from './manageUnit/index.vue'
  import EmergencyManage from './emergency-manage/index.vue'
  import SafetyManage from './safety-manage/index.vue'
  import SafetyTool from './safety-tool/index.vue' //安全监测 安全工器具
  import EngineInspection from './engine-inspection/index.vue'
  import Scheduling from './scheduling/index.vue'
  import MaintenanceUpkeep from './maintenance-upkeep/index.vue'

  import SafetyInspection from '@/views/standardized/engine-inspection/safety-inspection/index.vue'

  import DailyPatrol from '@/views/patrol-manage/dot-patrol/patrol-task/index.vue'
  import DailyMaintenance from '@/views/standardized/maintenance/daily-maintenance/index.vue'
  import SpecialMaintenance from '@/views/standardized/maintenance/special-maintenance/index.vue'
  import EmergencyRepair from '@/views/standardized/maintenance/emergency-repair/index.vue'

  import SafetyIdentification from '@/views/standardized/safety-manage/safety-identification/index.vue'
  import SafetyAppliances from '@/views/standardized/safety-manage/safety-moniter/appliances/index.vue'
  import PressurePretest from '@/views/standardized/safety-manage/safety-moniter/pressure-pretest/index.vue'
  import SettlementObservation from '@/views/standardized/engine-inspection/settlement-observation/index.vue'
  import SchedulingRule from '@/views/standardized/scheduling/scheduling-rule/index.vue'
  import SchedulingPlan from '@/views/standardized/scheduling/scheduling-plan/index.vue'
  import Staffing from '@/views/standardized/materialistic/staffing/index.vue'

  import { getDisplayCodes } from '../../services'
  import { getOptions } from '@/api/common'

  export default {
    name: 'CreateForm',
    props: {},
    components: {
      AntModal,
      Demarcation,
      FileViewList,

      Polder,
      Sluice,
      Pump,
      Gate,
      Embankment,
      Hyst,
      Dam,
      Res,
      Irr,
      Chan,
      Well,
      Cws,
      Pond,
      Fsda,
      Wadl,

      Sd,
      Flum,
      Insi,
      Culv,
      Grpj,
      Pit,
      Ruda,

      Cprj,

      ManageUnit,
      EmergencyManage,
      SafetyManage,
      EngineInspection,
      Scheduling,
      MaintenanceUpkeep,

      SafetyTool,
      SafetyInspection,
      DailyPatrol,
      DailyMaintenance,
      SpecialMaintenance,
      EmergencyRepair,
      SafetyIdentification,
      SafetyAppliances,
      PressurePretest,
      SettlementObservation,
      SchedulingRule,
      SchedulingPlan,
      Staffing,
    },
    data() {
      return {
        open: false,
        modalTitle: '',
        tabVal: '',
        windowSize: {},
        displayInfoOptions: [],
        recordInfo: {},
        projectInfo: [
          'projectInfoPolder',
          'projectInfoPump',
          'projectInfoSluice',
          'projectInfoGate',
          'projectInfoHP017',
          'projectInfoHP003',
          'projectInfoHP002',
          'projectInfoHP004',
          'projectInfoHP005',
          'projectInfoHP001',
          'projectInfoHP006',
          'projectInfoHP012',
          'projectInfoHP013',
          'projectInfoHP016',
          'projectInfoHP015',
          'projectInfoHP008',
          'projectInfoHP009',
          'projectInfoHP011',
          'projectInfoHP014',
          'projectInfoHP019',
          'projectInfoHP020',
          'projectInfoHP021',
          'projectInfoHP000', //骨干工程 闸站
          'unitManagement',
          'emergencyResponse',
          'safetyManage',
          'inspectionEngineering',
          'controlOperation',
          'maintenance',
          'demarcation',
          'safetyTool',
          'fireCheck',
        ], // 显示信息key集合
      }
    },
    created() {
      this.windowSize = {
        width: parseInt(window.innerWidth * 0.9),
        height: parseInt(window.innerHeight * 0.95),
      }

      // 64是弹窗footer的高度，如果弹窗footer设为null,则弹框高度会默认去掉footer的高度
      this.windowSize.height = this.windowSize.height + 64
    },
    computed: {},
    watch: {},
    methods: {
      onTabChange(val) {
        this.tabVal = val
      },

      /** 打开 */
      handleDetail(record) {
        this.modalTitle = record.projectName
        this.recordInfo = record

        getDisplayCodes({ projectId: record?.projectId }).then(resp => {
          if (!resp.data?.length) {
            this.$message.info('无展示信息')
            return
          }
          this.open = true
          getOptions('displayInfoTypes').then(res => {
            console.log(' displayInfoTypes', res)
            this.displayInfoOptions = [...this.displayInfoOptions, ...res.data.filter(el => resp.data.includes(el.key))]
            // this.displayInfoOptions = [this.displayInfoOptions[0]]

            this.tabVal = this.displayInfoOptions[0].key
          })
        })
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      onFullScreen() {
        this.$refs.demarcationRef?.[0].onFullScreen()
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    padding: 10px;
    background-color: #eef0f3 !important;
    border-radius: 0 0 4px 4px;
    .modal-content {
      width: 100%;
      height: 100%;
    }
  }

  .detail-content {
    width: 100%;
    height: 100%;
    position: relative;

    .detail-tab {
      height: 100%;

      ::v-deep .ant-tabs-content {
        height: 100%;
        padding-left: 0px;
        border-left: none;
        .ant-tabs-tabpane-active {
          height: 100%;
        }
      }
      ::v-deep .ant-tabs.ant-tabs-left {
        background-color: #fff;
        .ant-tabs-left-bar {
          border-right: 10px solid #eef0f3;
        }
        .ant-tabs-tab {
          margin-bottom: 6px;
          padding: 8px 24px;
        }
      }
    }
  }
</style>
