<template>
  <div
    style="
      margin-top: 12px;
      width: 850px;
      height: calc(100% - 12px);
      display: flex;
      background: #ffffff;
      position: relative;
    "
  >
    <div style="flex: 1; display: flex; flex-direction: column">
      <div style="flex: 1; position: relative; border-radius: 8px; overflow: hidden">
        <MapBox @onMapMounted="onMapMounted" />
        <QueryAndText
          v-if="projectList.length"
          :projectList="projectList"
          :searchType="searchType"
          @reset-map="onResetMap"
          @change-type="onChangeType"
        />
      </div>
      <TimePlaySlider v-if="times.length" :times="times" @onTimeChange="onTimeChange" />
    </div>
  </div>
</template>

<script lang="jsx">
  import { getValueByKey } from '@/api/common'
  import MapBox from '@/components/MapBox/index.vue'
  import QueryAndText from './QueryAndText.vue'
  import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
  import { getChSimRange, getWaterLevelMock } from '@/views/schedule/simulation-model/services'

  import axios from 'axios'
  import { LineLayer, Scene } from '@antv/l7'
  import { Mapbox as MapScene } from '@antv/l7-maps'
  import { MapboxOverlay } from '@deck.gl/mapbox'
  import addLine from './addLine.js'
  import useGeoJsonLabel from './useGeoJsonLabel.js'
  import pumpIcon from '@/assets/images/irrigation-open-pump.png'

  export default {
    name: 'GeneralizedDiagram',
    props: ['chSimId', 'fcstRange', 'mapData', 'dataSource', 'modelId'],
    components: { MapBox, QueryAndText, TimePlaySlider },
    data() {
      return {
        mapIns: null,
        deckMap: null,
        mapOverlayIns: null,
        times: [],
        geojson: null,
        curTime: '',
        sceneIns: null,
        searchType: 1,
        projectList: [],
        layerIndex: 11,
      }
    },
    computed: {},
    created() {
      this.times = [...new Set(this.mapData[0].resVOS.map(el => el.tm))]
    },

    methods: {
      onMapMounted(mapIns) {
        this.mapIns = mapIns
        addLine(this.mapIns)
        this.$nextTick(() => {
          this.mapIns.resize()
        })

        this.sceneIns = new Scene({
          id: 'mapBox',
          map: new MapScene({
            mapInstance: this.mapIns,
          }),
        })

        this.sceneIns.on('loaded', () => {
          this.curTime = this.times[0]

          getChSimRange({ type: 1, parentId: this.fcstRange }).then(resp => {
            axios.get(`${process.env.VUE_APP_MODEL_BASE}/${this.modelId}.json`).then(res => {
              getWaterLevelMock({
                endTime: this.times[this.times?.length - 1] + ':00',
                size: this.times?.length,
                startTime: this.times[0] + ':00',
              }).then(waterRes => {
                const jsonData = {}
                resp.data[0].projects.forEach(el => {
                  if (!!res.data[el.projectCode]) {
                    jsonData[el.projectCode] = { ...el, ...res.data[el.projectCode] }
                  }
                })
                const jsonResObj = jsonData
                if (!!jsonResObj) {
                  let lineLayerJson = {
                    type: 'FeatureCollection',
                    name: 'dl2',
                    crs: {
                      type: 'name',
                      properties: {
                        name: 'urn:ogc:def:crs:OGC:1.3:CRS84',
                      },
                    },
                    features: [],
                  }
                  Object.keys(jsonResObj).map(key => {
                    let arr = jsonResObj[key].coord
                    for (let i = 0; i < arr?.length * 2; i++) {
                      let waterLevel1 = waterRes?.data[this.curTime + ':00']?.wlevel[i]
                      let waterLevelVal = waterRes?.data[this.curTime + ':00']?.wlevel[i + 1]
                      if (arr[i] && arr[i + 1]) {
                        lineLayerJson.features.push({
                          type: 'Feature',
                          properties: {
                            highway: waterLevelVal > 5 ? 1 : waterLevelVal >= 5.4 ? 2 : 0,
                          },
                          geometry: {
                            type: 'MultiLineString',
                            coordinates: waterLevel1 > waterLevelVal ? [[arr[i], arr[i + 1]]] : [[arr[i + 1], arr[i]]],
                          },
                        })
                      }
                    }
                  })
                  this.setLineLayer(lineLayerJson)
                }
              })
            })
          })
        })
        this.mapOverlayIns = new MapboxOverlay({
          id: 'deck-geojson-layer-overlay',
          layers: [],
        })
        this.mapIns.addControl(this.mapOverlayIns)
        this.dealLayers()
      },
      onDeckLayer(map) {
        this.$nextTick(() => {
          map.resize()
        })
        this.mapOverlayIns = new MapboxOverlay({
          id: 'deck-geojson-layer-overlay',
          layers: [],
        })
        map.addControl(this.mapOverlayIns)
        this.dealLayers()
      },
      setLineLayer(lineJson) {
        this.sceneIns.addImage(
          'arrow',
          'https://gw.alipayobjects.com/zos/bmw-prod/ce83fc30-701f-415b-9750-4b146f4b3dd6.svg',
        )
        const layer = new LineLayer({})
          .source(lineJson)
          .size(3)
          .shape('line')
          .texture('arrow')
          .color('highway', v => {
            switch (v) {
              case 0:
                return 'rgb(22,119,255)'
              case 1:
                return 'rgb(255, 213, 47)'
              case 2:
                return 'rgb(246, 20, 65)'
            }
          })
          .animate({
            interval: 1, // 间隔
            duration: 1, // 持续时间，延时
            trailLength: 5, // 流线长度
          })
          .style({
            opacity: 0.6,
            lineTexture: true, // 开启线的贴图功能
            iconStep: 10, // 设置贴图纹理的间距
            borderWidth: 0.5, // 默认文 0，最大有效值为 0.5
            borderColor: '#fff', // 默认为 #ccc
          })
        this.sceneIns.addLayer(layer)
      },
      onTimeChange(time) {
        this.curTime = time
        this.layerIndex += 1
        let arr = []
        this.mapData?.forEach(el => {
          arr = arr.concat(el.resVOS.find(ele => ele.tm == time)?.records)
        })

        const factArr = arr.filter(el => !!+el?.longitude && !!+el?.latitude && el.type !== 3)
        this.projectList = factArr
        if (factArr.length === 0) return

        this.geojson = {
          type: 'FeatureCollection',
          features: factArr.map(el => {
            return {
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [+el.longitude, +el.latitude],
              },
              properties: {
                ...el,
                icon: pumpIcon,
              },
            }
          }),
        }

        this.dealLayers()
      },
      dealLayers() {
        if (!!this.mapIns && !!this.geojson) {
          useGeoJsonLabel(this.mapOverlayIns, this.geojson, this.searchType, this.layerIndex)
        }
      },
      onResetMap() {
        getValueByKey('patrolCentralPoint').then(res => {
          const center = res.data.split(',').map(el => +el)

          this.mapIns.setCenter([center])
          this.mapIns.setZoom(9.6)
        })
      },
      onChangeType(val) {
        this.searchType = val
        this.layerIndex += 1
        this.dealLayers()
      },
    },
  }
</script>

<style lang="less" scoped>
  #mapBox {
    background: linear-gradient(180deg, #36628d 0%, rgba(54, 107, 141, 0) 100%), rgba(20, 71, 105, 0.99);
  }
  #scene-box {
    width: 100%;
    height: 100%;
    :deep(.mapboxgl-canvas-container) {
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, #36628d 0%, rgba(54, 107, 141, 0) 100%), rgba(20, 71, 105, 0.99);
    }
    :deep(.mapboxgl-control-container) {
      display: none;
    }
  }
  :deep(.l7-control-container) {
    display: none;
  }
  :deep(.mapboxgl-ctrl-group button) {
    display: none;
  }
  :deep(.l7-scene) {
    top: 0;
  }
</style>
