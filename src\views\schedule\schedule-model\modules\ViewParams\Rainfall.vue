<template>
  <div style="height: 100%; display: flex; flex-direction: column">
    <a-radio-group v-model="sceneVal" button-style="solid" @change="onRadioChange">
      <a-radio-button :value="1">实测降雨</a-radio-button>
      <a-radio-button :value="2" v-if="scene === 2">未来预报</a-radio-button>
    </a-radio-group>

    <div style="flex: 1; margin-top: 10px">
      <VxeTable
        v-if="columns.length > 0"
        :key="tableKey"
        ref="vxeTableRef"
        :isShowTableHeader="false"
        :columns="columns"
        :tableData="list"
        :loading="loading"
        :isDrop="false"
        :tablePage="false"
        :showFooter="true"
        :footerData="footerData"
        :footer-row-style="{ background: '#F8F8F9', fontWeight: 'bold' }"
        :scrollY="{ enabled: true, gt: 0 }"
        :scrollX="{ enabled: true, gt: 0 }"
      ></VxeTable>
    </div>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'

  export default {
    name: 'Rainfall',
    components: { VxeTable },
    props: {
      rains: {
        type: Array,
        default: () => [],
      },
      futureRains: {
        type: Array,
        default: () => [],
      },
      scene: { type: Number },
    },
    data() {
      return {
        sceneVal: this.scene,
        list: [],
        list1: [],
        list2: [],
        loading: false,
        tableKey: 0,
        footerData: [],
        columns: [],
      }
    },
    computed: {},
    watch: {
      list: {
        handler(newVal) {
          if (this.sceneVal === 1) {
            let obj = {}
            Object.keys(newVal[0].sitesObj).forEach(el => {
              const total = newVal.reduce((pre, cur) => pre + cur.sitesObj[el].rain, 0)
              if (typeof total === 'string') {
                obj[el] = undefined
              } else {
                obj[el] = +total.toFixed(1)
              }
            })
            this.footerData = [obj]
          } else {
            let obj = {}
            Object.keys(newVal[0].sitesObj).forEach(el => {
              const total = newVal.reduce((pre, cur) => pre + cur.sitesObj[el].rain, 0)
              if (typeof total === 'string') {
                obj[el] = undefined
              } else {
                obj[el] = +total.toFixed(1)
              }
            })
            this.footerData = [obj]
          }
        },
        deep: true,
      },
    },
    created() {},
    mounted() {
      if (this.scene === 1) {
        this.list1 = this.rains.map(el => {
          const obj = {}
          el.sites.forEach(ele => (obj[ele.siteId] = { ...ele, rain: ele.rain }))
          return { tm: el.tm, sites: el.sites, sitesObj: obj }
        })
        this.list = JSON.parse(JSON.stringify(this.list1))

        this.dealColumns()
      }

      if (this.scene === 2) {
        this.list1 = this.futureRains.map(el => {
          const obj = {}
          el.sites.forEach(ele => (obj[ele.siteId] = { ...ele, rain: ele.rain }))
          return { tm: el.tm, sites: el.sites, sitesObj: obj }
        })

        this.list2 = this.rains.map(el => {
          const obj = {}
          el.sites.forEach(ele => (obj[ele.siteId] = { ...ele, rain: ele.rain }))
          return { tm: el.tm, sites: el.sites, sitesObj: obj }
        })

        this.list = JSON.parse(JSON.stringify(this.list2))
        this.dealColumns()
      }
    },
    methods: {
      onRadioChange(e) {
        this.sceneVal = e.target.value
        if (this.sceneVal === 1) {
          this.list = JSON.parse(JSON.stringify(this.list1))
        } else {
          this.list = JSON.parse(JSON.stringify(this.list2))
        }
        this.dealColumns()
      },
      dealColumns() {
        if (this.sceneVal === 1) {
          this.columns = [
            { title: '时间', field: 'tm', minWidth: 150, slots: { footer: ({ row }) => '总降雨量' } },
            ...this.list[0].sites.map(el => ({
              title: el.siteName + '(mm)',
              field: `sitesObj.${el.siteId}.rain`,
              minWidth: 130,
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='cell-box'>
                      <a-input-number
                        disabled
                        size='small'
                        step={0.1}
                        min={0}
                        v-model={this.list[rowIndex].sitesObj[el.siteId].rain}
                      />
                    </div>
                  )
                },
                footer: ({ row }) => {
                  return this.footerData.reduce((pre, cur) => pre + cur[`${el.siteId}`], 0)
                },
              },
            })),
          ]
        } else {
          this.columns = [
            { title: '时间', field: 'tm', minWidth: 150, slots: { footer: ({ row }) => '总降雨量' } },
            ...this.list[0].sites.map(el => ({
              title: '时段降雨量(mm)',
              minWidth: 150,
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='cell-box'>
                      <a-input-number
                        disabled
                        size='small'
                        step={0.1}
                        min={0}
                        v-model={this.list[rowIndex].sitesObj[el.siteId].rain}
                      />
                    </div>
                  )
                },
                footer: ({ row }) => {
                  return this.footerData.reduce((pre, cur) => pre + cur[`${el.siteId}`], 0)
                },
              },
            })),
          ]
        }

        this.tableKey += 1
      },
    },
  }
</script>

<style lang="less" scoped></style>
