<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="项目名称">
        <a-select allowClear v-model="queryParam.projectId" placeholder="请选择" :options="projectOptions"></a-select>
      </a-form-item>
      <a-form-item label="年份">
        <a-select v-model="queryParam.year" placeholder="请选择" :options="yearOptions"></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="false"
          :mergeCells="mergeCells"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getBudgetProject, getFinancialBudget } from './services'
  import { getManageYearList } from '../budget-project/services'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'CapitalBudget',
    components: {
      VxeTable,
      VxeTableForm,
    },
    data() {
      return {
        projectOptions: [],
        yearOptions: [],

        list: [],
        tableTitle: '资金预算',
        loading: false,
        queryParam: {
          projectId: undefined,
          year: moment().format('YYYY'),
        },
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 48,
            slots: {
              default: ({ row, rowIndex }) => {
                if (rowIndex == this.list.length - 1) return '合计'
                return rowIndex + 1
              },
            },
          },
          {
            title: '项目编码',
            field: 'projectCode',
          },
          {
            title: '项目名称',
            field: 'projectName',
          },
          {
            title: '当年预算金额(元)',
            field: 'yearBudgetAmount',
          },

          {
            title: '1月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[0]
              },
            },
          },
          {
            title: '2月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[1]
              },
            },
          },
          {
            title: '3月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[2]
              },
            },
          },
          {
            title: '4月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[3]
              },
            },
          },
          {
            title: '5月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[4]
              },
            },
          },
          {
            title: '6月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[5]
              },
            },
          },
          {
            title: '7月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[6]
              },
            },
          },
          {
            title: '8月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[7]
              },
            },
          },
          {
            title: '9月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[8]
              },
            },
          },
          {
            title: '10月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[9]
              },
            },
          },
          {
            title: '11月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[10]
              },
            },
          },
          {
            title: '12月(元)',
            slots: {
              default: ({ row }) => {
                return row.monthPayAmounts[11]
              },
            },
          },

          {
            title: '当年支付(元)',
            field: 'yearPayAmount',
          },
          {
            title: '当年剩余预算(元)',
            field: 'yearLeftBudgetAmount',
          },
          {
            title: '当年支付比例(%)',
            field: 'yearPayPercentage',
          },
          {
            title: '总支付比例(%)',
            field: 'payPercentage',
          },
        ],
        mergeCells: [],
      }
    },
    watch: {
      $route: {
        handler(newVal, oldVal) {
          if (newVal.name == 'capital-budget') {
            this.getProject()
            this.getList()
          }
        },
      },
    },
    created() {
      getManageYearList().then(res => {
        this.yearOptions = res?.data?.map(el => ({ label: el, value: el }))
      })
      this.getProject()
      this.getList()
    },
    mounted() {},
    methods: {
      getProject() {
        getBudgetProject().then(res => {
          this.projectOptions = res?.data?.map(el => ({ label: el.projectName, value: el.projectId }))
        })
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.loading = true
        getFinancialBudget(this.queryParam).then(response => {
          this.list = response?.data
          this.loading = false
          this.mergeCells = [{ row: this.list.length - 1, col: 0, rowspan: 1, colspan: 3 }]
        })
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          projectId: undefined,
          year: moment().format('YYYY'),
        }
        this.handleQuery()
      },
      // 导出
      handleExport() {},
    },
  }
</script>
<style lang="less" scoped>
  :deep(.vxe-table--body tbody tr:last-child) {
    position: sticky;
    bottom: 0;
    background-color: white;
  }
</style>
