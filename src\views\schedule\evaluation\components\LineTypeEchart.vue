<template>
  <base-echart id="line-echart" class="line-echart" :width="width" :height="height" :option="options" />
</template>

<script lang="jsx">
  // import BaseEchart from './BaseEchart.vue'
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import * as echarts from 'echarts/core'

  const colors = [
    '#58A9FB',
    '#B5E241',
    '#4363d8',
    '#911eb4',
    '#00A316',
    '#fabed4',
    '#469990',
    '#dcbeff',
    '#9A6324',
    '#aaffc3',
    '#808000',
    '#000075',
    '#a9a9a9',
  ]

  //hex -> rgba
  function hexToRgba(hex, opacity) {
    return (
      'rgba(' +
      parseInt('0x' + hex.slice(1, 3)) +
      ',' +
      parseInt('0x' + hex.slice(3, 5)) +
      ',' +
      parseInt('0x' + hex.slice(5, 7)) +
      ',' +
      opacity +
      ')'
    )
  }

  export default {
    components: {
      BaseEchart,
    },
    props: {
      dataSource: {
        require: true,
        default: () => [
          {
            name: '',
            data: [],
          },
        ],
      },
      custom: { default: () => {} },
      width: { default: '100%' },
      height: { default: '300px' },
    },
    data() {
      return {}
    },
    computed: {
      options() {
        return this.getOptions(this.dataSource, this.custom)
      },
    },
    mounted() {},
    methods: {
      getOptions(data, custom) {
        let {
          shortValue = true, // 缩写坐标值
          xLabel = '', // x轴名称
          yLabel = '', //y轴名称
          yUnit = '', //y轴单位
          legend = false, // 图例
          showAreaStyle = true, // 颜色区域
          rYUnit = '', // 右侧y轴单位
          rYLabel = '', // 右侧y轴名称
          dataZoom = true,
          color = null,
        } = custom
        let levels = ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
        const option = {
          color: color || colors,
          title: {
            top: 10,
            left: 10,
            // text: yLabel,
            textAlign: 'left',
            textStyle: {
              color: '#000',
              fontSize: 14,
              fontWeight: 400,
            },
          },
          grid: {
            left: '4%',
            right: xLabel ? '10%' : '4%',
            bottom: '16%',
            top: '15%',
            containLabel: true,
          },
          tooltip: {
            appendToBody: true,
            confine: true,
            position: (pos, params, dom, rect, size) => {
              let obj = { bottom: size.viewSize[1] - pos[1] + 10 }
              obj[['right', 'left'][+(pos[0] < size.viewSize[0] / 2)]] =
                pos[0] < size.viewSize[0] / 2 ? pos[0] + 10 : size.viewSize[0] - pos[0] + 10
              return obj
            },
            trigger: 'axis',
            // backgroundColor: 'rgba(0,0,0,0.6)', //通过设置rgba调节背景颜色与透明度
            borderWidth: 0,
            textStyle: {
              color: '#000',
            },
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
            formatter: function (params, ticket, callback) {
              var htmlStr = ''

              for (var i = 0; i < params.length; i++) {
                var param = params[i]

                htmlStr += '<div>'
                htmlStr += param.value[0] + '<br/>'
                htmlStr += param.value[1] ? levels[param.value[1]] : '-'
                htmlStr += '</div>'
              }
              return htmlStr
            },
          },
          xAxis: {
            data: data.length ? data[0].data.map(i => i[0]) : [],
            name: (data.length && xLabel) || '',
            nameTextStyle: {
              padding: [0, 0, 0, -5],
              color: '#000',
              fontSize: 12,
              fontWeight: 400,
            },
            axisLabel: {
              textStyle: {
                color: '#000',
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            z: 10,
          },
          yAxis: [
            {
              type: 'value',
              // 固定 y 轴的范围
              min: 0,
              max: 5,
              interval: 1,
              // 设置 y 轴的标签
              axisLabel: {
                formatter: function (value) {
                  return levels[value]
                },
              },
            },
            {},
          ],
          dataZoom: [
            {
              show: !!dataZoom,
              moveHandleSize: 12,
              height: 25,
              width: '80%',
              left: '10%',
              bottom: '6%',
            },
            {
              type: 'inside',
            },
          ],
          legend: {
            show: !!legend,
            icon: 'rect',
            itemWidth: 20,
            itemHeight: 3,
            top: 5,
            left: '50%',
            textStyle: {
              color: '#000',
            },
            ...legend,
          },
          series: data.map((item, i) => {
            return {
              type: 'line',
              showBackground: true,
              smooth: true,
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              name: item.name,
              color: item.color,
              stack: 'something',
              yAxisIndex: item?.yAxisIndex || 0,
              areaStyle: {
                opacity: showAreaStyle ? 0.4 : 0,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: hexToRgba(colors[i % colors.length], 0.3),
                  },
                  {
                    offset: 1,
                    color: '#fff',
                  },
                ]),
              },
              emphasis: {
                focus: 'series',
              },
              data: item.data,
            }
          }),
        }
        return option
      },
    },
  }
</script>

<style scoped></style>
