<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="年份">
        <a-select v-model="queryParam.year" placeholder="请选择" :options="yearOptions" showSearch></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="false"
          :mergeCells="mergeCells"
        ></VxeTable>
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getRealityList } from './services'
  import { getOptions } from '@/api/common'

  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import BigNumber from 'bignumber.js'

  export default {
    name: 'WaterAirport',
    components: {
      VxeTable,
      VxeTableForm,
    },
    data() {
      return {
        projectOptions: [],
        yearOptions: [],
        groupMap: {},
        list: [],
        tableTitle: '骨干工程实际用水量',
        loading: false,
        queryParam: {
          projectName: undefined,
          year: moment().format('YYYY'),
        },
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 48,
            fixed: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                if (rowIndex == this.list.length - 1) return '合计'
                return rowIndex + 1
              },
            },
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 180,
            fixed: 'left',
          },
          {
            title: '年份',
            field: 'year',
            width: 80,
            fixed: 'left',
          },
          {
            title: '1月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[0]
              },
            },
          },
          {
            title: '2月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[1]
              },
            },
          },
          {
            title: '3月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[2]
              },
            },
          },
          {
            title: '4月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[3]
              },
            },
          },
          {
            title: '5月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[4]
              },
            },
          },
          {
            title: '6月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[5]
              },
            },
          },
          {
            title: '7月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[6]
              },
            },
          },
          {
            title: '8月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[7]
              },
            },
          },
          {
            title: '9月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[8]
              },
            },
          },
          {
            title: '10月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[9]
              },
            },
          },
          {
            title: '11月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[10]
              },
            },
          },
          {
            title: '12月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[11]
              },
            },
          },
          {
            title: '累计取水量(万m³)',
            minWidth: 150,
            field: 'yearSum',
            align: 'center',
            fixed: 'right',
          },
          {
            title: '片区',
            minWidth: 100,
            field: 'group',
            align: 'center',
            fixed: 'right',
          },
          {
            title: '灌区取水量(万m³)',
            minWidth: 150,
            field: 'total',
            align: 'center',
            fixed: 'right',
          },
        ],
        mergeCells: [],
      }
    },
    created() {
      this.yearOptions = this.getYears().map(e => ({ label: e, value: e }))
      this.loadList()
    },
    mounted() {},
    methods: {
      loadList() {
        getOptions('backboneProjectCode').then(res => {
          res?.data.forEach(e => {
            this.groupMap[e.key] = e.option1
          })
          this.getList()
        })
      },
      getYears() {
        // 获取当前年份
        const currentYear = moment().year()
        // 从 2022 年到当前年份
        let i = 2022
        let years = []
        for (; i <= currentYear; i++) {
          years.push(i)
        }
        return years
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.loading = true
        getRealityList(this.queryParam).then(response => {
          this.list = response?.data
          this.list.forEach(e => (e.monthValues = e.monthWaterRates.map(a => a.waterRate)))
          let groupCountMap = new Map()
          let groupTotalMap = new Map()
          let totalRow = {
            projectName: '合计',
            monthValues: [],
            total: 0,
          }
          for (let i = 0; i < 12; i++) {
            totalRow.monthValues.push(0)
          }

          for (let i = 0; i < this.list.length; i++) {
            let e = this.list[i]
            e.group = this.groupMap[e.projectCode]
            let count = groupCountMap.get(e.group) || 0
            let groupTotal = groupTotalMap.get(e.group) || 0

            e.yearSum = e.monthValues.reduce((accumulator, currentValue) => {
              return new BigNumber(accumulator).plus(new BigNumber(currentValue))
            }, 0)

            for (let j = 0; j < e.monthValues.length; j++) {
              totalRow.monthValues[j] = new BigNumber(totalRow.monthValues[j]).plus(e.monthValues[j]).toString()
            }

            groupCountMap.set(e.group, count + 1)
            groupTotalMap.set(e.group, new BigNumber(groupTotal).plus(new BigNumber(e.yearSum)))
          }
          totalRow.total = totalRow.monthValues.reduce((accumulator, currentValue) => {
            return new BigNumber(accumulator).plus(new BigNumber(currentValue))
          }, 0)

          this.list.sort((a, b) => a.group.localeCompare(b.group))
          this.list.push(totalRow)

          let groupCountList = []
          for (let [key, value] of groupCountMap) {
            groupCountList.push({ group: key, count: value })
          }
          groupCountList.sort((a, b) => a.group.localeCompare(b.group))

          let rowIdx = 0
          for (let i = 0; i < groupCountList.length; i++) {
            let e = groupCountList[i]
            if (e.count < 2) {
              rowIdx++
              continue
            }

            this.list[rowIdx].total = groupTotalMap.get(e.group)

            this.mergeCells.push({ row: rowIdx, col: 16, rowspan: e.count, colspan: 1 })
            this.mergeCells.push({ row: rowIdx, col: 17, rowspan: e.count, colspan: 1 })
            rowIdx += e.count
          }

          this.mergeCells.push({ row: this.list.length - 1, col: 0, rowspan: 1, colspan: 3 })
          this.loading = false
        })
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          projectName: undefined,
          year: moment().format('YYYY'),
        }
        this.handleQuery()
      },
      // 导出
      handleExport() {},
    },
  }
</script>
<style lang="less" scoped>
  :deep(.vxe-table--body tbody tr:last-child) {
    position: sticky;
    bottom: 0;
    background-color: white;
  }
</style>
