<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">工程名称：</label>
            <span class="common-value-text">
              {{ this.projectOptions.find(el => el.projectId == repairDetails?.projectId)?.projectName }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">维修人员：</label>
            <span class="common-value-text">
              {{ this.systemUserOptions.find(el => el.userId == repairDetails?.repairUserId)?.name }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">维修结果：</label>
            <span class="common-value-text">
              {{ this.repairResultOptions?.find(el => el.key == repairDetails.repairResult)?.value }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">维修时间：</label>
            <span class="common-value-text">
              {{ repairDetails?.repairTime }}
            </span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">维修内容：</label>
            <span class="common-value-text">
              {{ repairDetails?.repairContent }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 20px">
            <div class="title">合同</div>

            <div
              class="file-item"
              v-for="(el, i) in repairDetails?.contractUrls"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 20px">
            <div class="title">其他附件</div>
            <div
              class="file-item"
              v-for="(el, i) in repairDetails?.otherUrls"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import Upload from '@/components/UploadFile'
  import moment from 'moment'
  import { getSysUserPage } from '@/api/common'
  import { getRepairById } from '../services'

  export default {
    name: 'RepairDetails',
    components: { AntModal, Upload },
    props: ['projectOptions', 'repairResultOptions'],
    data() {
      return {
        formTitle: '',
        modalLoading: false,
        open: false,
        systemUserOptions: [],
        repairDetails: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 按钮操作 */
      showDetails(row) {
        this.open = true
        if (row != undefined) {
          this.formTitle = '查看'
          getSysUserPage({
            pageNum: 1,
            pageSize: Number.MAX_SAFE_INTEGER,
            isDisabled: 0,
            deptId: null,
          }).then(res => {
            this.systemUserOptions = res?.data?.data
          })
          getRepairById({
            repairId: row.repairId,
          }).then(res => {
            if (res.code == 200) {
              this.repairDetails = res?.data
            }
            this.modalLoading = false
          })
        }
      },

      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
