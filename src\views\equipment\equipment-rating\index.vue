<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="工程名称">
        <!-- <a-select show-search placeholder="请输入" v-model="queryParam.curingType" option-filter-prop="children">
          <a-select-option v-for="item in curingTypeOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select> -->
        <a-tree-select
          v-model="queryParam.projectId"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
          @change="onChange"
        ></a-tree-select>
      </a-form-item>

      <a-form-item label="设备名称">
        <a-select
          show-search
          placeholder="请输入"
          :disabled="!queryParam.projectId"
          v-model="queryParam.deviceId"
          option-filter-prop="children"
        >
          <a-select-option v-for="(d, index) in deviceOptions" :key="index" :value="d.deviceId">
            {{ d.deviceName }}
          </a-select-option>
        </a-select>
        <!-- <a-input v-model="queryParam.deviceName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" /> -->
      </a-form-item>

      <a-form-item label="时间范围">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="planTimes"
          :disabled-date="disabledDate"
          @openChange="() => (disabledDealDate = null)"
          @change="onRangeChange"
          @calendarChange="calendarChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :projectOptions="projectOptions"
          :rateLevelOptions="rateLevelOptions"
          :userOptions="userOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <!-- <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :inspectionTypeOptions="inspectionTypeOptions"
          :projectOptions="projectOptions"
          :propertyList="propertyList"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        /> -->
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree, getSysUserPage } from '@/api/common'
  import { getDevicePage, deleteDevice, getDevicePageByProject } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'DailyMaintenance',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
    },
    data() {
      return {
        deviceOptions: [],
        userOptions: [],
        disabledDealDate: null,

        isChecked: false,
        checkOptions: [
          { key: 0, value: '否' },
          { key: 1, value: '是' },
        ],
        acceptOptions: [
          { key: 0, value: '未验收' },
          { key: 1, value: '已验收' },
        ],
        rateLevelOptions: [], //项目类型
        rateLeveles: [],

        planSourceOptions: [], //项目来源
        reportStatusOptions: [], //申报状态

        planSources: [],
        reportStatuses: [],

        propertyList: [],
        inspectionTypeOptions: [],
        inspectionTypes: [],
        inspectionStatuses: [],

        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        planTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '设备评级',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          deviceId: null,
          deviceName: '',
          endTime: '',
          pageNum: 1,
          pageSize: 10,
          projectId: null,
          projectName: '',
          sort: [],
          startTime: '',
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
          },
          {
            title: '工程名称',
            field: 'projectName',
          },
          {
            title: '设备名称',
            field: 'deviceName',
          },

          {
            title: '等级',
            field: 'rateLevel',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.rateLeveles[row.rateLevel]?.value
              },
            },
          },
          {
            title: '评级时间',
            field: 'rateDate',
          },

          {
            title: '评级人员',
            field: 'rateUserName',
          },

          {
            title: '设备维护情况',
            field: 'maintainContent',
          },
          {
            title: '创建人',
            field: 'createdUserName',
          },

          {
            title: '创建时间',
            field: 'createdTime',
          },

          {
            title: '操作',
            field: 'operate',
            width: 190,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
      getSysUserPage({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        isDisabled: 0,
        deptId: null,
      }).then(res => {
        this.userOptions = res.data.data
      })
      getOptions('rateLevel').then(res => {
        this.rateLevelOptions = res.data
        this.rateLeveles = getFlatTreeMap(this.rateLevelOptions, 'key')
        // console.log('*** 36 this.planTypes 1', this.curingTypeOptions, this.planTypes)
      })

      getOptions('planSource').then(res => {
        this.planSourceOptions = res.data
        this.planSources = getFlatTreeMap(this.planSourceOptions, 'key')
        // console.log('*** 36 this.planSources 2', this.planSourceOptions, this.planSources)
      })

      getOptions('reportStatus').then(res => {
        this.reportStatusOptions = res.data
        this.reportStatuses = getFlatTreeMap(this.reportStatusOptions, 'key')
      })

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })

      // this.inspectionStatuses = getFlatTreeMap(this.inspectionStatusOptions, 'key')
    },
    mounted() {},
    methods: {
      disabledDate(current) {
        // let start = moment(this.disabledDealDate).subtract(7, 'd')
        // let end = moment(this.disabledDealDate).add(7, 'd')
        // return current > end || current < start
      },
      calendarChange(dates) {
        if (dates?.length == 1) {
          this.disabledDealDate = dates[0]
        }
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      onChange(value) {
        console.log('**** 323 value', value)
        this.getDevicePageByProjectId(value)
      },
      getDevicePageByProjectId(value) {
        console.log('**** 308 value', value)
        let param = {
          treeNodeId: value,
          treeNodeType: 'data',
          districtCode: '',
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          sort: [],
          deviceCategoryId: '',
        }
        getDevicePageByProject(param).then(res => {
          console.log('**** 220 value res', res.data?.data)
          this.deviceOptions = res.data?.data
        })
      },
      onRangeChange(value, planTimes) {
        this.planTimes = value
        if (planTimes.length == 0) {
          return
        }
        this.queryParam.startTime = planTimes[0] ? moment(planTimes[0]).format('YYYY-MM-DD') : undefined
        this.queryParam.endTime = planTimes[1] ? moment(planTimes[1]).format('YYYY-MM-DD') : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getDevicePage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
        // this.names = valObj.records.map(item => item.planName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          deviceId: null,
          deviceName: '',
          startTime: '',
          endTime: '',
          pageNum: 1,
          // pageSize: 10,
          projectId: null,
          projectName: '',
          sort: [],
        }
        this.planTimes = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteDevice({ ids: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
