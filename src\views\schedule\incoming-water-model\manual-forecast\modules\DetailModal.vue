<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <div class="header" v-if="!!dataSource">
        <div class="item">
          <div class="label">方案名称:&nbsp;</div>
          <div class="value">
            <a-tooltip>
              <template slot="title">{{ dataSource.caseName }}</template>
              {{ dataSource.caseName }}
            </a-tooltip>
          </div>
        </div>
        <div class="item" style="width: 20%">
          <div class="label">预报方式:&nbsp;</div>
          <div class="value">{{ sourceTypeOptions.find(ele => ele.value == dataSource.sourceType)?.label }}</div>
        </div>
        <div class="item" style="width: 30%">
          <div class="label">预报范围:&nbsp;</div>
          <div class="value">{{ fcstRangeOptions.find(ele => ele.value == dataSource.fcstRange)?.label }}</div>
        </div>
        <div class="item">
          <div class="label">预报总来水量:&nbsp;</div>
          <div class="value">
            <span style="font-size: 20px; font-weight: 700">{{ dataSource.inWaterSum }}</span>
            万m³
          </div>
        </div>
        <div class="item">
          <div class="label">方案编号:&nbsp;</div>
          <div class="value">{{ dataSource.caseCode }}</div>
        </div>
        <div class="item" style="width: 20%">
          <div class="label">模拟应用场景:&nbsp;</div>
          <div class="value">{{ sceneOptions.find(ele => ele.value == dataSource.scene)?.label }}</div>
        </div>
        <div class="item" style="width: 30%">
          <div class="label">预报时段:&nbsp;</div>
          <div class="value">
            {{ moment(dataSource.startTime).format('YYYY-MM-DD HH') }}时 ~
            {{ moment(dataSource.endTime).format('YYYY-MM-DD HH') }}时
          </div>
        </div>
        <div class="item">
          <div class="label">方案生成时间:&nbsp;</div>
          <div class="value">{{ dataSource.saveTime }}</div>
        </div>
      </div>

      <div style="flex: 1">
        <ResultPanel v-if="!!dataSource" :resultData="dataSource" style="margin-top: 10px" />
      </div>
    </div>
    <template slot="footer">
      <!-- <a-button @click="onGenerate">生成水库调度</a-button> -->
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getInWater } from '../../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'
  import ResultPanel from '../../components/ResultPanel.vue'
  import { sourceTypeOptions, sceneOptions, modelStatusOptions } from '../config.js'

  export default {
    name: 'FormDrawer',
    components: { AntModal, ResultPanel },
    data() {
      return {
        moment,
        sourceTypeOptions,
        sceneOptions,
        modelStatusOptions,
        fcstRangeOptions: [],
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '详情',
        dataSource: null,
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleShow(row) {
        this.open = true
        this.modalLoading = true

        getOptions('fcstRange').then(res => {
          this.fcstRangeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        getInWater({ inWaterId: row.inWaterId }).then(res => {
          this.dataSource = res.data
          this.modalTitle = res.data.caseName + '详情'

          this.modalLoading = false
        })
      },
      onGenerate() {
        this.$emit('handleAddDispatchDetail', this.dataSource)

        this.$nextTick(() => {
          this.open = false
          this.$emit('close')
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
  .header {
    padding: 14px 16px;
    background: #e8f3ff;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    .item {
      display: flex;
      width: 25%;
      height: 30px;
      line-height: 30px;
      .label {
        color: #4e5969;
        white-space: nowrap;
      }
      .value {
        color: #1d2129;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
