<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="机埠名称">
        <a-input v-model="queryParam.projectName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="年份">
        <a-select v-model="queryParam.year" placeholder="请选择" :options="yearOptions" showSearch></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="false"
          :mergeCells="mergeCells"
        ></VxeTable>
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getAirportList } from './services'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import BigNumber from 'bignumber.js'

  export default {
    name: 'WaterAirport',
    components: {
      VxeTable,
      VxeTableForm,
    },
    data() {
      return {
        projectOptions: [],
        yearOptions: [],

        list: [],
        tableTitle: '机埠水电管理',
        loading: false,
        queryParam: {
          projectName: undefined,
          year: moment().format('YYYY'),
        },
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 48,
            fixed: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                if (rowIndex == this.list.length - 1) return '合计'
                return rowIndex + 1
              },
            },
          },
          {
            title: '机埠名称',
            field: 'projectName',
            minWidth: 180,
            fixed: 'left',
          },
          {
            title: '年份',
            field: 'year',
            width: 80,
            fixed: 'left',
          },

          {
            title: '1月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[0]
              },
            },
          },
          {
            title: '2月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[1]
              },
            },
          },
          {
            title: '3月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[2]
              },
            },
          },
          {
            title: '4月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[3]
              },
            },
          },
          {
            title: '5月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[4]
              },
            },
          },
          {
            title: '6月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[5]
              },
            },
          },
          {
            title: '7月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[6]
              },
            },
          },
          {
            title: '8月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[7]
              },
            },
          },
          {
            title: '9月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[8]
              },
            },
          },
          {
            title: '10月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[9]
              },
            },
          },
          {
            title: '11月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[10]
              },
            },
          },
          {
            title: '12月(万m³)',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return row.monthValues[11]
              },
            },
          },
          {
            title: '累计用水量(万m³)',
            minWidth: 150,
            field: 'total',
            fixed: 'right',
          },
        ],
        mergeCells: [],
      }
    },
    created() {
      this.yearOptions = this.getYears().map(e => ({ label: e, value: e }))
      this.getList()
    },
    mounted() {},
    methods: {
      getYears() {
        // 获取当前年份
        const currentYear = moment().year()
        // 从 2022 年到当前年份
        let i = 2022
        let years = []
        for (; i <= currentYear; i++) {
          years.push(i)
        }
        return years
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.loading = true
        getAirportList(this.queryParam).then(response => {
          this.list = response?.data
          this.list.forEach(e => (e.monthValues = e.monthWaterRates.map(a => a.waterRate)))

          let totalRow = {
            projectName: '合计',
            monthValues: [],
            total: 0,
          }
          for (let i = 0; i < 12; i++) {
            totalRow.monthValues.push(0)
          }

          for (let i = 0; i < this.list.length; i++) {
            let e = this.list[i]

            e.total = e.monthValues.reduce((accumulator, currentValue) => {
              return new BigNumber(accumulator).plus(new BigNumber(currentValue))
            }, 0)

            for (let j = 0; j < e.monthValues.length; j++) {
              totalRow.monthValues[j] = new BigNumber(totalRow.monthValues[j]).plus(e.monthValues[j]).toString()
            }
          }
          totalRow.total = totalRow.monthValues.reduce((accumulator, currentValue) => {
            return new BigNumber(accumulator).plus(new BigNumber(currentValue))
          }, 0)
          this.list.push(totalRow)

          //todo 要处理数据
          this.loading = false
          this.mergeCells = [{ row: this.list.length - 1, col: 0, rowspan: 1, colspan: 3 }]
        })
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          projectName: undefined,
          year: moment().format('YYYY'),
        }
        this.handleQuery()
      },
      // 导出
      handleExport() {},
    },
  }
</script>
<style lang="less" scoped>
  :deep(.vxe-table--body tbody tr:last-child) {
    position: sticky;
    bottom: 0;
    background-color: white;
  }
</style>
