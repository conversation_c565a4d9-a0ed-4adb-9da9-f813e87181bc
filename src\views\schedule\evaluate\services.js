import request from '@/utils/request'

//洪涝灾害评价-河道灾害评价
export function getRiverEvaluateData(params) {
    return request({
      url: '/model/scheduling/getChRes',
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      params,
    })
  }
//洪涝灾害评价-片区灾害评价
export function getAreaEvaluateData(params) {
  return request({
    url: '/model/scheduling/getEleRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 分页查询
export function getSchedulingPage(data) {
  return request({
    url: '/model/scheduling/page',
    method: 'post',
    data,
  })
}

//洪涝灾害统计
export function getEleVo(params) {
  return request({
    url: '/model/scheduling/getEleVo',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}