<template>
  <div style="flex: 1; padding-top: 20px; display: flex; flex-direction: column; align-items: center; overflow-y: auto">
    <div style="width: 100%; display: flex; flex-direction: column; align-items: center">
      <div style="margin: 20px 0 30px; font-size: 16px; font-weight: 700">{{ stepname }}</div>
      <div style="width: 70%">
        <a-progress :percent="process" :status="isover ? 'success' : 'active'" />
      </div>

      <div style="width: 100%; display: flex; flex-direction: column; align-items: center; margin-top: 20px">
        <div v-for="item in logsList" :key="item.id" style="width: 70%; display: flex; align-items: center">
          <div>{{ item.stepname }}，进度{{ item.process }}%</div>
        </div>
      </div>

      <div style="width: 60%; font-size: 16px; margin-top: 30px" v-if="isover">{{ msg }}</div>
    </div>
  </div>
</template>

<script>
  import { getModelRunProcess } from '../../services'

  export default {
    name: 'Calculation',
    components: {},
    props: ['schedulingId'],
    data() {
      return {
        timer: null,
        process: 0,
        stepname: '　',
        msg: '',
        isover: false,

        logsList: [],
      }
    },
    computed: {},
    activated() {
      this.logsList = []

      this.timer = setInterval(() => {
        getModelRunProcess({
          schedulingId: this.schedulingId,
        }).then(res => {
          this.process = res.data.process
          this.stepname = res.data.stepname
          this.msg = res.data.msg
          this.isover = !!res.data.isover

          this.logsList.push(res.data)
          if (!!res.data.isover) {
            clearInterval(this.timer)
          }
        })
      }, 1000)
    },
    deactivated() {
      this.timer && clearInterval(this.timer)
    },

    methods: {
      save() {
        this.$emit('saveData', {})
      },
    },
  }
</script>

<style lang="less" scoped></style>
