import request from '@/utils/request'

// 列表分页查询
export function getNewsPage(data) {
  return request({
    url: '/custom/news/page',
    method: 'post',
    data,
  })
}

// 新增
export function addNews(data) {
  return request({
    url: '/custom/news/add',
    method: 'post',
    data,
  })
}

// 删除
export function deleteNews(params) {
  return request({
    url: '/custom/news/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 修改
export function updateNews(data) {
  return request({
    url: '/custom/news/update',
    method: 'post',
    data,
  })
}

// 详情
export function getNews(params) {
  return request({
    url: '/custom/news/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 新闻-置顶
export function setTop(params) {
  return request({
    url: '/custom/news/setTop',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 新闻-撤回 
export function recall(params) {
  return request({
    url: '/custom/news/recall',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//发布与撤回 
export function addIssue(data) {
  return request({
    url: '/custom/news/addIssue',
    method: 'post',
    data,
  })
}
export function issue(params) {
  return request({
    url: '/custom/news/issue',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
