<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="试验名称" prop="highName">
              <a-input v-model="form.highName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="预试日期" prop="highDate">
              <a-date-picker
                v-model="form.highDate"
                format="YYYY-MM-DD"
                placeholder="请选择"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="试验内容" prop="highContent">
              <a-textarea
                v-model="form.highContent"
                placeholder="请输入"
                allow-clear
                :auto-size="{ minRows: 3, maxRows: 3 }"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="试验报告" prop="positionAttaches">
              <UploadFile
                :fileUrl.sync="form.positionAttaches"
                :multiple="true"
                listType="text"
                folderName="check-report"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addHigh, updateHigh, getHigh } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          id: undefined,
          highName: undefined,
          highDate: undefined,
          projectId: undefined,
          highContent: '',
          positionAttaches: [],
        },
        open: false,
        rules: {
          highName: [{ required: true, message: '工具器名称不能为空', trigger: 'blur' }],
          highDate: [
            {
              required: true,
              message: '预试日期不能为空',
              trigger: 'change',
            },
          ],
          highContent: [{ required: true, message: '试验内容不能为空', trigger: 'blur' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
          positionAttaches: [{ required: true, message: '附件不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        this.form.id = row?.id
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getHigh({ id: row.id }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                positionAttaches: res.data.positionAttaches?.map(el => el.attachUrl),
              }

              this.modalLoading = false
            }
          })
        }
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            const params = {
              ...this.form,
              highDate: moment(this.form.highDate).format('YYYY-MM-DD'),
            }
            if (!!this.form.id) {
              updateHigh(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('close')
                    this.$emit('ok')
                  }
                })
                .finally(() => (this.loading = false))
            } else {
              addHigh(params)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('close')
                    this.$emit('ok')
                  }
                })
                .finally(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
