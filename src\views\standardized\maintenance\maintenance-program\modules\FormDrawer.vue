<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <!-- <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col> -->

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="项目名称" prop="planName">
              <a-input v-model="form.planName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="项目类型" prop="planType">
              <a-select show-search placeholder="请输入" v-model="form.planType" option-filter-prop="children">
                <a-select-option v-for="item in planTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <!-- <a-input v-model="form.propertyId" placeholder="请输入" allow-clear /> ,'propertyList' -->
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="项目来源" prop="planSource">
              <a-select show-search placeholder="请输入" v-model="form.planSource" option-filter-prop="children">
                <a-select-option v-for="item in planSourceOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="年份" prop="year">
              <a-date-picker
                mode="year"
                format="YYYY"
                v-model="form.year"
                placeholder="请选择"
                allow-clear
                :open="yearShowOne"
                style="width: 100%"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
              ></a-date-picker>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="计划开始时间" prop="startTime">
              <a-date-picker v-model="form.startTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="计划结束时间" prop="endTime">
              <a-date-picker v-model="form.endTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="资金(元)" prop="capital">
              <a-input-number
                :precision="2"
                :min="0"
                style="width: 100%"
                v-model="form.capital"
                placeholder="请输入"
                allow-clear
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工程名称" prop="projectId">
              <!-- <a-select
                mode="multiple"
                :default-value="[]"
                style="width: 100%"
                placeholder="请选择工程名称"
                v-model="form.projectId"
              >
                <a-select-option v-for="item in propertyInProject" :key="item.projectId">
                  {{ item.projectName }}
                </a-select-option>
              </a-select> -->
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="申报状态" prop="reportStatus">
              <a-select show-search placeholder="请输入" v-model="form.reportStatus" option-filter-prop="children">
                <a-select-option v-for="item in reportStatusOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="主要内容" prop="content">
              <a-textarea style="width: 100%" v-model="form.content" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <!-- <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">合同文件附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.contractAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">阶段计划附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.stageAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">月度考核报告附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.monthlyAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">季度考核报告附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.quarterlyAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">验收材料附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.acceptanceAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col> -->
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addPlans, editPlans, getPlansById, getProjectListByPropertyId } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'
  import { validEmail } from '@/utils/validate'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'planTypeOptions', 'planSourceOptions', 'reportStatusOptions'],
    data() {
      return {
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭

        formTitle: '',
        loading: false,
        modalLoading: false,
        form: {
          capital: null,
          content: '',
          endTime: '',
          planName: '',
          planSource: undefined,
          planType: undefined,
          projectId: null,
          reportStatus: undefined,
          startTime: '',
          year: null,
        },
        open: false,
        propertyInProject: [],
        rules: {
          planName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
          planType: [{ required: true, message: '项目类型不能为空', trigger: 'change' }],
          planSource: [{ required: true, message: '项目来源不能为空', trigger: 'change' }],
          year: [{ required: true, message: '计划年度不能为空', trigger: 'change' }],
          projectId: [{ required: true, message: '工程名称不能为空', trigger: 'change' }],

          capital: [{ required: true, message: '资金(万元)不能为空', trigger: 'blur' }],

          startTime: [{ required: true, message: '计划开始时间不能为空', trigger: 'change' }],
          endTime: [{ required: true, message: '计划结束时间不能为空', trigger: 'change' }],
          reportStatus: [{ required: true, message: '申报状态不能为空', trigger: 'change' }],
          content: [{ required: true, message: '主要内容不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.form.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getPlansById({
            planId: row.planId,
          }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                // contractAttaches: res.data.contractAttaches?.map(el => el.attachUrl),
                // acceptanceAttaches: res.data.acceptanceAttaches?.map(el => el.attachUrl),
                // monthlyAttaches: res.data.monthlyAttaches?.map(el => el.attachUrl),
                // quarterlyAttaches: res.data.quarterlyAttaches?.map(el => el.attachUrl),
                // stageAttaches: res.data.stageAttaches?.map(el => el.attachUrl),
              }
              // this.form.propertyId = this.propertyList.find(el => el.propertyId == this.form.propertyId)?.propertyId
              // propertyId: this.form.propertyId
              this.form.year = moment(`${res.data.year}-01-01`)
              getProjectListByPropertyId({ propertyId: 1 }).then(res => {
                this.propertyInProject = res?.data
              })
            }
            this.modalLoading = false
          })
        }
      },
      //

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.startTime = moment(this.form.startTime).format('YYYY-MM-DD')
            this.form.endTime = moment(this.form.endTime).format('YYYY-MM-DD')
            this.form.year = moment(this.form.year).format('YYYY')
            if (this.form.planId == null) {
              addPlans(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editPlans(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
