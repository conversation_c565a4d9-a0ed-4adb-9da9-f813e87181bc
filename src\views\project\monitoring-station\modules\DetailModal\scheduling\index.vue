<template>
  <div class="manage-unit">
    <a-tabs v-model="tabKey" size="small" @change="onTabChange">
      <a-tab-pane key="1" tab="控制计划">
        <AttachItem :dataSource="currentData" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="调度规则">
        <AttachItem :dataSource="currentData" />
      </a-tab-pane>
      <a-tab-pane key="3" tab="防洪调度">
        <AttachItem :dataSource="currentData" />
      </a-tab-pane>
      <a-tab-pane key="4" tab="调度操作">
        <AttachItem :dataSource="currentData" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="jsx">
  import { getAttaches } from './services'
  import AttachItem from './modules/attach-item.vue'

  export default {
    name: 'Scheduling',
    components: {
      AttachItem,
    },
    props: {
      projectId: {},
    },
    data() {
      return {
        tabKey: '1',
        currentData: [],
      }
    },
    computed: {},
    watch: {},
    created() {
      this.getList(this.tabKey)
    },
    methods: {
      onTabChange(val) {
        this.getList(val)
      },
      getList(type) {
        getAttaches({ type, projectId: this.projectId }).then(res => {
          this.currentData = res.data || []
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .manage-unit {
    margin: 10px 0;
    width: 100%;
    height: calc(100% - 20px);
    overflow: hidden;
    overflow: auto;
  }

  ::v-deep .ant-tabs-top {
    height: 100%;
  }
  ::v-deep .ant-tabs-content {
    height: calc(100% - 50px) !important;
  }
</style>
