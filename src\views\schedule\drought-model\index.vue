<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="预警编码">
        <a-input allowClear v-model="queryParam.warnCode" placeholder="请选择"></a-input>
      </a-form-item>

      <a-form-item label="预警时间">
        <a-range-picker v-model="queryParam.range" />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="旱情预警识别模型"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="ids.length != 0" @click="handleDelete('all')">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>

        <FormModal
          v-if="showFormModal"
          ref="formModalRef"
          :geoServerList="geoServerList"
          @ok="onOperationComplete"
          @close="showFormModal = false"
        />

        <DetailModal
          v-if="showDetailModal"
          ref="detailModalRef"
          @ok="onOperationComplete"
          @close="showDetailModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getDamageWarnPage, getGeoServer, deleteDamageWarn } from './services.js'

  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'
  import FormModal from './modules/FormModal.vue'
  import DetailModal from './modules/DetailModal.vue'

  export default {
    name: 'DroughtModel',
    components: {
      VxeTable,
      VxeTableForm,
      FormModal,
      DetailModal,
    },
    data() {
      return {
        exportLoading: false,
        showFormModal: false,
        showDetailModal: false,

        geoServerList: [],

        list: [],
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          warnCode: undefined,
          range: [],
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '预警编码',
            field: 'warnCode',
            minWidth: 220,
            showOverflow: 'tooltip',
          },
          {
            title: '土壤湿度矢量图层',
            field: 'humidityUrl',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
          {
            title: '含水率矢量图层',
            field: 'moistureUrl',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
          {
            title: '预警时间',
            field: 'warnDate',
            minWidth: 170,
          },
          {
            title: '操作',
            field: 'operate',
            width: 150,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getGeoServer().then(res => {
        this.geoServerList = (res.data || []).map(item => ({
          label: item.name,
          value: item.name,
        }))
      })
    },

    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.loading = true
        this.selectChange({ records: [] })
        getDamageWarnPage({
          ...this.queryParam,
          startTime: !this.queryParam.range?.[0] ? undefined : moment(this.queryParam.range[0]).format('YYYY-MM-DD'),
          endTime: !this.queryParam.range?.[1] ? undefined : moment(this.queryParam.range[1]).format('YYYY-MM-DD'),
        }).then(response => {
          this.list = response.data?.data || []
          this.total = response.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          tp: undefined,
          range: [],
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.id)
        this.names = valObj.records.map(item => item.warnCode)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      /* 新增 */
      handleAdd() {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleShow())
      },
      handleEdit(record) {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleShow(record))
      },
      /* 详情 */
      handleDetail(record) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleShow(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.ids
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteDamageWarn({ ids: ids.join(',') }).then(res => {
              that.$message.success(`删除成功`, 3)
              that.ids = []
              that.onOperationComplete()
            })
          },
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
