<template>
  <div style="height: 100%; padding-bottom: 24px">
    <Type1 v-if="detail.dispathType === 1" ref="type1Ref" :detail="detail" />
    <Type2 v-if="detail.dispathType === 2" ref="type2Ref" :detail="detail" />
    <Type3 v-if="detail.dispathType === 3" ref="type3Ref" :detail="detail" />
  </div>
</template>

<script>
  import Type1 from './type1.vue'
  import Type2 from './type2.vue'
  import Type3 from './type3.vue'

  export default {
    name: 'DispatchProcess',
    components: {
      Type1,
      Type2,
      Type3,
    },
    props: ['detail'],
    data() {
      return {}
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
      save() {
        if (this.detail.dispathType === 1) {
          this.$emit('saveData', this.$refs.type1Ref.save())
        } else if (this.detail.dispathType === 2) {
          this.$emit('saveData', this.$refs.type2Ref.save())
        } else if (this.detail.dispathType === 3) {
          this.$emit('saveData', this.$refs.type3Ref.save())
        }
      },
    },
  }
</script>

<style lang="less" scoped></style>
