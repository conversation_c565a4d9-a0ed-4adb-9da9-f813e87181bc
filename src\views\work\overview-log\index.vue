<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="日志类型">
        <a-select v-model="queryParam.logType" allowClear :options="logTypeOptions" placeholder="请选择"></a-select>
      </a-form-item>

      <a-form-item label="日志日期">
        <a-range-picker format="YYYY-MM-DD" v-model:value="logDate" valueFormat="YYYY-MM-DD" @change="changeLogDate" />
      </a-form-item>

      <a-form-item label="所在班组">
        <a-select
          v-model="queryParam.groupId"
          allowClear
          :options="groupOptions"
          placeholder="请选择"
          @change="changeQueryGroup"
        ></a-select>
      </a-form-item>
      <a-form-item label="填写人">
        <a-select
          v-model="queryParam.userId"
          :filter-option="false"
          :showSearch="true"
          :disabled="queryParam.groupId == null"
          :not-found-content="fetching ? undefined : null"
          @search="fetchUserSearch"
          allowClear
          :options="groupUserOptions"
          placeholder="请选择"
        ></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="日志总览"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <!-- <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button> -->
            <!-- <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button> -->
          </div>
        </VxeTable>

        <DetailsModal v-if="showDetailsModal" ref="detailsModalRef" @ok="getList" @close="showDetailsModal = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getWorkLogPageAdmin, getGroupUserList, workLogDelete } from './services'
  import { getWorkGroupPage } from '@/views/work/work-group/services'
  import DetailsModal from './modules/DetailsModal'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'ScheduleLog',
    components: {
      VxeTableForm,
      VxeTable,
      DetailsModal
    },
    data() {
      return {
        showDetailsModal: false,
        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,
        fetching: false,

        logDate: [],

        queryParam: {
          logType: null,
          groupId: null,
          userId: null,
          pageNum: 1,
          pageSize: 10,
          startTime: '',
          endTime: ''
        },
        logTypeOptions: [
          { label: '值班日志', value: 1 },
          { label: '调度日志', value: 2 }
        ],
        groupOptions: [],
        groupUserOptions: [],
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '日志日期',
            field: 'logDate',
            minWidth: 96
          },
          {
            title: '日志内容',
            field: 'logContent',
            showOverflow: 'tooltip',
            minWidth: 80
          },
          {
            title: '填写人',
            field: 'name',
            width: 76,
            showOverflow: 'tooltip'
          },
          {
            title: '日志类型',
            field: 'logType',
            width: 76,
            slots: {
              default: ({ row }) => {
                if (row.logType === 1) {
                  return <span>值班日志</span>
                } else if (row.logType === 2) {
                  return <span>调度日志</span>
                }
              }
            }
          },
          {
            title: '所在班组',
            field: 'groupName',
            showOverflow: 'tooltip',
            minWidth: 80
          },
          {
            title: '所在班次',
            field: 'shiftName',
            showOverflow: 'tooltip',
            minWidth: 80
          },
          {
            title: '日志更新时间',
            field: 'updatedTime',
            minWidth: 153
          },
          {
            title: '操作',
            field: 'operate',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleLogDetails(row)}>详情</a>
                  </span>
                )
              }
            }
          }
        ]
      }
    },
    computed: {},
    watch: {},
    beforeDestroy() {},
    created() {
      getWorkGroupPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.groupOptions = (res.data?.data || []).map(el => ({ ...el, label: el.groupName, value: el.groupId }))

        this.getList()
      })
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.loading = true
        this.selectChange({ records: [] })
        getWorkLogPageAdmin(this.queryParam).then(response => {
          this.list = response.data?.data || []

          this.total = response.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          logType: null,
          groupId: null,
          userId: null,
          pageNum: 1,
          startTime: '',
          endTime: ''
        }
        this.logDate = []
        this.handleQuery()
      },
      fetchUserSearch(val) {
        this.fetching = true
        getGroupUserList({ groupId: this.queryParam.groupId, name: val }).then(res => {
          this.groupUserOptions = (res.data || []).map(el => ({ ...el, label: el.name, value: el.userId }))
          this.fetching = false
        })
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.logId)
        this.names = valObj.records.map(item => item.logTitle)
        this.isChecked = !!valObj.records.length
      },
      changeLogDate(val) {
        this.queryParam.startTime = val[0]
        this.queryParam.endTime = val[1]
      },
      changeQueryGroup() {
        this.queryParam.userId = null
        if (this.queryParam.groupId) {
          getGroupUserList({ groupId: this.queryParam.groupId }).then(res => {
            this.groupUserOptions = (res.data || []).map(el => ({ ...el, label: el.name, value: el.userId }))
          })
        }
      },
      /* 详情 */
      handleLogDetails(record) {
        this.showDetailsModal = true
        this.$nextTick(() => this.$refs.detailsModalRef.handleLogDetails(record))
      }
    }
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
</style>
