<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">名称：</label>
            <span class="common-value-text">
              {{ form.inspectionName }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">计划类型：</label>
            <span class="common-value-text">
              {{ planTypeFormat(form.planType) }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">检查类型：</label>
            <span class="common-value-text">
              {{ inspectionTypeFormat(form.inspectionType) }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">检查时间：</label>
            <span class="common-value-text">
              {{ form.planCompleteTime }}
            </span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">
              {{ form.projectName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 20px">
            <div class="title">申请文件附件</div>
            <div
              class="file-item"
              v-for="(el, i) in form.applyForAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 20px">
            <div class="title">检查记录单附件</div>
            <div class="file-item" v-for="(el, i) in form.checkAttaches" :key="i" @click="() => downLoad(el.attachUrl)">
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 20px">
            <div class="title">整改结果附件</div>

            <div
              class="file-item"
              v-for="(el, i) in form.summaryAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">结果</div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">检查结果：</label>
            <span class="common-value-text">
              {{ inspectionStatusFormat(form.inspectionStatus) }}
            </span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :span="24" v-if="observationType == 1">
          <div class="title">检查项</div>
          <div class="item-group" v-if="form.items?.length > 0">
            <div class="group-item" v-for="(item, index) in form.items" :key="index">
              <div class="group-item-title">
                {{ '检查项' + (index + 1) }}
                <!-- <span style="float: right; cursor: pointer" @click="delItem(item, index)">
                  <a-icon type="delete" theme="twoTone" two-tone-color="#EB3435" />
                </span> -->
              </div>
              <!-- <a-form-model-item label="检查内容">
                <a-input v-model="item.checkContent"  placeholder="请输入" allow-clear />
              </a-form-model-item> -->
              <div class="item">
                <label class="common-label-text">检查内容：</label>
                <span class="common-value-text">
                  {{ item.checkContent }}
                </span>
              </div>

              <!-- <a-form-model-item label="照片">
                <UploadFile
                  :fileUrl.sync="item.attachUrl"
                  :key="index"
                  :multiple="true"
                  listType="picture-card"
                  folderName="safety-manage"
                />
              </a-form-model-item> -->
              <div class="item">
                <label class="common-label-text">照片：</label>
                <span class="common-value-text" style="display: flex; flex-wrap: wrap; gap: 10px">
                  <!-- class="file-item" -->
                  <div v-for="(el, i) in item.attachUrl" :key="i" @click="() => downLoad(el)">
                    <!-- <img src="{ el }" v-for="(el, i) in item.attachUrl" :key="i" @click="() => downLoad(el)" /> -->
                    <img :src="el" :alt="i" style="width: 60px; height: 60px; margin-bottom: 10px" />
                    <!-- <div class="file-name" style="margin-left: 5px">
                    {{ el }}
                  </div> -->
                  </div>
                </span>
                <!-- <UploadFile
                  :fileUrl.sync="item.attachUrl"
                  :key="index"
                  disabled
                  :multiple="true"
                  listType="picture-card"
                  folderName="safety-manage"
                /> -->
                <!-- <a-icon type="paper-clip" /> -->
              </div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- <div class="form-item-title">配置模板内容</div> -->
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getToolById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'toolTypeOptions', 'toolStatusOptions', 'toolType'],
    data() {
      return {
        modalLoading: false,
        tabKey: '1',
        tabPosition: 'left',
        tabAttachKey: '1',
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        form: {
          applyForAttaches: [],
          checkAttaches: [],
          inspectionId: null,
          inspectionName: '',
          inspectionStatus: null,
          inspectionType: null,
          planCompleteTime: '',
          planType: null,
          projectId: null,
          summaryAttaches: [],
        },
        open: false,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 检查结果格式化
      inspectionStatusFormat(value) {
        if (value) {
          return this.inspectionStatusOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 计划类型格式化
      planTypeFormat(value) {
        if (value) {
          return this.planTypeOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 检查类型格式化
      inspectionTypeFormat(value) {
        if (value) {
          return this.inspectionTypeOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },
      downLoad(url) {
        window.open(url)
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '查看'
          getToolById({ inspectionId: row.inspectionId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
              this.form.inspectionType = String(this.form.inspectionType)

              this.modalLoading = false
            }
          })
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .item-group {
    margin-top: 10px;

    .group-item {
      margin-top: 10px;
      padding: 10px;
      background: #f2f2f2;
      .item-title {
        font-size: 16px;
        font-weight: 700;
      }
    }
  }
</style>
