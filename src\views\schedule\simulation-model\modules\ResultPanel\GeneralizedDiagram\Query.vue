<template>
  <div class="icon-box">
    <i class="icon-item mr-0.06rem query-bg" @click="changeRest">
      <a-icon class="reload-icon" type="reload" />
    </i>

    <i class="icon-item" :class="[searchType == 1 ? 'query-active-bg' : 'query-bg']" @click="changeSearch(1)">
      <img v-if="searchType == 1" class="icon-img" src="@/assets/images/irrigation-water-level-active.png" />
      <img v-else class="icon-img" src="@/assets/images/irrigation-water-level.png" />
    </i>
    <i
      class="icon-item mx-0.06rem"
      :class="[searchType == 2 ? 'query-active-bg' : 'query-bg']"
      @click="changeSearch(2)"
    >
      <img v-if="searchType == 2" class="icon-img" src="@/assets/images/irrigation-flow-active.png" />
      <img v-else class="icon-img" src="@/assets/images/irrigation-flow.png" />
    </i>
    <i class="icon-item" :class="[showDescribe ? 'query-active-bg' : 'query-bg']" @click="changeDescribe">
      <img v-if="showDescribe" class="icon-img more" src="@/assets/images/irrigation-describe-active.png" />
      <img v-else class="icon-img more" src="@/assets/images/irrigation-describe.png" />
    </i>
  </div>
</template>
<script lang="jsx">
  export default {
    name: 'Query',
    props: ['searchType', 'showDescribe'],

    methods: {
      changeSearch(val) {
        this.$emit('change-Type', val)
      },

      changeDescribe() {
        this.$emit('change-describe')
      },
      changeRest() {
        this.$emit('change-reset')
      },
    },
  }
</script>

<style lang="less" scoped>
  .icon-box {
    display: flex;
    padding-top: 5px;
    position: absolute;
    right: 0;
    top: 0;
    .icon-item {
      width: 26px;
      height: 26px;
      display: block;
      cursor: pointer;
      margin-right: 3px;
      .icon-img {
        width: 16px;
        height: 17px;
        margin-top: 2px;
        margin-left: 4px;
      }
      .more {
        margin-left: 6px;
      }
      .reload-icon {
        color: #fff;
        font-size: 14px;
        margin-left: 5px;
        margin-top: 5px;
      }
    }
    .query-bg {
      background: url('@/assets/images/irrigation-search-bg.png') no-repeat center / 100% 100%;
    }
    .query-active-bg {
      background: url('@/assets/images/irrigation-search-bg-active.png') no-repeat center / 100% 100%;
    }
  }
</style>
