<template>
  <div class="map-model">
    <MapBox @onMapMounted="onMapMounted" @onMapStyleLoad="onMapStyleLoad" />

    <div style="position: absolute; right: 16px; bottom: 16px">
      <MapStyle v-if="!!mapIns" :mapIns="mapIns" ref="mapStyleRef" />
    </div>
  </div>
</template>

<script lang="jsx">
  import mapboxgl from 'mapbox-gl'
  import { boundFlatLngLats } from '@/utils/mapBounds'
  import MapBox from '@/components/MapBox'
  import poiImg from '@/assets/images/poi-marker-default.png'
  import defaultImg from '../default-project.jpg'
  import { clearSourceAndLayer } from '@/utils/mapUtils'
  import MapStyle from '@/components/MapBox/MapStyle.vue'

  export default {
    name: 'MapModel',
    props: ['list', 'total', 'pageNum', 'pageSize', 'loading'],
    components: { MapBox, MapStyle },
    data() {
      return {
        mapIns: null,
        geojson84: {},
        pointLayerIns: null,
        sourceIns: null,
        markersIns: [],
        hasMarkers: false,
      }
    },
    created() {},
    mounted() {},
    computed: {},
    watch: {
      list: {
        handler(newVal, oldVal) {
          this.markersIns.forEach(ele => {
            ele.remove()
          })

          if (this.mapIns?.getSource('points')) {
            this.mapIns.off('zoomend', this.onMapZoomEnd)
            clearSourceAndLayer(this.mapIns, ['points'], ['points'])
          }

          this.mapBounds(newVal)
        },
        deep: true,
      },
    },
    methods: {
      onMapMounted(mapIns) {
        this.mapIns = mapIns
        this.mapBounds(this.list)
      },

      onMapStyleLoad() {
        if (this.mapIns) {
          this.mapBounds(this.list)
        }
      },

      setMarkers(list) {
        this.geojson84 = {
          type: 'FeatureCollection',
          features: list
            .filter(el => +el.longitude && +el.latitude)
            .map(item => {
              return {
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [+item.longitude, +item.latitude],
                },
                properties: {
                  ...item,
                  name: item.projectName,
                },
              }
            }),
        }
        this.mapIns.loadImage(poiImg, (error, image) => {
          if (!image) return
          this.mapIns.addImage('custom-marker', image)

          this.mapIns.addSource('points', { type: 'geojson', data: this.geojson84 })

          this.mapIns
            .addLayer({
              id: 'points',
              type: 'circle',
              source: 'points',
              paint: {
                'circle-color': '#165DFF',
                'circle-radius': ['interpolate', ['exponential', 1], ['zoom'], 5, 3, 13.99, 6, 14, 0],
                'circle-stroke-width': 0,
                'circle-stroke-color': '#ffffff',
              },
            })
            .on('click', 'points', e => {
              const currentInfo = e.features[0].properties
              this.$listeners.clickItem && this.$listeners.clickItem(currentInfo)
            })

          // this.mapIns
          //   .addLayer({
          //     id: 'points2',
          //     type: 'symbol',
          //     source: 'points',
          //     layout: {
          //       'icon-image': 'custom-marker',
          //       'icon-ignore-placement': true,
          //       'icon-allow-overlap': true,
          //       'text-size': ['interpolate', ['exponential', 8], ['zoom'], 5, 0, 13.99, 0, 14, 12, 14.99, 12, 15, 0],
          //       'text-field': ['get', 'name'],
          //       'text-offset': [0, 1.25],
          //       'text-anchor': 'top',
          //       'text-allow-overlap': true,
          //       'icon-size': ['interpolate', ['exponential', 5], ['zoom'], 5, 0, 13.99, 0, 14, 0.4, 14.99, 0.5, 15, 0]
          //     }
          //   })
          //   .on('click', 'points2', e => {
          //     const currentInfo = e.features[0].properties
          //     this.$listeners.clickItem && this.$listeners.clickItem(currentInfo)
          //   })

          this.mapIns.on('zoomend', this.onMapZoomEnd)
        })
      },

      onMapZoomEnd(e) {
        if (e.target.getZoom() >= 14) {
          if (this.hasMarkers) return

          this.geojson84.features.forEach((item, index) => {
            console.log('*** 139 item.properties ***', item.properties)
            const el = document.createElement('div')
            el.innerHTML = `
              <div style="display:flex; align-items:center;">
                <div style=" border:2px solid #fff; border-radius:4px; position:relative; box-shadow:0px 4px 10px rgba(22, 93, 255, .4)">
                  <div style="width:34px; height:34px; border-radius:4px; background-size:100%; background-color:#fff; background-image:url('${
                    item.properties.projectImgUrl || defaultImg
                  }');">
                  </div>
                  <div style="position:absolute; bottom:-12px; left:50%; transform:translate(-50%); border-top:12px solid #fff; border-right:10px solid transparent; border-left: 10px solid transparent;">
                    <div style="position:absolute; left:-5px; top: -10px; border-top: 6px solid #165DFF;border-right: 5px solid transparent;border-left: 5px solid transparent;"></div>  
                  </div>
                </div>
                <div style="height:28px; line-height:28px; color:#1D2129; font-size:14px;font-weight:600; border-radius:0px 2px 2px 0; background:#fff; padding:0 8px; box-shadow:0px 4px 10px rgba(22, 93, 255, .4)">${
                  item.properties.siteName
                }</div>
              </div>`

            el.addEventListener('click', () => {
              this.$listeners.clickItem && this.$listeners.clickItem(item.properties)
            })

            let markIns = new mapboxgl.Marker({ anchor: 'bottom-left', offset: [-19, -9], element: el })
            markIns.setLngLat(item.geometry.coordinates).addTo(this.mapIns)

            this.markersIns.push(markIns)
          })
          this.hasMarkers = true
        } else {
          this.markersIns.forEach((item, index) => {
            item.remove()
            this.hasMarkers = false
          })
        }
      },

      mapBounds(newList) {
        if (newList?.length && !!this.mapIns) {
          const arr = newList.filter(el => +el.longitude && +el.latitude).map(el => [el.longitude, el.latitude])

          if (newList?.length == 1) {
            this.mapIns.flyTo({
              center: arr?.[0],
              zoom: 13,
            })
          } else {
            // 地图自动缩放偏移
            boundFlatLngLats(arr, this.mapIns)
          }

          this.setMarkers(newList)
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  .map-model {
    margin: 0 16px 16px;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
  }
</style>
