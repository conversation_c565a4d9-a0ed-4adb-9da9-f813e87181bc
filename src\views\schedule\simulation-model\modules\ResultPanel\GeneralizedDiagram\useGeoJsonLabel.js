import { GeoJsonLayer } from '@deck.gl/layers'

export default function useGeoJsonLabel(mapOverlay, pointsGeojson, legendType, layerIndex) {
  mapOverlay.setProps({
    layers: [
      new GeoJsonLayer({
        id: 'geojson-layer-point' + layerIndex,
        data: pointsGeojson,
        pointType: 'icon',
        // 处理 icon
        getIcon: d => {
          return { url: d.properties.icon, width: 22, height: 22, anchorY: 28 }
        },
        iconSizeMinPixels: 22,

        onClick: opt => {},
      }),
      new GeoJsonLayer({
        id: 'geojson-layer-text' + layerIndex,
        data: pointsGeojson,
        filled: true,
        stroked: true,
        pointType: 'text',
        getText: d => {
          return legendType == 1
            ? d.properties?.projectName + ': ' + (d.properties.upWlv == null ? '-' : d.properties.upWlv + 'm')
            : d.properties.type === 0 || d.properties.type === 2
              ? d.properties?.projectName + ': ' + (d.properties.outFlow == null ? '-' : d.properties.outFlow + 'm³/s')
              : d.properties?.projectName + ': ' + (d.properties.inFlow == null ? '-' : d.properties.inFlow + 'm³/s')
        },
        getTextColor: [255, 255, 255, 255],
        textCharacterSet: 'auto',
        getTextSize: 13,
        textOutlineColor: [255, 255, 255, 255],
        textOutlineWidth: 1,
        textFontSettings: { sdf: true, smoothing: 0.2 },
        onClick: opt => {},
      }),
    ],
  })
}
