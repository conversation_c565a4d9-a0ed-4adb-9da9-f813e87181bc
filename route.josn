{"code": 200, "message": "成功", "data": [{"menuId": 10320, "menuCode": "standardized", "menuName": "组织管理", "route": "/standardized", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/10320/", "isLink": 1, "openWith": 1, "perms": null, "icon": "标准化管理", "sort": 0, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10493, "menuCode": "organization-structure", "menuName": "组织机构", "route": "organization-structure", "component": "standardized/unit-manage/org-dept/index", "type": 3, "parentId": 10320, "parentPath": "0/10138/10320/10493/", "isLink": 1, "openWith": 3, "perms": null, "icon": "出库管理", "sort": 0, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10493}, {"menuId": 11321, "menuCode": "manage-user", "menuName": "管理人员", "route": "manage-user", "component": "standardized/unit-manage/user/index", "type": 3, "parentId": 10320, "parentPath": "0/10138/10320/11321/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 1, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11321}, {"menuId": 10328, "menuCode": "training-materials", "menuName": "培训管理", "route": "training-materials", "component": "standardized/unit-manage/training-materials/index", "type": 3, "parentId": 10320, "parentPath": "0/10138/10320/10328/", "isLink": 1, "openWith": 3, "perms": null, "icon": "培训资料", "sort": 2, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10328}, {"menuId": 10318, "menuCode": "enterprises-manage", "menuName": "物业单位", "route": "enterprises-manage", "component": "standardized/unit-manage/enterprises-manage/index", "type": 3, "parentId": 10320, "parentPath": "0/10138/10320/10318/", "isLink": 1, "openWith": 3, "perms": null, "icon": "物业化企业管理", "sort": 3, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10318}, {"menuId": 11330, "menuCode": "budget-manager", "menuName": "预算管理", "route": "/budget", "component": null, "type": 2, "parentId": 10320, "parentPath": "0/10138/10320/11330/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 70, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11331, "menuCode": "capital-budget", "menuName": "资金预算", "route": "capital-budget", "component": "standardized/budget/capital-budget/index", "type": 3, "parentId": 11330, "parentPath": "0/10138/10320/11330/11331/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11331}, {"menuId": 11332, "menuCode": "budget-project", "menuName": "项目管理", "route": "budget-project", "component": "standardized/budget/budget-project/index", "type": 3, "parentId": 11330, "parentPath": "0/10138/10320/11330/11332/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11332}], "isChecked": null, "id": 11330}], "isChecked": null, "id": 10320}, {"menuId": 11671, "menuCode": "org-function", "menuName": "组织职能", "route": "org-function", "component": "org-function/index", "type": 3, "parentId": 10138, "parentPath": "0/10138/11671/", "isLink": 1, "openWith": 3, "perms": null, "icon": "单位信息", "sort": 2, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11671}, {"menuId": 11194, "menuCode": "comprehensive-monitoring", "menuName": "综合监测", "route": "/comprehensive-monitoring", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/11194/", "isLink": 1, "openWith": 1, "perms": null, "icon": "控制运行", "sort": 4, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 10139, "menuCode": "basic-info", "menuName": "基础信息管理", "route": "/basic", "component": null, "type": 2, "parentId": 11194, "parentPath": "0/10138/11194/10139/", "isLink": 1, "openWith": 1, "perms": null, "icon": "基础信息管理", "sort": 1, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 11039, "menuCode": "hydraulic-project", "menuName": "工程信息", "route": "hydraulic-project", "component": "project/hydraulic-project/index", "type": 3, "parentId": 10139, "parentPath": "0/10138/11194/10139/11039/", "isLink": 1, "openWith": 3, "perms": null, "icon": "水利管理", "sort": 1, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11039}, {"menuId": 11040, "menuCode": "rivers-lakes", "menuName": "水系管理", "route": "rivers-lakes", "component": "project/rivers-lakes/index", "type": 3, "parentId": 10139, "parentPath": "0/10138/11194/10139/11040/", "isLink": 1, "openWith": 3, "perms": null, "icon": "水系管理", "sort": 2, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11040}, {"menuId": 11041, "menuCode": "monitoring-station", "menuName": "监测站点", "route": "monitoring-station", "component": "project/monitoring-station/index", "type": 3, "parentId": 10139, "parentPath": "0/10138/11194/10139/11041/", "isLink": 1, "openWith": 3, "perms": null, "icon": "监测站点管理", "sort": 3, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11041}, {"menuId": 10146, "menuCode": "camera", "menuName": "视频点位", "route": "camera", "component": "basic/camera/index", "type": 3, "parentId": 10139, "parentPath": "0/10138/11194/10139/10146/", "isLink": 1, "openWith": 3, "perms": null, "icon": "视频监控", "sort": 70, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10146}], "isChecked": null, "id": 10139}, {"menuId": 10562, "menuCode": "video-monitor", "menuName": "视频监测", "route": "video-monitor", "component": "video/video-monitor/index", "type": 3, "parentId": 11194, "parentPath": "0/10138/11194/10562/", "isLink": 1, "openWith": 3, "perms": null, "icon": "视频监控2", "sort": 3, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10562}, {"menuId": 11195, "menuCode": "multi-source-monitoring", "menuName": "多源监测", "route": "multi-process-line?keepAlive=false&type=2", "component": "water-rain/process-line", "type": 3, "parentId": 11194, "parentPath": "0/10138/11194/11195/", "isLink": 1, "openWith": 3, "perms": null, "icon": "水利对象分类管理", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11195}, {"menuId": 11346, "menuCode": "work-monitor", "menuName": "工情监测", "route": "work-monitor", "component": "work-monitor/index.vue", "type": 3, "parentId": 11194, "parentPath": "0/10138/11194/11346/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 50, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11346}, {"menuId": 10210, "menuCode": "real-time", "menuName": "实时水雨情", "route": "real-time", "component": "water-rain/real-time", "type": 3, "parentId": 11194, "parentPath": "0/10138/11194/10210/", "isLink": 1, "openWith": 3, "perms": null, "icon": "实时水雨情", "sort": 60, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10210}, {"menuId": 10211, "menuCode": "history", "menuName": "历史水雨情", "route": "history", "component": null, "type": 2, "parentId": 11194, "parentPath": "0/10138/11194/10211/", "isLink": 1, "openWith": 1, "perms": null, "icon": "历史水雨情", "sort": 70, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10212, "menuCode": "rainfall", "menuName": "雨量数据", "route": "rainfall", "component": "water-rain/history/rainfall", "type": 3, "parentId": 10211, "parentPath": "0/10138/11194/10211/10212/", "isLink": 1, "openWith": 3, "perms": null, "icon": "雨量数据", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10212}, {"menuId": 10213, "menuCode": "water-level", "menuName": "水位数据", "route": "water-level", "component": "water-rain/history/water-level", "type": 3, "parentId": 10211, "parentPath": "0/10138/11194/10211/10213/", "isLink": 1, "openWith": 3, "perms": null, "icon": "水位数据", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10213}, {"menuId": 10214, "menuCode": "flow", "menuName": "流量数据", "route": "flow", "component": "water-rain/history/flow", "type": 3, "parentId": 10211, "parentPath": "0/10138/11194/10211/10214/", "isLink": 1, "openWith": 3, "perms": null, "icon": "流量数据", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10214}], "isChecked": null, "id": 10211}], "isChecked": null, "id": 11194}, {"menuId": 10324, "menuCode": "engine-inspection", "menuName": "安全管理", "route": "engine-inspection", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/10324/", "isLink": 1, "openWith": 1, "perms": null, "icon": "工程检查", "sort": 5, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10337, "menuCode": "safety-inspection", "menuName": "安全检查", "route": "safety-inspection?keepAlive=false&type=3", "component": "standardized/engine-inspection/safety-inspection/index", "type": 3, "parentId": 10324, "parentPath": "0/10138/10324/10337/", "isLink": 1, "openWith": 3, "perms": null, "icon": "安全检查", "sort": 1, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 11361, "menuCode": "audit", "menuName": "审核", "route": null, "component": null, "type": 4, "parentId": 10337, "parentPath": "0/10138/10324/10337/11361/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11361}, {"menuId": 11365, "menuCode": "查看", "menuName": "查看", "route": null, "component": null, "type": 4, "parentId": 10337, "parentPath": "0/10138/10324/10337/11365/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11365}], "isChecked": null, "id": 10337}, {"menuId": 10334, "menuCode": "safety-identification", "menuName": "安全鉴定", "route": "safety-identification", "component": "standardized/safety-manage/safety-identification/index", "type": 3, "parentId": 10324, "parentPath": "0/10138/10324/10334/", "isLink": 1, "openWith": 3, "perms": null, "icon": "安全鉴定", "sort": 3, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10334}, {"menuId": 11322, "menuCode": "safety-monitor", "menuName": "安全监测", "route": "safety-monitor", "component": null, "type": 2, "parentId": 10324, "parentPath": "0/10138/10324/11322/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 4, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11541, "menuCode": "safety-tool", "menuName": "安全工器具", "route": "safety-tool?keepAlive=false&type=1", "component": "standardized/safety-manage/safety-moniter/safety-tool/index", "type": 3, "parentId": 11322, "parentPath": "0/10138/10324/11322/11541/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11541}, {"menuId": 11542, "menuCode": "fire-inspection", "menuName": "消防检查", "route": "fire-inspection?keepAlive=false&type=2", "component": "standardized/safety-manage/safety-moniter/safety-tool/index", "type": 3, "parentId": 11322, "parentPath": "0/10138/10324/11322/11542/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11542}, {"menuId": 11324, "menuCode": "pressure-pretest", "menuName": "高压预试", "route": "pressure-pretest", "component": "standardized/safety-manage/safety-moniter/pressure-pretest/index", "type": 3, "parentId": 11322, "parentPath": "0/10138/10324/11322/11324/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11324}], "isChecked": null, "id": 11322}, {"menuId": 11325, "menuCode": "hazard-source", "menuName": "危险源识别", "route": "hazard-source", "component": "standardized/safety-manage/hazard-source/index", "type": 3, "parentId": 10324, "parentPath": "0/10138/10324/11325/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 5, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11325}, {"menuId": 10330, "menuCode": "emergency-plan", "menuName": "应急预案", "route": "emergency-plan", "component": "standardized/emergency-manage/emergency-plan/index", "type": 3, "parentId": 10324, "parentPath": "0/10138/10324/10330/", "isLink": 1, "openWith": 3, "perms": null, "icon": "应急预案", "sort": 6, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10330}, {"menuId": 11326, "menuCode": "hidden-danger", "menuName": "隐患处理", "route": "hidden-danger", "component": "standardized/safety-manage/hidden-danger/index", "type": 3, "parentId": 10324, "parentPath": "0/10138/10324/11326/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11351, "menuCode": "conclude", "menuName": "办结", "route": null, "component": null, "type": 4, "parentId": 11326, "parentPath": "0/10138/10324/11326/11351/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11351}], "isChecked": null, "id": 11326}], "isChecked": null, "id": 10324}, {"menuId": 10148, "menuCode": "project-info", "menuName": "工程管理", "route": "/project", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/10148/", "isLink": 1, "openWith": 1, "perms": null, "icon": "工程信息", "sort": 6, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10201, "menuCode": "patrol-manager", "menuName": "工程巡查", "route": "/patrol", "component": null, "type": 2, "parentId": 10148, "parentPath": "0/10138/10148/10201/", "isLink": 1, "openWith": 1, "perms": null, "icon": "巡检管理", "sort": 1, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10281, "menuCode": "line-patrol", "menuName": "日常巡查", "route": "line-patrol", "component": null, "type": 2, "parentId": 10201, "parentPath": "0/10138/10148/10201/10281/", "isLink": 1, "openWith": 1, "perms": null, "icon": "路线巡检", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10285, "menuCode": "line-patrol-item", "menuName": "巡检项", "route": "line-patrol-item?keepAlive=false&patrolType=2", "component": "patrol-manage/dot-patrol/patrol-item", "type": 3, "parentId": 10281, "parentPath": "0/10138/10148/10201/10281/10285/", "isLink": 1, "openWith": 3, "perms": null, "icon": "巡检项", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10285}, {"menuId": 10286, "menuCode": "line-patrol-object", "menuName": "巡检对象", "route": "line-patrol-object?keepAlive=false&patrolType=2", "component": "patrol-manage/dot-patrol/patrol-object", "type": 3, "parentId": 10281, "parentPath": "0/10138/10148/10201/10281/10286/", "isLink": 1, "openWith": 3, "perms": null, "icon": "巡检对象", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10286}, {"menuId": 10282, "menuCode": "patrol-area", "menuName": "巡检范围", "route": "patrol-area?patrolType=2", "component": "patrol-manage/line-patrol/patrol-area", "type": 3, "parentId": 10281, "parentPath": "0/10138/10148/10201/10281/10282/", "isLink": 1, "openWith": 3, "perms": null, "icon": "巡检范围", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10282}, {"menuId": 10287, "menuCode": "line-patrol-plan", "menuName": "巡检计划", "route": "line-patrol-plan?keepAlive=false&patrolType=2", "component": "patrol-manage/dot-patrol/patrol-plan", "type": 3, "parentId": 10281, "parentPath": "0/10138/10148/10201/10281/10287/", "isLink": 1, "openWith": 3, "perms": null, "icon": "巡检计划", "sort": 40, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10287}, {"menuId": 10288, "menuCode": "line-patrol-task", "menuName": "巡检任务", "route": "line-patrol-task?keepAlive=false&patrolType=2", "component": "patrol-manage/dot-patrol/patrol-task", "type": 3, "parentId": 10281, "parentPath": "0/10138/10148/10201/10281/10288/", "isLink": 1, "openWith": 3, "perms": null, "icon": "巡检任务", "sort": 50, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10289, "menuCode": "task-delete", "menuName": "删除", "route": null, "component": null, "type": 4, "parentId": 10288, "parentPath": "0/10138/10148/10201/10281/10288/10289/", "isLink": 1, "openWith": 1, "perms": "task:delete", "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10289}], "isChecked": null, "id": 10288}, {"menuId": 10283, "menuCode": "line-monitor", "menuName": "巡检监控", "route": "line-monitor?patrolType=2", "component": "patrol-manage/line-patrol/line-monitor", "type": 3, "parentId": 10281, "parentPath": "0/10138/10148/10201/10281/10283/", "isLink": 1, "openWith": 3, "perms": null, "icon": "巡检监控", "sort": 60, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10283}, {"menuId": 10284, "menuCode": "track-replay", "menuName": "轨迹回放", "route": "track-replay?patrolType=2", "component": "patrol-manage/line-patrol/track-replay", "type": 3, "parentId": 10281, "parentPath": "0/10138/10148/10201/10281/10284/", "isLink": 1, "openWith": 3, "perms": null, "icon": "轨迹回放", "sort": 70, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10284}], "isChecked": null, "id": 10281}, {"menuId": 11198, "menuCode": "safety-inspection_type1", "menuName": "定期检查", "route": "safety-inspection-type1?keepAlive=false&type=1", "component": "standardized/engine-inspection/safety-inspection/index", "type": 3, "parentId": 10201, "parentPath": "0/10138/10148/10201/11198/", "isLink": 1, "openWith": 3, "perms": null, "icon": "安全检查", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 11358, "menuCode": "audit", "menuName": "审核", "route": null, "component": null, "type": 4, "parentId": 11198, "parentPath": "0/10138/10148/10201/11198/11358/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11358}, {"menuId": 11362, "menuCode": "555", "menuName": "555", "route": null, "component": null, "type": 4, "parentId": 11198, "parentPath": "0/10138/10148/10201/11198/11362/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11362}], "isChecked": null, "id": 11198}, {"menuId": 11199, "menuCode": "safety-inspection_type2", "menuName": "专项检查", "route": "safety-inspection-type2?keepAlive=false&type=2", "component": "standardized/engine-inspection/safety-inspection/index", "type": 3, "parentId": 10201, "parentPath": "0/10138/10148/10201/11199/", "isLink": 1, "openWith": 3, "perms": null, "icon": "安全管理", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 11359, "menuCode": "audit", "menuName": "审核", "route": null, "component": null, "type": 4, "parentId": 11199, "parentPath": "0/10138/10148/10201/11199/11359/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11359}, {"menuId": 11367, "menuCode": "121212", "menuName": "1212", "route": null, "component": null, "type": 4, "parentId": 11199, "parentPath": "0/10138/10148/10201/11199/11367/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11367}], "isChecked": null, "id": 11199}], "isChecked": null, "id": 10201}, {"menuId": 11333, "menuCode": "maintenance", "menuName": "维修养护", "route": "/maintenance", "component": null, "type": 2, "parentId": 10148, "parentPath": "0/10138/10148/11333/", "isLink": 1, "openWith": 1, "perms": null, "icon": "班组管理", "sort": 2, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11334, "menuCode": "maintenance-program", "menuName": "维养计划", "route": "maintenance-program", "component": "standardized/maintenance/maintenance-program/index", "type": 3, "parentId": 11333, "parentPath": "0/10138/10148/11333/11334/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11334}, {"menuId": 11335, "menuCode": "daily-maintenance", "menuName": "日常养护", "route": "daily-maintenance?keepAlive=false&isProperty=", "component": "standardized/maintenance/daily-maintenance/index", "type": 3, "parentId": 11333, "parentPath": "0/10138/10148/11333/11335/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11357, "menuCode": "audit", "menuName": "审核", "route": null, "component": null, "type": 4, "parentId": 11335, "parentPath": "0/10138/10148/11333/11335/11357/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11357}], "isChecked": null, "id": 11335}, {"menuId": 11336, "menuCode": "special-maintenance", "menuName": "专项维修", "route": "special-maintenance", "component": "standardized/maintenance/special-maintenance/index", "type": 3, "parentId": 11333, "parentPath": "0/10138/10148/11333/11336/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11355, "menuCode": "audit", "menuName": "审核", "route": null, "component": null, "type": 4, "parentId": 11336, "parentPath": "0/10138/10148/11333/11336/11355/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11355}], "isChecked": null, "id": 11336}, {"menuId": 11337, "menuCode": "emergency-repair", "menuName": "应急抢修", "route": "emergency-repair?keepAlive=false&isProperty=", "component": "standardized/maintenance/emergency-repair/index", "type": 3, "parentId": 11333, "parentPath": "0/10138/10148/11333/11337/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11337}], "isChecked": null, "id": 11333}, {"menuId": 11200, "menuCode": "engine-inspection_copy", "menuName": "工程观测", "route": "engine-inspection", "component": null, "type": 2, "parentId": 10148, "parentPath": "0/10138/10148/11200/", "isLink": 1, "openWith": 1, "perms": null, "icon": "工程检查", "sort": 4, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 11204, "menuCode": "settlement-observation", "menuName": "沉降观测", "route": "settlement-observation?keepAlive=false&type=1", "component": "standardized/engine-inspection/settlement-observation/index", "type": 3, "parentId": 11200, "parentPath": "0/10138/10148/11200/11204/", "isLink": 1, "openWith": 3, "perms": null, "icon": "沉降观察", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11204}, {"menuId": 11327, "menuCode": "displacement-observation", "menuName": "位移观测", "route": "displacement-observation?keepAlive=false&type=2", "component": "standardized/engine-inspection/settlement-observation/index", "type": 3, "parentId": 11200, "parentPath": "0/10138/10148/11200/11327/", "isLink": 1, "openWith": 3, "perms": null, "icon": "设备档案管理", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11327}], "isChecked": null, "id": 11200}, {"menuId": 10198, "menuCode": "work-manager", "menuName": "值班管理", "route": "/work", "component": null, "type": 2, "parentId": 10148, "parentPath": "0/10138/10148/10198/", "isLink": 1, "openWith": 1, "perms": null, "icon": "值班管理", "sort": 5, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10199, "menuCode": "work-shift", "menuName": "班次管理", "route": "work-shift", "component": "work/work-shift/index", "type": 3, "parentId": 10198, "parentPath": "0/10138/10148/10198/10199/", "isLink": 1, "openWith": 3, "perms": null, "icon": "班次管理", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10199}, {"menuId": 10576, "menuCode": "work-group", "menuName": "班组管理", "route": "work-group", "component": "work/work-group/index", "type": 3, "parentId": 10198, "parentPath": "0/10138/10148/10198/10576/", "isLink": 1, "openWith": 3, "perms": null, "icon": "班组管理", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10576}, {"menuId": 10200, "menuCode": "work-group-member", "menuName": "班组成员管理", "route": "work-group-member", "component": "work/work-group-member/index", "type": 3, "parentId": 10198, "parentPath": "0/10138/10148/10198/10200/", "isLink": 1, "openWith": 3, "perms": null, "icon": "班组管理", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10200}, {"menuId": 10577, "menuCode": "schedule-manage", "menuName": "排班管理", "route": "schedule-manage", "component": "work/schedule-manage/index", "type": 3, "parentId": 10198, "parentPath": "0/10138/10148/10198/10577/", "isLink": 1, "openWith": 3, "perms": null, "icon": "排班管理", "sort": 40, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10577}, {"menuId": 10578, "menuCode": "schedule-log", "menuName": "排班日志", "route": "schedule-log", "component": "work/schedule-log/index", "type": 3, "parentId": 10198, "parentPath": "0/10138/10148/10198/10578/", "isLink": 1, "openWith": 3, "perms": null, "icon": "排班日志", "sort": 50, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10578}, {"menuId": 10756, "menuCode": "overview-log", "menuName": "日志总览", "route": "overview-log", "component": "work/overview-log/index", "type": 3, "parentId": 10198, "parentPath": "0/10138/10148/10198/10756/", "isLink": 1, "openWith": 3, "perms": null, "icon": "日志总览", "sort": 60, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10756}], "isChecked": null, "id": 10198}, {"menuId": 11206, "menuCode": "basic-info_copy", "menuName": "设备管理", "route": "/basic", "component": null, "type": 2, "parentId": 10148, "parentPath": "0/10138/10148/11206/", "isLink": 1, "openWith": 1, "perms": null, "icon": "基础信息管理", "sort": 6, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 11210, "menuCode": "equipment-manage", "menuName": "设备维护", "route": "equipment-manage", "component": "basic/equipment-manage/index", "type": 3, "parentId": 11206, "parentPath": "0/10138/10148/11206/11210/", "isLink": 1, "openWith": 3, "perms": null, "icon": "设备管理", "sort": 40, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11210}, {"menuId": 10182, "menuCode": "equipment-record", "menuName": "设备信息", "route": "equipment-record", "component": "equipment/equipment-record", "type": 3, "parentId": 11206, "parentPath": "0/10138/10148/11206/10182/", "isLink": 1, "openWith": 3, "perms": null, "icon": "设备档案管理", "sort": 50, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10182}, {"menuId": 11344, "menuCode": "equipment-rating", "menuName": "设备评级", "route": "equipment-rating", "component": "equipment/equipment-rating/index", "type": 3, "parentId": 11206, "parentPath": "0/10138/10148/11206/11344/", "isLink": 1, "openWith": 3, "perms": null, "icon": "工情指标管理", "sort": 50, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11344}], "isChecked": null, "id": 11206}, {"menuId": 11328, "menuCode": "materialistic-manager", "menuName": "物业化管理", "route": "/materialistic", "component": null, "type": 2, "parentId": 10148, "parentPath": "0/10138/10148/11328/", "isLink": 1, "openWith": 1, "perms": null, "icon": "仓库设置", "sort": 60, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11343, "menuCode": "staffing", "menuName": "人员配备", "route": "staffing", "component": "standardized/materialistic/staffing/index", "type": 3, "parentId": 11328, "parentPath": "0/10138/10148/11328/11343/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11352, "menuCode": "audit", "menuName": "审核", "route": null, "component": null, "type": 4, "parentId": 11343, "parentPath": "0/10138/10148/11328/11343/11352/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11352}, {"menuId": 11366, "menuCode": "121", "menuName": "1212", "route": null, "component": null, "type": 4, "parentId": 11343, "parentPath": "0/10138/10148/11328/11343/11366/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11366}], "isChecked": null, "id": 11343}, {"menuId": 11340, "menuCode": "emergency-repair_copy", "menuName": "设备维养", "route": "emergency-repair?keepAlive=false&isProperty=true", "component": "standardized/maintenance/emergency-repair/index", "type": 3, "parentId": 11328, "parentPath": "0/10138/10148/11328/11340/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11340}, {"menuId": 11338, "menuCode": "daily-maintenance_copy", "menuName": "绿化养护", "route": "daily-maintenance?keepAlive=false&isProperty=true", "component": "standardized/maintenance/daily-maintenance/index", "type": 3, "parentId": 11328, "parentPath": "0/10138/10148/11328/11338/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11356, "menuCode": "audit", "menuName": "审核", "route": null, "component": null, "type": 4, "parentId": 11338, "parentPath": "0/10138/10148/11328/11338/11356/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11356}], "isChecked": null, "id": 11338}, {"menuId": 11329, "menuCode": "safety-inspection_copy_copy_copy", "menuName": "安全管理", "route": "safety-inspection?keepAlive=false&type=3&isProperty=true", "component": "standardized/engine-inspection/safety-inspection/index", "type": 3, "parentId": 11328, "parentPath": "0/10138/10148/11328/11329/", "isLink": 1, "openWith": 3, "perms": null, "icon": "安全检查", "sort": 40, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 11360, "menuCode": "audit", "menuName": "审核", "route": null, "component": null, "type": 4, "parentId": 11329, "parentPath": "0/10138/10148/11328/11329/11360/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11360}], "isChecked": null, "id": 11329}, {"menuId": 11522, "menuCode": "dispatch-project", "menuName": "调度值班配置", "route": "dispatch-project", "component": "custom/basic-info/dispatch-project/index", "type": 3, "parentId": 11328, "parentPath": "0/10138/10148/11328/11522/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 50, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11522}], "isChecked": null, "id": 11328}], "isChecked": null, "id": 10148}, {"menuId": 11221, "menuCode": "equment_copy", "menuName": "运行管理", "route": "/equipment", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/11221/", "isLink": 1, "openWith": 1, "perms": null, "icon": "设备管理", "sort": 7, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10340, "menuCode": "scheduling-rule", "menuName": "调度规则", "route": "scheduling-rule", "component": "standardized/scheduling/scheduling-rule/index", "type": 3, "parentId": 11221, "parentPath": "0/10138/11221/10340/", "isLink": 1, "openWith": 3, "perms": null, "icon": "调度规划", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10340}, {"menuId": 10339, "menuCode": "scheduling-plan", "menuName": "控运计划", "route": "scheduling-plan", "component": "standardized/scheduling/scheduling-plan/index", "type": 3, "parentId": 11221, "parentPath": "0/10138/11221/10339/", "isLink": 1, "openWith": 3, "perms": null, "icon": "控运计划", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10339}, {"menuId": 11523, "menuCode": "dispatch-case", "menuName": "调度方案", "route": "dispatch-case", "component": "custom/basic-info/dispatch-case/index", "type": 3, "parentId": 11221, "parentPath": "0/10138/11221/11523/", "isLink": 1, "openWith": 3, "perms": null, "icon": "调度规划", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11523}, {"menuId": 11524, "menuCode": "run-command", "menuName": "运行指令", "route": "run-command", "component": "custom/basic-info/run-command/index", "type": 3, "parentId": 11221, "parentPath": "0/10138/11221/11524/", "isLink": 1, "openWith": 3, "perms": null, "icon": "调度规划", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11524}, {"menuId": 11525, "menuCode": "opt-command", "menuName": "操作指令", "route": "opt-command", "component": "custom/basic-info/opt-command/index", "type": 3, "parentId": 11221, "parentPath": "0/10138/11221/11525/", "isLink": 1, "openWith": 3, "perms": null, "icon": "调度规划", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11525}], "isChecked": null, "id": 11221}, {"menuId": 11264, "menuCode": "farm_copy", "menuName": "水旱灾害防御", "route": "/farm/four-prevent", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/11264/", "isLink": 1, "openWith": 1, "perms": null, "icon": "系统实施", "sort": 9, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10532, "menuCode": "earlyWarning", "menuName": "预警预报中心", "route": "/earlyWarning", "component": null, "type": 2, "parentId": 11264, "parentPath": "0/10138/11264/10532/", "isLink": 1, "openWith": 1, "perms": null, "icon": "预警预报中心", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10533, "menuCode": "monitor", "menuName": "预警监控", "route": "monitor", "component": "early-warning/monitor", "type": 3, "parentId": 10532, "parentPath": "0/10138/11264/10532/10533/", "isLink": 1, "openWith": 3, "perms": null, "icon": "预警监控", "sort": 210, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10533}, {"menuId": 10534, "menuCode": "ruleConfiguration", "menuName": "预警规则配置", "route": "ruleConfiguration", "component": "early-warning/ruleConfiguration", "type": 3, "parentId": 10532, "parentPath": "0/10138/11264/10532/10534/", "isLink": 1, "openWith": 3, "perms": null, "icon": "预警规则配置", "sort": 260, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10534}, {"menuId": 10536, "menuCode": "informationNotification", "menuName": "预警信息通知", "route": "informationNotification", "component": "early-warning/informationNotification", "type": 3, "parentId": 10532, "parentPath": "0/10138/11264/10532/10536/", "isLink": 1, "openWith": 3, "perms": null, "icon": "预警信息通知", "sort": 280, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10536}, {"menuId": 10537, "menuCode": "messageTemplateConfig", "menuName": "消息模板配置", "route": "messageTemplateConfig", "component": "early-warning/messageTemplateConfig", "type": 3, "parentId": 10532, "parentPath": "0/10138/11264/10532/10537/", "isLink": 1, "openWith": 3, "perms": null, "icon": "消息模版配置", "sort": 290, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10537}, {"menuId": 10538, "menuCode": "waterLevelWarning", "menuName": "水位预警值管理", "route": "waterLevelWarning", "component": null, "type": 2, "parentId": 10532, "parentPath": "0/10138/11264/10532/10538/", "isLink": 1, "openWith": 1, "perms": null, "icon": "水位预警值管理", "sort": 460, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10539, "menuCode": "constant", "menuName": "恒定阈值", "route": "constant", "component": "early-warning/waterLevelWarning/constant", "type": 3, "parentId": 10538, "parentPath": "0/10138/11264/10532/10538/10539/", "isLink": 1, "openWith": 3, "perms": null, "icon": "恒定阈值", "sort": 310, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10539}, {"menuId": 10540, "menuCode": "timeShare", "menuName": "分时阈值", "route": "timeShare", "component": "early-warning/waterLevelWarning/timeShare", "type": 3, "parentId": 10538, "parentPath": "0/10138/11264/10532/10538/10540/", "isLink": 1, "openWith": 3, "perms": null, "icon": "分时阈值", "sort": 320, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10540}], "isChecked": null, "id": 10538}], "isChecked": null, "id": 10532}, {"menuId": 11543, "menuCode": "rainfall-forecast", "menuName": "降雨监测", "route": "rainfall-forecast", "component": "schedule/rainfall-forecast/index", "type": 3, "parentId": 11264, "parentPath": "0/10138/11264/11543/", "isLink": 1, "openWith": 3, "perms": null, "icon": "实时水雨情", "sort": 50, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11543}, {"menuId": 11544, "menuCode": "simulation-model", "menuName": "河道仿真", "route": "simulation-model", "component": "schedule/simulation-model/index", "type": 3, "parentId": 11264, "parentPath": "0/10138/11264/11544/", "isLink": 1, "openWith": 3, "perms": null, "icon": "防洪调度", "sort": 80, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11544}, {"menuId": 11545, "menuCode": "drought-model", "menuName": "旱情预警识别模型", "route": "drought-model", "component": "schedule/drought-model/index", "type": 3, "parentId": 11264, "parentPath": "0/10138/11264/11545/", "isLink": 1, "openWith": 3, "perms": null, "icon": "流量数据", "sort": 90, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11545}, {"menuId": 11673, "menuCode": "schedule-model", "menuName": "调度模型管理", "route": "schedule-model", "component": "schedule/schedule-model/index", "type": 3, "parentId": 11264, "parentPath": "0/10138/11264/11673/", "isLink": 1, "openWith": 3, "perms": "", "icon": "典型案例", "sort": 100, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11673}, {"menuId": 11676, "menuCode": "model-result", "menuName": "模型结果查看", "route": "model-result", "component": "schedule/model-result/index", "type": 3, "parentId": 11264, "parentPath": "0/10138/11264/11676/", "isLink": 1, "openWith": 3, "perms": null, "icon": "工程信息", "sort": 110, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11676}, {"menuId": 11677, "menuCode": "evaluate", "menuName": "洪涝灾害评价", "route": "evaluate", "component": "schedule/evaluate/index", "type": 3, "parentId": 11264, "parentPath": "0/10138/11264/11677/", "isLink": 1, "openWith": 3, "perms": null, "icon": "培训资料", "sort": 120, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11677}], "isChecked": null, "id": 11264}, {"menuId": 11271, "menuCode": "implement-configure", "menuName": "实施配置", "route": "/implement-configure", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/11271/", "isLink": 1, "openWith": 1, "perms": null, "icon": "调度规程", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10150, "menuCode": "system-implement", "menuName": "系统实施", "route": "/system", "component": null, "type": 2, "parentId": 11271, "parentPath": "0/10138/11271/10150/", "isLink": 1, "openWith": 1, "perms": null, "icon": "系统实施", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10152, "menuCode": "monitor-data", "menuName": "监测数据采集", "route": "monitor-data", "component": null, "type": 2, "parentId": 10150, "parentPath": "0/10138/11271/10150/10152/", "isLink": 1, "openWith": 1, "perms": null, "icon": "监测数据采集", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10153, "menuCode": "site-maintain", "menuName": "站点信息维护", "route": "site-maintain", "component": "system/monitor-data/site-maintain/index", "type": 3, "parentId": 10152, "parentPath": "0/10138/11271/10150/10152/10153/", "isLink": 1, "openWith": 3, "perms": null, "icon": "站点信息维护", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10153}, {"menuId": 10155, "menuCode": "terminal", "menuName": "采集终端管理", "route": "terminal", "component": "system/monitor-data/terminal/index", "type": 3, "parentId": 10152, "parentPath": "0/10138/11271/10150/10152/10155/", "isLink": 1, "openWith": 3, "perms": null, "icon": "采集终端管理", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10155}, {"menuId": 10156, "menuCode": "monitoring", "menuName": "采集监控", "route": "monitoring", "component": "system/monitor-data/monitoring/index", "type": 3, "parentId": 10152, "parentPath": "0/10138/11271/10150/10152/10156/", "isLink": 1, "openWith": 3, "perms": null, "icon": "采集监控", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10156}], "isChecked": null, "id": 10152}, {"menuId": 10310, "menuCode": "work-data", "menuName": "工情数据采集", "route": "work-data", "component": null, "type": 2, "parentId": 10150, "parentPath": "0/10138/11271/10150/10310/", "isLink": 1, "openWith": 1, "perms": null, "icon": "工情数据采集", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10314, "menuCode": "work-Index", "menuName": "工情指标管理", "route": "work-Index", "component": "system/work-data/work-Index/index", "type": 3, "parentId": 10310, "parentPath": "0/10138/11271/10150/10310/10314/", "isLink": 1, "openWith": 3, "perms": null, "icon": "工情指标管理", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10314}, {"menuId": 10311, "menuCode": "Index-group", "menuName": "指标组管理", "route": "Index-group", "component": "system/work-data/Index-group/index", "type": 3, "parentId": 10310, "parentPath": "0/10138/11271/10150/10310/10311/", "isLink": 1, "openWith": 3, "perms": null, "icon": "指标组管理", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10311}, {"menuId": 10312, "menuCode": "equipment-monitoring", "menuName": "设备监控指标", "route": "equipment-monitoring", "component": "system/work-data/equipment-monitoring/index", "type": 3, "parentId": 10310, "parentPath": "0/10138/11271/10150/10310/10312/", "isLink": 1, "openWith": 3, "perms": null, "icon": "设备监控指标", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10312}, {"menuId": 10313, "menuCode": "working-condition", "menuName": "工况数据采集监控", "route": "working-condition", "component": "system/work-data/working-condition/index", "type": 3, "parentId": 10310, "parentPath": "0/10138/11271/10150/10310/10313/", "isLink": 1, "openWith": 3, "perms": null, "icon": "工况数据采集监控", "sort": 40, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10313}], "isChecked": null, "id": 10310}, {"menuId": 11516, "menuCode": "water-resource", "menuName": "水资源管理", "route": "water-resource", "component": null, "type": 2, "parentId": 10150, "parentPath": "0/10138/11271/10150/11516/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11517, "menuCode": "water-charge", "menuName": "水费收取管理", "route": "water-charge", "component": "custom/water-resource/water-charge/index", "type": 3, "parentId": 11516, "parentPath": "0/10138/11271/10150/11516/11517/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11517}, {"menuId": 11518, "menuCode": "water-airport", "menuName": "机埠水电管理", "route": "water-airport", "component": "custom/water-resource/water-airport/index", "type": 3, "parentId": 11516, "parentPath": "0/10138/11271/10150/11516/11518/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11518}, {"menuId": 11520, "menuCode": "water-backbone-fact", "menuName": "工程实际用水量", "route": "water-backbone-fact", "component": "custom/water-resource/water-backbone-fact/index", "type": 3, "parentId": 11516, "parentPath": "0/10138/11271/10150/11516/11520/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11520}, {"menuId": 11519, "menuCode": "water-backbone-plan", "menuName": "工程计划用水量", "route": "water-backbone-plan", "component": "custom/water-resource/water-backbone-plan/index", "type": 3, "parentId": 11516, "parentPath": "0/10138/11271/10150/11516/11519/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11519}, {"menuId": 11670, "menuCode": "water-consumption", "menuName": "灌溉计划用水量", "route": "water-consumption", "component": "custom/water-resource/water-consumption/index", "type": 3, "parentId": 11516, "parentPath": "0/10138/11271/10150/11516/11670/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11670}], "isChecked": null, "id": 11516}, {"menuId": 11521, "menuCode": "basic-info", "menuName": "基础信息", "route": "basic-info", "component": null, "type": 2, "parentId": 10150, "parentPath": "0/10138/11271/10150/11521/", "isLink": 1, "openWith": 1, "perms": null, "icon": "", "sort": 40, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11521}], "isChecked": null, "id": 10150}, {"menuId": 11275, "menuCode": "/base-system", "menuName": "系统配置", "route": "/base-system", "component": null, "type": 2, "parentId": 11271, "parentPath": "0/10138/11271/11275/", "isLink": 1, "openWith": 1, "perms": null, "icon": "系统实施", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10140, "menuCode": "object-category", "menuName": "水利对象分类管理", "route": "object-category", "component": "basic/object-category/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/10140/", "isLink": 1, "openWith": 3, "perms": null, "icon": "水利对象分类管理", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10140}, {"menuId": 10141, "menuCode": "equipment-category", "menuName": "设备分类管理", "route": "equipment-category", "component": "basic/equipment-category/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/10141/", "isLink": 1, "openWith": 3, "perms": null, "icon": "设备分类管理", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10141}, {"menuId": 10142, "menuCode": "district", "menuName": "行政区划管理", "route": "district", "component": "basic/district/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/10142/", "isLink": 1, "openWith": 3, "perms": null, "icon": "仓库管理", "sort": 30, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10142}, {"menuId": 11035, "menuCode": "project-position", "menuName": "工程部位", "route": "project-position", "component": "basic/project-position/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/11035/", "isLink": 1, "openWith": 3, "perms": null, "icon": "巡检对象", "sort": 40, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11035}, {"menuId": 11315, "menuCode": "other-manage_copy", "menuName": "其他管理对象", "route": "other-manage", "component": "basic/other-manage/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/11315/", "isLink": 1, "openWith": 3, "perms": null, "icon": "培训资料", "sort": 50, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11315}, {"menuId": 11316, "menuCode": "data-permission_copy", "menuName": "数据权限", "route": "data-permission", "component": "basic/data-permission/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/11316/", "isLink": 1, "openWith": 3, "perms": null, "icon": "数据权限", "sort": 60, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11316}, {"menuId": 10529, "menuCode": "address-book", "menuName": "通讯录管理", "route": "address-book", "component": null, "type": 2, "parentId": 11275, "parentPath": "0/10138/11271/11275/10529/", "isLink": 1, "openWith": 1, "perms": null, "icon": "通讯录管理", "sort": 70, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [{"menuId": 10530, "menuCode": "organization", "menuName": "组织架构", "route": "Organization", "component": "basic/address-book/organization", "type": 3, "parentId": 10529, "parentPath": "0/10138/11271/11275/10529/10530/", "isLink": 1, "openWith": 3, "perms": null, "icon": "connections", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10530}, {"menuId": 10531, "menuCode": "customize", "menuName": "自定义分组", "route": "Customize", "component": "basic/address-book/customize/index", "type": 3, "parentId": 10529, "parentPath": "0/10138/11271/11275/10529/10531/", "isLink": 1, "openWith": 3, "perms": null, "icon": "自定义分组", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10531}], "isChecked": null, "id": 10529}, {"menuId": 10143, "menuCode": "equipment-manage", "menuName": "设备管理", "route": "equipment-manage", "component": "basic/equipment-manage/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/10143/", "isLink": 1, "openWith": 3, "perms": null, "icon": "设备管理", "sort": 80, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10143}, {"menuId": 10144, "menuCode": "base-project-manage", "menuName": "工程管理", "route": "base-project-manage", "component": "basic/project-manage/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/10144/", "isLink": 1, "openWith": 3, "perms": null, "icon": "工程管理", "sort": 90, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10144}, {"menuId": 10145, "menuCode": "river-system", "menuName": "水系管理", "route": "river-system", "component": "basic/river-system/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/10145/", "isLink": 1, "openWith": 3, "perms": null, "icon": "水系管理", "sort": 100, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10145}, {"menuId": 10147, "menuCode": "site-manage", "menuName": "测站信息", "route": "site-manage", "component": "basic/site-manage/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/10147/", "isLink": 1, "openWith": 3, "perms": null, "icon": "监测站点管理", "sort": 110, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10147}, {"menuId": 11320, "menuCode": "camera_copy", "menuName": "视频点位", "route": "camera", "component": "basic/camera/index", "type": 3, "parentId": 11275, "parentPath": "0/10138/11271/11275/11320/", "isLink": 1, "openWith": 3, "perms": null, "icon": "视频监控", "sort": 120, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11320}], "isChecked": null, "id": 11275}], "isChecked": null, "id": 11271}, {"menuId": 11031, "menuCode": "project-runtime", "menuName": "工程运行管理", "route": "/farm/project-runtime", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/11031/", "isLink": 1, "openWith": 1, "perms": null, "icon": "发布项维护", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 11031}, {"menuId": 11341, "menuCode": "archives", "menuName": "档案管理", "route": "archives", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/11341/", "isLink": 1, "openWith": 1, "perms": null, "icon": "基础信息管理", "sort": 150, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11342, "menuCode": "paper-file", "menuName": "纸质档案", "route": "paper-file", "component": "archives/paper-file/index", "type": 3, "parentId": 11341, "parentPath": "0/10138/11341/11342/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11342}, {"menuId": 11345, "menuCode": "cloud-file", "menuName": "电子档案", "route": "cloud-file", "component": "archives/cloud-file/index", "type": 3, "parentId": 11341, "parentPath": "0/10138/11341/11345/", "isLink": 1, "openWith": 3, "perms": null, "icon": "", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11345}], "isChecked": null, "id": 11341}, {"menuId": 11368, "menuCode": "new", "menuName": "灌区风采", "route": "new", "component": null, "type": 2, "parentId": 10138, "parentPath": "0/10138/11368/", "isLink": 1, "openWith": 1, "perms": null, "icon": "单位信息", "sort": 160, "remark": null, "isDisabled": 0, "isLeaf": 0, "isHidden": 0, "title": null, "client": "pc", "children": [{"menuId": 11369, "menuCode": "news", "menuName": "灌区新闻", "route": "news?keepAlive=false&type=1", "component": "new/news/index", "type": 3, "parentId": 11368, "parentPath": "0/10138/11368/11369/", "isLink": 1, "openWith": 3, "perms": null, "icon": "培训资料", "sort": 10, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11369}, {"menuId": 11370, "menuCode": "events", "menuName": "灌区活动", "route": "events?keepAlive=false&type=2", "component": "new/news/index", "type": 3, "parentId": 11368, "parentPath": "0/10138/11368/11370/", "isLink": 1, "openWith": 3, "perms": null, "icon": "管理制度", "sort": 20, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": "pc", "children": [], "isChecked": null, "id": 11370}], "isChecked": null, "id": 11368}, {"menuId": 10186, "menuCode": "warehouse-set", "menuName": "仓库设置", "route": "warehouse-set", "component": "warehouse/info-set/warehouse-set/index", "type": 3, "parentId": 10138, "parentPath": "0/10138/10186/", "isLink": 1, "openWith": 3, "perms": null, "icon": "仓库设置", "sort": 180, "remark": null, "isDisabled": 0, "isLeaf": 1, "isHidden": 0, "title": null, "client": null, "children": [], "isChecked": null, "id": 10186}], "success": true, "timestamp": 1751533728930, "traceId": null}