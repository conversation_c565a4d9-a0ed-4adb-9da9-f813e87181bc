<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="650"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="项目编码" prop="projectCode">
              <a-input v-model="form.projectCode" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="项目名称" prop="projectName">
              <a-input v-model="form.projectName" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="中标日期">
              <a-date-picker
                allow-clear
                style="width: 200px"
                v-model="form.winBidDate"
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="中标单位">
              <a-input v-model="form.winBidUnit" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="合同金额(元)" prop="contractAmount">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.contractAmount"
                :precision="2"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="工期(年)">
              <a-input-number
                style="width: 200px"
                allow-clear
                placeholder="请输入"
                v-model="form.projectDuration"
                :precision="1"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addBudgetProject, editBudgetProject, getBudgetProjectById } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormBudgetProject',
    components: { AntModal },
    props: ['projectOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          contractAmount: undefined,
          projectCode: undefined,
          projectDuration: undefined,
          projectName: undefined,
          winBidDate: undefined,
          winBidUnit: undefined,
        },
        open: false,
        rules: {
          projectCode: [{ required: true, message: '项目编码不能为空', trigger: 'blur' }],
          projectName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
          contractAmount: [{ required: true, message: '合同金额不能为空', trigger: 'blur, change' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handleForm(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getBudgetProjectById({ projectId: row.projectId }).then(res => {
            if (res.code == 200) {
              this.form = res.data
            }
            this.modalLoading = false
          })
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.form.projectId == null) {
              addBudgetProject(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editBudgetProject(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped></style>
