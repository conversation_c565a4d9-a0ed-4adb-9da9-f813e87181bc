<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="方案编码">
        <a-input
          v-model="queryParam.schedulingCode"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="方案名称">
        <a-input
          v-model="queryParam.schedulingName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>
      <a-form-item label="片区">
        <a-select allowClear v-model="queryParam.fcstRange" placeholder="请选择" :options="fcstRangeOptions"></a-select>
      </a-form-item>

      <a-form-item label="创建人">
        <a-select
          allowClear
          v-model="queryParam.userId"
          placeholder="请选择"
          :options="createdUserOptions"
          show-search
          :filter-option="
            (input, option) => option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
          "
        ></a-select>
      </a-form-item>
      <a-form-item label="调度类型">
        <a-select
          allowClear
          v-model="queryParam.dispathType"
          placeholder="请选择"
          :options="dispatchTypeOptions"
        ></a-select>
      </a-form-item>
      <a-form-item label="预报类型">
        <a-select
          allowClear
          v-model="queryParam.sourceType"
          placeholder="请选择"
          :options="sourceTypeOptions"
        ></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="调度模型管理"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-tooltip :title="canCompareTooltip">
              <a-button :disabled="!canCompare" @click="handlePlanComparison" style="margin-right: 8px">
                <a-icon type="swap" />
                方案对比
              </a-button>
            </a-tooltip>
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              手动预报
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>
    <AddModal
      ref="addModalRef"
      v-if="showAddModal"
      @close="showAddModal = false"
      @ok="onOperationComplete"
      :fcstRangeOptions="fcstRangeOptions"
      :sceneOptions="sceneOptions"
    />
    <ViewParams
      ref="viewParamsRef"
      v-if="showViewParams"
      @close="showViewParams = false"
      :fcstRangeOptions="fcstRangeOptions"
    />
    <PlanComparison
      ref="planComparisonRef"
      v-if="showPlanComparison"
      @close="showPlanComparison = false"
      :fcstRangeOptions="fcstRangeOptions"
      :dispatchTypeOptions="dispatchTypeOptions"
      :sourceTypeOptions="sourceTypeOptions"
    />
  </div>
</template>

<script lang="jsx">
  import { getComUserList, getOptions, getValueByKey } from '@/api/common'
  import { getSchedulingPage, deleteScheduling } from './services.js'
  import queryString from 'query-string'

  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'
  import { dispatchTypeOptions, sourceTypeOptions, sceneOptions } from './config.js'
  import AddModal from './modules/AddModal/index.vue'
  import ViewParams from './modules/ViewParams/index.vue'
  import PlanComparison from './modules/PlanComparison/index.vue'

  export default {
    name: 'ScheduleModel',
    components: {
      VxeTable,
      VxeTableForm,
      AddModal,
      ViewParams,
      PlanComparison,
    },
    data() {
      return {
        exportLoading: false,
        showAddModal: false,
        showViewParams: false,
        showPlanComparison: false,
        dispatchTypeOptions,
        sourceTypeOptions,
        sceneOptions,
        createdUserOptions: [],
        fcstRangeOptions: [],
        submergedAnalysis: '', // 淹没分析地址
        list: [],
        isChecked: false,
        ids: [],
        names: [],
        selectedRecords: [],
        loading: false,
        total: 0,

        queryParam: {
          schedulingCode: undefined,
          schedulingName: undefined,
          fcstRange: undefined,
          dispathType: undefined,
          sourceType: undefined,
          userId: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '方案编码',
            field: 'schedulingCode',
            minWidth: 220,
            showOverflow: 'tooltip',
          },
          {
            title: '方案名称',
            field: 'schedulingName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
          {
            title: '片区',
            field: 'fcstRange',
            minWidth: 110,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.fcstRangeOptions.find(el => el.value == row.fcstRange)?.label
              },
            },
          },
          {
            title: '调度类型',
            field: 'dispathType',
            minWidth: 110,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.dispatchTypeOptions.find(el => el.value == row.dispathType)?.label
              },
            },
          },

          {
            title: '预演时间',
            minWidth: 330,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => row.startTime + ' - ' + row.endTime,
            },
          },

          {
            title: '预报类型',
            field: 'sourceType',
            minWidth: 110,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.sourceTypeOptions.find(el => el.value == row.sourceType)?.label
              },
            },
          },
          {
            title: '创建人',
            field: 'createdUserName',
            minWidth: 80,
          },
          {
            title: '当前状态',
            minWidth: 110,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => (row.status === null ? '' : row.status === 1 ? '已完成' : '进行中'),
            },
          },
          {
            title: '操作',
            field: 'operate',
            width: 140,
            headerAlign: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a-dropdown>
                      <a>查看</a>
                      <a-menu slot='overlay'>
                        <a-menu-item>
                          <a onClick={() => this.handleDrainage(row)}>调度仿真结果</a>
                        </a-menu-item>
                        <a-menu-item>
                          <a onClick={() => this.handleHydrodynamic(row)}>洪涝灾害评价</a>
                        </a-menu-item>
                      </a-menu>
                    </a-dropdown>
                    {row.sourceType !== 1 && (
                      <span>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete(row)}>删除</a>
                      </span>
                    )}
                    <a-divider type='vertical' />
                    <a-dropdown>
                      <a>···</a>
                      <a-menu slot='overlay'>
                        <a-menu-item>
                          <a
                            onClick={() => {
                              if (!this.submergedAnalysis) {
                                this.$message.warning('淹没分析地址未配置')
                                return
                              }
                              const params = queryString.parse(this.submergedAnalysis.split('?')[1])
                              const obj = { ...params }
                              delete obj.east
                              delete obj.west

                              if (row.fcstRange === '1') {
                                //东片区
                                window.open(
                                  `${this.submergedAnalysis.split('?')[0]}?${queryString.stringify({
                                    ...obj,
                                    ...JSON.parse(params.east),
                                  })}`,
                                )
                              }

                              if (row.fcstRange === '2') {
                                //西片区
                                window.open(
                                  `${this.submergedAnalysis.split('?')[0]}?${queryString.stringify({
                                    ...obj,
                                    ...JSON.parse(params.west),
                                  })}`,
                                )
                              }
                            }}
                          >
                            淹没分析
                          </a>
                        </a-menu-item>
                        <a-menu-item>
                          <a onClick={() => this.viewModelParams(row)}>查看参数</a>
                        </a-menu-item>
                      </a-menu>
                    </a-dropdown>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {
      // 是否可以进行方案对比
      canCompare() {
        if (this.selectedRecords.length !== 2) {
          return false
        }

        const [plan1, plan2] = this.selectedRecords
        // 检查是否为同一片区且同一调度类型
        return plan1.fcstRange === plan2.fcstRange && plan1.dispathType === plan2.dispathType
      },

      // 方案对比按钮的提示文本
      canCompareTooltip() {
        if (this.selectedRecords.length === 0) {
          return '请先勾选两个预报方案，且方案为同一片区，调度类型相同'
        } else if (this.selectedRecords.length === 1) {
          return '请再勾选一个预报方案，且方案为同一片区，调度类型相同'
        } else if (this.selectedRecords.length === 2) {
          const [plan1, plan2] = this.selectedRecords
          if (plan1.fcstRange !== plan2.fcstRange) {
            return '所选方案不是同一片区，无法对比'
          } else if (plan1.dispathType !== plan2.dispathType) {
            return '所选方案调度类型不同，无法对比'
          } else {
            return '点击进行方案对比'
          }
        } else {
          return '请只勾选两个预报方案进行对比'
        }
      },
    },
    watch: {},
    created() {
      getComUserList({}).then(res => {
        this.createdUserOptions = res?.data.map(el => ({ label: el.name, value: el.userId }))
      })
      getOptions('fcstRange').then(res => {
        this.fcstRangeOptions = res.data.map(el => ({ label: el.value, value: +el.key }))
      })
      getValueByKey('submerged.analysis').then(res => {
        this.submergedAnalysis = res.data
      })
    },

    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showAddModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getSchedulingPage(this.queryParam).then(response => {
          this.list = response.data?.data || []
          this.total = response.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          schedulingCode: undefined,
          schedulingName: undefined,
          fcstRange: undefined,
          dispathType: undefined,
          sourceType: undefined,
          userId: undefined,
          pageNum: 1,
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.schedulingId)
        this.names = valObj.records.map(item => item.schedulingName)
        this.selectedRecords = valObj.records
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.addModalRef.handleShow())
      },

      /* 方案对比 */
      handlePlanComparison() {
        if (this.canCompare) {
          console.log('选中的记录用于方案对比:', this.selectedRecords)
          this.showPlanComparison = true
          this.$nextTick(() => this.$refs.planComparisonRef.handleShow(this.selectedRecords))
        }
      },
      /* 调度仿真结果-（田间排水过程与喝到水动力过程）*/
      handleDrainage(record) {
        this.$router.push({
          name: 'model-result',
          query: { schedulingName: record.schedulingName, modelId: record.modelId, modelScene: record.scene },
        })
      },
      /* 洪涝灾害评价 */
      handleHydrodynamic(record) {
        this.$router.push({
          path: '/farm/four-prevent/evaluate',
          query: { schedulingName: record.schedulingName, schedulingId: record.schedulingId },
        })
      },
      /* 模型参数 */
      viewModelParams(record) {
        this.showViewParams = true
        this.$nextTick(() => this.$refs.viewParamsRef.handleShow(record))
      },
      handleCopy(record) {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.addModalRef.handleShow(record, 'copy'))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const schedulingIds = row.schedulingId ? [row.schedulingId] : this.ids
        const names = row.schedulingName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteScheduling({ schedulingIds: schedulingIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
