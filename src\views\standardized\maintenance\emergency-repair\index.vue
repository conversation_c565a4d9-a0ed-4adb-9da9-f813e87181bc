<template>
  <div class="common-table-page" :style="isDetail ? { height: '100%' } : {}">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="工程名称">
        <a-tree-select
          :disabled="isDetail"
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-item>

      <a-form-item label="维修时间">
        <a-range-picker
          allow-clear
          :value="planTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>

      <a-form-item label="维修结果">
        <a-select
          show-search
          allowClear
          placeholder="请选择"
          v-model="queryParam.repairResult"
          option-filter-prop="children"
        >
          <a-select-option v-for="item in repairResultOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :projectOptions="projectOptions"
          :repairResultOptions="repairResultOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <RepairDetails
          v-if="showRepairDetails"
          ref="repairDetailsRef"
          :projectOptions="projectOptions"
          :repairResultOptions="repairResultOptions"
          @ok="onOperationComplete"
          @close="showRepairDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getRepairPage, deleteRepair } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import RepairDetails from './modules/RepairDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'DailyMaintenance',
    components: {
      VxeTable,
      VxeTableForm,
      RepairDetails,
      FormDrawer,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        isProperty: '',
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        isChecked: false,
        checkOptions: [
          { key: 0, value: '否' },
          { key: 1, value: '是' },
        ],

        repairResultOptions: [],
        repairResults: [],

        curingTypeOptions: [], //项目类型
        planSourceOptions: [], //项目来源
        reportStatusOptions: [], //申报状态
        curingTypes: [],
        planSources: [],
        reportStatuses: [],

        propertyList: [],
        inspectionTypeOptions: [],
        inspectionTypes: [],
        inspectionStatuses: [],

        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showRepairDetails: false,
        planTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '应急抢修',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          repairResult: undefined,
          startTime: '',
          endTime: '',
          isProperty: '',
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
          },

          {
            title: '维修内容',
            field: 'repairContent',
            minWidth: 110,
            showOverflow: true,
          },

          {
            title: '维修人员',
            field: 'repairUserName',
            minWidth: 60,
          },
          {
            title: '维修结果',
            field: 'repairResult',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.repairResultOptions?.find(el => el.key == row.repairResult)?.value
              },
            },
          },

          {
            title: '维修时间',
            field: 'repairTime',
            minWidth: 90,
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 100,
            showOverflow: true,
          },

          {
            title: '创建人',
            field: 'createdUserName',
            minWidth: 60,
          },
          {
            title: '创建时间',
            field: 'createdTime',
            minWidth: 90,
          },

          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    {!this.isDetail && <a-divider type='vertical' />}
                    {!this.isDetail && <a onClick={() => this.handleEdit(row)}>修改</a>}
                    {!this.isDetail && <a-divider type='vertical' />}
                    {!this.isDetail && <a onClick={() => this.handleDelete(row)}>删除</a>}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
      this.queryParam.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
      this.tableTitle = this.$route.meta.query?.isProperty ? '设备维养' : '应急抢修'
      this.getList()

      getOptions('repairResult').then(res => {
        this.repairResultOptions = res.data
        this.repairResults = getFlatTreeMap(this.repairResultOptions, 'key')
      })

      // getPropertyList({
      //   pageNum: 1,
      //   pageSize: Number.MAX_SAFE_INTEGER,
      //   projectId: null,
      //   propertyCharge: '',
      //   propertyName: '',
      // }).then(res => {
      //   this.propertyList = res?.data?.data
      // })

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })

      // this.inspectionStatuses = getFlatTreeMap(this.inspectionStatusOptions, 'key')
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      onRangeChange(value, planTimes) {
        this.planTimes = value
        if (planTimes.length == 0) {
          return
        }
        this.queryParam.startTime = planTimes[0] ? moment(planTimes[0]).format('YYYY-MM-DD HH:mm:ss') : undefined
        this.queryParam.endTime = planTimes[1] ? moment(planTimes[1]).format('YYYY-MM-DD HH:mm:ss') : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showRepairDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getRepairPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.repairId)
        // this.names = valObj.records.map(item => item.planName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          repairResult: undefined,
          startTime: '',
          endTime: '',
          // isProperty: '',
          pageNum: 1,
          projectId: null,
          sort: [],
        }
        this.planTimes = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showRepairDetails = true
        this.$nextTick(() => this.$refs.repairDetailsRef.showDetails(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.repairId ? [row?.repairId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteRepair({ repairIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
