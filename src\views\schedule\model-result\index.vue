<template>
    <div class="common-table-page" style="padding: 20px; background-color: #FFFFFF; position: relative;">
        <div style="position: relative;  height: 4%; width: 100%;  ">
            <div
                style="float: left; font-size: 20px;font-family: PingFang SC; font-weight: 500; line-height: 32px; color: #1D2129; ">
                调度仿真过程 </div>
            <a-select v-model="modelCode" allowClear
                style="float:right; width: 30%; height: 32px; font-size: 14px;font-family: PingFang SC; "
                placeholder="请选择" :options="modelOptions" show-search @change="handleChange"></a-select>
        </div>
        <div style="height: 96%; width: 100%; position: relative;">
            <a-tabs v-model="activeKey" @change="changeTabs"
                style="height: 100%; width: 100%; letter-spacing: 0; font-size: 16px; font-weight: 600; font-family: PingFang SC; overflow: auto;">
                <a-tab-pane key="1" tab="田间排水过程" style="height: 100%; width: 100%;">
                    <a-spin :spinning="loading">
                        <PaiShui :modelCode="modelCode" :modelScene="modelScene" @changeLoading="changeLoading">
                        </PaiShui>
                    </a-spin>
                </a-tab-pane>

                <a-tab-pane key="2" tab="河道水动力过程" style="height: 30px;">
                    <a-spin :spinning="loading">
                        <ShuiDongLi :modelCode="modelCode" @changeLoading="changeLoading"></ShuiDongLi>
                    </a-spin>
                </a-tab-pane>
            </a-tabs>

        </div>



    </div>
</template>

<script lang="jsx">
import { getModelIdList } from './services'
import PaiShui from "./PaiShui"
import ShuiDongLi from "./ShuiDongLi"

export default {
    name: 'Drainage',
    components: { PaiShui, ShuiDongLi },
    data() {
        return {
            activeKey: '2', // 选中的选项卡
            modelCode: "", // 选中的模型名称
            modelOptions: [], // 模型名称选项
            loading: false,
            times: [],
            modelScene: 1
        }
    },
    watch: {
        '$route.query.modelScene': {
            handler(newVal) {
                this.modelScene = newVal
            },
            immediate: true
        }
    },
    async created() {
        await getModelIdList({
            pageNum: 1,
            pageSize: 1000
        }).then(res => {
            if (res.data && res.data.data && res.data.data.length > 0) {
                this.modelOptions = res.data.data.map(el => {
                    return {
                        label: el.schedulingName,  //方案名称
                        value: el.modelId + "", // 方案编码
                        scene: el.scene, // 场景
                    }
                })
                this.modelCode = (this.$route.query.modelId ? this.$route.query.modelId + "" : "") || this.modelOptions[0].value
            }
        })

    },
    methods: {
        changeLoading(val) {
            this.loading = val
        },
        changeTabs() { },
        handleChange() {
            this.modelScene = this.modelOptions.find(el => el.value == this.modelCode).scene
        }
    },
}

</script>
<style lang="less" scoped>
::v-deep(.vxe-table .vxe-header--column.col-blue) {
    //   background-color: #2db7f5;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 13px;
    color: #1D2129;
}

::v-deep(.vxe-table .vxe-header--column.row-green) {
    //   background-color: #2db7f5;
    font-family: PingFang SC;
    font-size: 14px;
    color: #1D2129;
}
</style>
