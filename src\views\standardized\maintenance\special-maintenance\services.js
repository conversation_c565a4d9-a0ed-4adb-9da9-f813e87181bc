import request from '@/utils/request'

// 工程检查_安全检查-列表分页查询
export function getSpecialPage(data) {
  return request({
    url: '/prjstd/special/page',
    method: 'post',
    data,
  })
}
// 增加
export function addSpecial(data) {
  return request({
    url: '/prjstd/special/add',
    method: 'post',
    data,
  })
}
// 详情
export function getSpecialById(params) {
  return request({
    url: '/prjstd/special/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editSpecial(data) {
  return request({
    url: '/prjstd/special/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteSpecial(params) {
  return request({
    url: '/prjstd/special/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 审核 /prjstd/special/check
export function checkSpecial(data) {
  return request({
    url: '/prjstd/special/check',
    method: 'post',
    data,
  })
}
