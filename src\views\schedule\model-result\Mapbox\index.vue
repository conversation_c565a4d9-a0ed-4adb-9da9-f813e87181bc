<template>
  <div :id="mapBoxId" style="height: 100%; width: 100%;"></div>
</template>

<script lang="jsx">
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import axios from 'axios'
import { mapBoundGeo } from '@/utils/mapBounds.js'
// import MapboxLanguage from '@mapbox/mapbox-gl-language'
import { getValueByKey, getOptions } from '@/api/common'
import { Scene } from '@antv/l7';
export default {
  name: 'MapBox',
  mapIns: null,
  l7Scene: null,
  props: {
    mapBoxId: {
      type: String,
      default: 'mapBox',
      required: true,
    }
  },
  components: {},
  data() {
    return {
      center: null,
    }
  },
  mounted() {
    getValueByKey('patrolCentralPoint').then(res => {
      this.center = res.data.split(',').map(el => +el)
      this.$nextTick(() => {
        this.createMap(this.mapBoxId)
      })
    })
  },
  methods: {
    createMap(mapNode) {
      mapboxgl.accessToken =
        // 'pk.eyJ1IjoiaGhjY2RldiIsImEiOiJjbTBxaDBhdmswYzZjMmpwdzE4eWU2d2NvIn0._XkHfjxUcOLIZ7bIJUcbWw'
        "pk.eyJ1IjoiZ2lzZXJqaWFuZyIsImEiOiJjbHJ3Ym1nZzYwdTI2Mml1b3FzN3RlZmRrIn0.Sy_jGFu4prk99Udfa6AgkQ"
      this.mapIns = new mapboxgl.Map({
        container: mapNode,
        style: {
          version: 8,
          //  glyphs: "mapbox://fonts/mapbox/{fontstack}/{range}.pbf",
          glyphs: "https://www.sthzxgq.cn:11119/sthgq/font/{fontstack}/{range}.pbf",
          sprite: window.location.origin + "/icons/sprite/sprite",
          sources: {
            "wmts-tianditu-img-source": {
              type: 'raster',
              tiles: [
                `${process.env.VUE_APP_TIANDI_BASE}/img_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
              ],
              tileSize: 256,
            },
            "wmts-tianditu-cva-source": {
              type: 'raster',
              tiles: [
                `${process.env.VUE_APP_TIANDI_BASE}/cia_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
              ],
              tileSize: 256,
            }
          },
          layers: [
            {
              id: 'wmts-tianditu-img-layer',
              type: 'raster',
              slot: 'bottom',
              source: "wmts-tianditu-img-source"
            },
            {
              id: 'wmts-tianditu-cva-layer',
              type: 'raster',
              slot: 'bottom',
              source: "wmts-tianditu-cva-source"
            },
          ]
        },
        center: this.center,
        zoom: 10,
        maxZoom: 17.49, // 天地图大于这个值时，图层会消失
        // minZoom: 4,
        pitch: 0, // 相对于地面3D视角的角度
        antialias: false, //抗锯齿，通过false关闭提升性能
        // maxBounds: [
        //   [73.66, 3.86],
        //   [135.05, 53.55],
        // ],
        ...this.$attrs?.options,
      })

      // ### 标签汉化 //style已经设置中文
      // this.mapIns.addControl(new MapboxLanguage({ defaultLanguage: 'zh-Hans' }))
      // ### 添加导航控制条
      this.mapIns.addControl(new mapboxgl.NavigationControl({ showCompass: false }), 'top-left')
      // 添加全屏控件
      this.mapIns.addControl(new mapboxgl.FullscreenControl(), 'top-left');

      // 鼠标样式
      this.mapIns.getCanvas().style.cursor = 'auto'

      this.mapIns.on('style.load', async () => {
        await this.drawIrr(this.mapIns)
        await this.$emit('onMapMounted', this.mapIns);
      })

      // this.mapIns.on('style.load', () => {
      //   this.$listeners?.onMapStyleLoad && this.$listeners?.onMapStyleLoad(this.mapIns)
      // })


    },
    async drawIrr(mapIns) {
      // 蒙版
      await axios(
        "https://www.sthzxgq.cn:11120/geoserver/district/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=district:zhejiang&filter=<PropertyIsEqualTo><PropertyName>code</PropertyName><Literal>330481</Literal></PropertyIsEqualTo>"
      ).then(async res => {
        //灌区面
        await getValueByKey('gis.irr.area').then(async res => {
          await axios(res.data).then(resp => {
            mapIns.addLayer({
              id: 'gq-area',
              type: 'fill',
              slot: 'middle',
              source: {
                type: 'geojson',
                data: resp.data,
              },
              paint: {
                'fill-color': 'rgba(2, 237, 164, 0.05)',
                // 'fill-outline-color': '#03FFCD', // 边框颜色（必填）边框宽度固定为1像素，无法调整
              },
              layout: { visibility: 'visible' },
            })
          })
        })
        // await sleep(200)
        mapBoundGeo(res.data, this.mapIns, { top: 200, bottom: 200, left: 200, right: 200 })
        //蒙版边界
        mapIns.addLayer({
          id: 'mb-line',
          type: 'line',
          source: {
            type: 'geojson',
            data: res.data, //区划的面数据
          },
          paint: {
            'line-color': '#458fad', //'rgba(9, 236, 255, 1)',
            'line-width': 4,
            'line-dasharray': [0.2, 0.2],
          },
          layout: {
            visibility: 'visible',
          },
        })
        // 蒙版图层   //通过边界数据反选 达到挖洞效果
        mapIns.addLayer({
          id: 'mb-tag',
          type: 'fill',
          source: {
            type: 'geojson',
            data: {
              type: 'Feature',
              geometry: {
                type: 'Polygon',
                coordinates: [
                  [
                    [-180, 90],
                    [180, 90],
                    [180, -90],
                    [-180, -90],
                  ],
                  res.data.features[0].geometry.coordinates[0][0],
                ],
              },
            },
          },
          paint: { 'fill-color': 'rgba(0,0,0,0.4)' },
          layout: { visibility: 'visible' },
        })
      })
      // 灌区边界
      await getValueByKey('gis.irr.boundary').then(res => {
        axios(res.data).then(resp => {
          mapIns.addLayer({
            id: 'gq-area-line',
            type: 'line',
            source: {
              type: 'geojson',
              data: resp.data, //区划的面数据
            },
            paint: {
              'line-color': '#bc7c2b',
              'line-width': 2,
            },
          })
        })
      })
    }
  },
}
</script>
<style lang="less" scoped>
#mapBox {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
