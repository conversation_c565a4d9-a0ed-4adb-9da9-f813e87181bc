<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工程名称" prop="projectId">
              <!-- <a-select
                mode="multiple"
                :default-value="[]"
                style="width: 100%"
                placeholder="请选择工程名称"
                v-model="form.projectId"
              >
                <a-select-option v-for="item in propertyInProject" :key="item.projectId">
                  {{ item.projectName }}
                </a-select-option>
              </a-select> -->
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工程部位" prop="part">
              <a-input v-model="form.part" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="维养类型" prop="curingType">
              <a-select show-search placeholder="请输入" v-model="form.curingType" option-filter-prop="children">
                <a-select-option v-for="item in curingTypeOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <!-- <a-input v-model="form.propertyId" placeholder="请输入" allow-clear /> ,'propertyList' -->
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="维护开始时间" prop="startTime">
              <a-date-picker v-model="form.startTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="维护结束时间" prop="endTime">
              <a-date-picker v-model="form.endTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" v-if="isEdit">
            <a-form-model-item label="维护单位">
              <a-input style="width: 100%" v-model="form.deptName" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" v-if="form.isAccept == 1 && isEdit">
            <a-form-model-item label="验收人">
              <a-input style="width: 100%" v-model="form.acceptUserName" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" v-if="form.isAccept == 1 && isEdit">
            <a-form-model-item label="验收时间">
              <a-input style="width: 100%" v-model="form.acceptDate" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="维修养护内容" prop="content">
              <a-textarea style="width: 100%" v-model="form.content" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="维护情况记录" prop="record">
              <a-textarea style="width: 100%" v-model="form.record" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <!--  -->

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">相关附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.positionAttaches"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">图片</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.phoneAttaches"
                :multiple="true"
                listType="picture-card"
                folderName="safety-manage"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addPlans, editPlans, getPlansById, getProjectListByPropertyId } from '../services'
  import { getOptions, getSysUserPage } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'
  import { validEmail } from '@/utils/validate'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'curingTypeOptions', 'chargeOptions'],
    data() {
      return {
        isEdit: false,
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭

        formTitle: '',
        loading: false,
        modalLoading: false,
        form: {
          content: '',
          curingType: '',
          endTime: '',
          isProperty: '',
          part: '',
          phoneAttaches: [],
          positionAttaches: [],
          projectId: null,
          record: '',
          startTime: '',
          acceptUserName: '',
          acceptDate: '',
          deptName: '',
        },
        open: false,
        propertyInProject: [],
        rules: {
          projectId: [{ required: true, message: '工程名称不能为空', trigger: 'change' }],

          part: [{ required: true, message: '工程部位不能为空', trigger: 'blur' }],
          curingType: [{ required: true, message: '维养类型不能为空', trigger: 'change' }],

          startTime: [{ required: true, message: '维护开始时间不能为空', trigger: 'change' }],
          endTime: [{ required: true, message: '维护结束时间不能为空', trigger: 'change' }],

          content: [{ required: true, message: '维修养护内容不能为空', trigger: 'blur' }],
          record: [{ required: true, message: '维护情况记录不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          this.isEdit = true

          getPlansById({
            curingId: row.curingId,
          }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                phoneAttaches: res.data.phoneAttaches?.map(el => el.attachUrl),
                positionAttaches: res.data.positionAttaches?.map(el => el.attachUrl),
              }

              // this.$nextTick(() => {
              //   setTimeout(() => {
              //     this.form.acceptUserName = this.chargeOptions.find(el => el.userId == row.acceptUserId)?.name
              //   }, 700)
              // })
              // setTimeout(() => {
              //   this.form.acceptUserName = this.chargeOptions.find(el => el.userId == row.acceptUserId)?.name
              // }, 700)
              this.form.deptName = row.deptName
              this.form.acceptDate = row.acceptDate
              this.form.acceptUserName = this.chargeOptions.find(el => el.userId == row.acceptUserId)?.name
              // this.form.propertyId = this.propertyList.find(el => el.propertyId == this.form.propertyId)?.propertyId
              // propertyId: this.form.propertyId
              // this.form.year = moment(`${res.data.year}-01-01`)
              // getProjectListByPropertyId({ propertyId: 1 }).then(res => {
              //   this.propertyInProject = res?.data
              // })
            }
            this.modalLoading = false
          })
        }
      },
      //

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.startTime = moment(this.form.startTime).format('YYYY-MM-DD')
            this.form.endTime = moment(this.form.endTime).format('YYYY-MM-DD')
            this.form.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
            if (this.form.curingId == null) {
              addPlans(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editPlans(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
