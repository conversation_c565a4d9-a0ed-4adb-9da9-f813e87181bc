<template>
  <div>
    <!-- modalWidth="1200"
      modalHeight="800" -->
    <ant-modal
      :visible="open"
      :modal-title="modalTitle"
      :loading="modalLoading"
      :modalWidth="windowSize.width"
      :modalHeight="windowSize.height"
      @cancel="cancel"
    >
      <div slot="content">
        <div class="table-panel" layout="vertical">
          <!-- 左侧树 -->
          <div class="left-panel">
            <div class="title">基本信息</div>
            <div class="model">
              <a-form-model :label-col="labelCol" ref="form" :model="form" :rules="rules" :wrapper-col="wrapperCol">
                <a-form-model-item label="任务类型" class="formItemName">
                  <div style="line-height: 28px; padding-left: 15px">
                    {{ form.isTemp == 0 ? '计划任务' : '临时任务' }}
                  </div>

                  <!-- <a-select v-model="form.isTemp" allowClear class="formItemValue" disabled>
                    <a-select-option
                      v-for="(d, index) in isTempOptions"
                      :key="index"
                      :value="d.key"
                      :disabled="d.key == 0"
                    >
                      {{ d.value }}
                    </a-select-option>
                  </a-select> -->
                </a-form-model-item>
                <a-form-model-item label="任务名称" prop="taskName" class="formItem">
                  <a-input
                    allowClear
                    v-model="form.taskName"
                    :disabled="isInfo"
                    placeholder="请输入"
                    class="formItemValue"
                  />
                </a-form-model-item>
                <a-form-model-item label="巡检线路" prop="lineId" class="formItem">
                  <a-select
                    v-model="form.lineId"
                    allowClear
                    class="formItemValue"
                    :disabled="isInfo"
                    style="width: 100%; margin-left: 16px"
                    @change="handleChange"
                  >
                    <a-select-option v-for="(d, index) in lineOptions" :key="index" :value="d.lineId">
                      {{ d.lineName }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
                <a-form-model-item label="巡检班次" prop="shiftId" class="formItem">
                  <a-select
                    v-model="form.shiftId"
                    allowClear
                    placeholder="请选择"
                    :disabled="isInfo"
                    class="formItemValue"
                    @change="changeShift"
                  >
                    <a-select-option v-for="(d, index) in workShiftList" :key="index" :value="d.shiftId">
                      {{ d.shiftName }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
                <a-form-model-item label="巡检班组" prop="groupId" class="formItem">
                  <a-select
                    v-model="form.groupId"
                    allowClear
                    placeholder="请选择"
                    class="formItemValue"
                    :disabled="isInfo || !form.shiftId"
                    @change="changeUser"
                  >
                    <a-select-option v-for="(d, index) in workGroupList" :key="index" :value="d.groupId">
                      {{ d.groupName }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
                <a-form-model-item label="巡检人" prop="patrolUserId" class="formItem">
                  <a-select
                    v-model="form.patrolUserId"
                    allowClear
                    placeholder="请选择"
                    :disabled="isInfo || !form.groupId"
                    class="formItemValue"
                  >
                    <a-select-option v-for="(d, index) in workGroupUserList" :key="index" :value="d.userId">
                      {{ d.name }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
                <a-form-model-item label="开始时间" prop="planStartTime" class="formItem">
                  <a-date-picker
                    v-model="form.planStartTime"
                    :show-time="{ format: 'HH:mm' }"
                    valueFormat="YYYY-MM-DD HH:mm"
                    placeholder="请选择"
                    class="formItemValue"
                    :disabled="isInfo"
                  />
                </a-form-model-item>
                <a-form-model-item label="结束时间" prop="planEndTime" class="formItem">
                  <a-date-picker
                    v-model="form.planEndTime"
                    :show-time="{ format: 'HH:mm' }"
                    valueFormat="YYYY-MM-DD HH:mm"
                    placeholder="请选择"
                    class="formItemValue"
                    :disabled="isInfo"
                  />
                </a-form-model-item>
              </a-form-model>
            </div>
            <!-- 未选设施 -->
            <div
              style="
                position: absolute;
                top: 376px;
                height: 40px;
                width: 340px;
                background-color: #1890ff;
                color: #f2f2f2;
                padding: 10px;
                line-height: 20px;
                border-radius: 2px 2px 0 0;
              "
            >
              未选设施
            </div>
            <div class="table-box">
              <a-table
                :columns="leftColumns"
                :pagination="false"
                rowKey="objectId"
                style="height: 300px; background-color: #f0f0f0"
                :data-source="unselectedList"
              >
                <span slot="action" slot-scope="text, record">
                  <a class="arrow-right">
                    <a-icon v-if="!isInfo" type="arrow-right" @click="selectChange(text, record)" />
                  </a>
                </span>
              </a-table>
            </div>
          </div>

          <!-- 筛选栏 -->
          <div class="right-table-panel">
            <!-- 统计数 -->
            <div class="project-statistics">
              <div class="project-data">
                <p class="project-title">巡检对象数</p>
                <p class="project-num">{{ objectCount }}个</p>
              </div>
              <div class="project-data">
                <p class="project-title">设备数</p>
                <p class="project-num">{{ deviceCount }}个</p>
              </div>
              <div class="project-data">
                <p class="project-title">巡检项数</p>
                <p class="project-num">{{ itemCount }}个</p>
              </div>
            </div>
            <!-- 对象折叠 -->
            <div class="project-table">
              <a-collapse
                v-model="activeKey"
                style="height: 610px; width: 100%; overflow-y: auto; overflow-x: hidden; background-color: #f0f0f0"
                @change="switchObject"
                :expand-icon-position="expandIconPosition"
              >
                <a-collapse-panel v-for="(item, index) in selectObjectItems" :key="String(index)">
                  <template slot="header">
                    <span
                      style="
                        position: absolute;
                        height: 20px;
                        width: 20px;
                        text-align: center;
                        background-color: #1890ff;
                        color: #f2f2f2;
                      "
                    >
                      {{ index + 1 }}
                    </span>
                    <span
                      style="
                        margin-left: 30px;
                        display: inline-block;
                        width: 188px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                      "
                      :title="item.objectName"
                    >
                      {{ item.objectName }}
                    </span>
                    <span style="margin-left: 120px">
                      巡检项：总数{{ item.totalCount }}个 已选{{ item.checkCount }}个 未选{{ item.unCheckCount }}个
                    </span>
                    <a-icon
                      type="delete"
                      style="float: right; color: red"
                      v-if="!isInfo"
                      @click="delPartolObject(item)"
                    />
                  </template>
                  <!-- ${item.objectId}# -->
                  <a-table
                    :columns="tableColumns"
                    :rowKey="itemTb => `${item.objectId}#${itemTb.objectId}#${itemTb.itemId}`"
                    :data-source="item.childObject"
                    :pagination="false"
                    :row-selection="{
                      getCheckboxProps: record => ({
                        props: {
                          disabled: isInfo,
                        },
                      }),
                      selectedRowKeys: selectedRowKeys,
                      onChange: onSelectChange,
                    }"
                  ></a-table>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </div>
        </div>
      </div>

      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="confirm" v-if="!isInfo" :loading="loading">确定</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import {
    getPatrolObjectList,
    addPatrolTask,
    updatePatrolTask,
    getPatrolTask,
    chooseObjectPatrolLine,
    getWorkGroupUserList,
    getConfigPatrolLine,
    getWorkGroupList,
  } from '../services'
  import draggable from 'vuedraggable'
  import cloneDeep from 'lodash.clonedeep'
  import { getOptions } from '@/api/common'

  import difference from 'lodash/difference'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import TreeGeneral from '@/components/TreeGeneral'

  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'TreeTransfer',
    components: {
      VxeTable,
      VxeTableForm,
    },
    props: ['lineOptions', 'workShiftList'],
    components: {
      // ATransfer,
      TreeGeneral,
      AntModal,
      AdvanceTable,
      draggable,
    },
    data() {
      return {
        loading: false,
        modalLoading: false,
        list: [],
        configList: [],
        objectCount: 0,
        deviceCount: 0,
        itemCount: 0,
        isOperation: 0,
        selectObjectItems: [],
        windowSize: {
          width: `${parseInt(window.innerWidth * 0.95)}`,
          height: `${parseInt(window.innerHeight * 0.96)}`,
        },
        isTempOptions: [
          { key: 0, value: '计划任务' },
          { key: 1, value: '临时任务' },
        ],
        objectTypeOptions: [
          //巡检对象类型(1-水利工程 2-监测站点 3-江河湖泊 9-工程设备 )
          { key: 1, value: '水利工程' },
          { key: 2, value: '监测站点' },
          { key: 3, value: '江河湖泊' },
          { key: 9, value: '工程设备' },
        ],
        objectTypes: {},
        workGroupList: [],
        workGroupUserList: [],
        isInfo: false,
        checkedList: [],

        labelCol: { span: 4 },
        wrapperCol: { span: 16 },
        expandIconPosition: 'right',
        activeKey: [],
        selectedRowKeys: [], // Check here to configure the default

        dataSource: [],
        targetKeys: [],
        disabled: false,
        showSearch: true,

        propsTargetKeys: [],
        treeTabKey: '1',

        open: false,
        modalTitle: '',
        rowInfo: {},
        isShowTableModal: false,
        dragging: false,

        reqType: 'add',
        isActive: false,
        selectedRows: [],

        ids: [],
        single: true,
        multiple: true,
        list: [],
        taskType: '临时任务',
        form: {
          groupId: null,
          lineId: null,
          patrolType: this.$route.meta.query.patrolType,
          patrolUserId: null,
          planEndTime: null,
          planStartTime: null,
          shiftId: null,
          taskId: null,
          taskName: '',
          is_incomplete: 0,
          taskObjectItems: [
            {
              itemIds: [],
              objectId: null,
            },
          ],
        },
        orgList: [],
        selectIds: [],
        unselectedList: [],
        leftColumns: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            align: 'center',
            width: '50px',
          },
          {
            title: '名称',
            dataIndex: 'objectName',
            align: 'left',
            ellipsis: true,
          },
          {
            title: '操作',
            key: 'action',
            width: '60px',
            scopedSlots: { customRender: 'action' },
          },
        ],
        tableColumns: [
          { title: '', type: 'checkbox', width: 30, align: 'center' },
          {
            title: '对象类型',
            dataIndex: 'objectType',
            scopedSlots: { customRender: 'objectType' },
            key: 'key',
            customRender: (_, record, i) => {
              return this.objectTypes[record.objectType]?.value || ''
            },
          },
          { title: '对象编码', dataIndex: 'objectCode' },
          { title: '对象名称', dataIndex: 'objectName' },
          { title: '巡检项', dataIndex: 'itemName' },
          { title: '所在位置', dataIndex: 'address' },
        ],
        rules: {
          taskName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
          lineId: [
            {
              required: true,
              message: '巡检线路不能为空',
              trigger: ['blur', 'change'],
            },
          ],
          shiftId: [
            {
              required: true,
              message: '巡检班次不能为空',
              trigger: ['blur', 'change'],
            },
          ],
          groupId: [
            {
              required: true,
              message: '巡检班组不能为空',
              trigger: ['blur', 'change'],
            },
          ],
          patrolUserId: [
            {
              required: true,
              message: '巡检人不能为空',
              trigger: ['blur', 'change'],
            },
          ],
          planStartTime: [
            {
              required: true,
              message: '开始时间不能为空',
              trigger: ['blur', 'change'],
            },
          ],
          planEndTime: [
            {
              required: true,
              message: '结束时间不能为空',
              trigger: ['blur', 'change'],
            },
          ],
        },
      }
    },
    computed: {},
    watch: {
      'form.shiftId': {
        immediate: true,
        handler: function (newVal, oldVal) {
          this.form.shiftId = newVal
          this.getData()
        },
      },
    },
    created() {
      this.init()
    },
    mounted() {},
    methods: {
      init() {
        this.objectTypes = getFlatTreeMap(this.objectTypeOptions, 'key')
        let objParam = {
          lineId: null,
          objectCategoryId: null,
          objectIds: [],
          objectName: '',
          objectType: null,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          patrolType: this.$route.meta.query.patrolType,
          sort: [],
          taskId: null,
        }
        getPatrolObjectList(objParam).then(res => {
          this.orgList = res?.data?.data
          this.unselectedList = this.orgList.filter(item => !this.selectIds.includes(String(item.objectId)))
        })
      },
      /** 获取线路关联数据 **/
      handleChange() {
        this.selectObjectItems = []
        this.selectedRowKeys = []
        this.selectObjectItems = []
        getConfigPatrolLine({ lineId: this.form.lineId }).then(res => {
          let orgArr = res?.data
          this.configList = res?.data
          let r = 0
          let objectTmpId = ''
          let selectedObjArr = []
          if (orgArr.length > 0) {
            orgArr.forEach((el, idx) => {
              objectTmpId += el.objectId + ','
              el.childObject.forEach((ele, ibjIndex) => {
                ele.patrolItemCheckeds?.forEach((item, itemIndex) => {
                  selectedObjArr.push(`${el.objectId}#${ele.objectId}#${item.itemId}`)
                })
              })
            })
            objectTmpId = objectTmpId.slice(0, -1)
            this.selectIds = objectTmpId ? objectTmpId.split(',') : []
            this.unselectedList = this.orgList.filter(item => !this.selectIds.includes(String(item.objectId)))
            this.selectedRowKeys = selectedObjArr
            chooseObjectPatrolLine({ objectId: objectTmpId }).then(res => {
              let arr = res?.data || []
              let targetArr = []

              arr.forEach((el, idx) => {
                targetArr.push({ ...el, childObject: [] })
                el.childObject.forEach((ele, ibjIndex) => {
                  ele.patrolItemCheckeds?.forEach((item, itemIndex) => {
                    if (item?.itemName) {
                      targetArr[idx].childObject.push({
                        ...ele,
                        ...item,
                        patrolItemCheckeds: null,
                      })
                    }
                  })
                })
              })
              let selectArr = []
              targetArr.forEach((el, index) => {
                let targetArr = []
                let totalCount = 0
                let unCheckCount = 0
                let checkCount = 0
                this.selectedRowKeys.forEach(el => {
                  let id = el.split('#')[0]
                  let itemId = el.split('#')[2]
                  if (targetArr.some(ele => ele.objectId == id)) {
                    const idx = targetArr.findIndex(item => item.objectId == id)
                    targetArr[idx].itemIds.push(itemId)
                  } else {
                    targetArr.push({ objectId: id, itemIds: [itemId] })
                  }
                })
                targetArr.forEach((countEl, index) => {
                  if (el.objectId == countEl.objectId) {
                    totalCount = el.childObject ? el.childObject.length : 0
                    checkCount = countEl.itemIds ? countEl.itemIds.length : 0
                    unCheckCount = el.childObject.length - checkCount
                  }
                })
                selectArr.push({
                  ...el,
                  totalCount: totalCount,
                  checkCount: checkCount,
                  unCheckCount: unCheckCount,
                })
              })
              this.selectObjectItems = this.selectObjectItems.concat(selectArr)
            })
            // this.getCount(this.selectedRowKeys, this.selectObjectItems)
          } else {
            this.selectIds = []
            this.unselectedList = this.orgList
          }

          this.getCount(this.selectedRowKeys, this.selectObjectItems)
        })
      },
      getData() {
        getWorkGroupList({ shiftId: this.form.shiftId }).then(res => {
          this.workGroupList = res?.data[0]?.groups
        })
      },
      changeShift() {
        // this.form.groupId = ''
        this.form.groupId = this.workGroupList[0]?.groupId
        this.form.patrolUserId = ''
      },
      changeUser() {
        this.form.patrolUserId = ''
        this.selectUserList()
      },
      selectUserList() {
        this.workGroupUserList = []
        let paramWorkGroupUser = {
          deptId: null,
          groupId: this.form.groupId,
          name: '',
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          postId: null,
          shiftId: this.form.shiftId,
          sort: [],
        }
        getWorkGroupUserList(paramWorkGroupUser).then(res => {
          this.workGroupUserList = res?.data?.data
        })
      },
      selectChange(text, record) {
        // 使用filter()方法过滤掉objectId为id的数据
        var newList = this.unselectedList.filter(function (obj) {
          return obj.objectId != text.objectId
        })
        // 更新objList数组
        this.unselectedList = newList
        this.configList = []
        chooseObjectPatrolLine({ objectId: text.objectId }).then(res => {
          let arr = res?.data || []
          let targetArr = []
          let selectedOrgArr = []
          arr.forEach((el, idx) => {
            targetArr.push({ ...el, childObject: [] })
            el.childObject.forEach((ele, ibjIndex) => {
              ele.patrolItemCheckeds?.forEach((item, itemIndex) => {
                this.selectIds.push(String(el.objectId))

                if (item?.itemName) {
                  selectedOrgArr.push(`${el.objectId}#${ele.objectId}#${item.itemId}`)
                  targetArr[idx].childObject.push({
                    ...ele,
                    ...item,
                    patrolItemCheckeds: null,
                  })
                }
              })
            })
          })
          this.selectIds = [...new Set(this.selectIds)]
          this.selectedRowKeys = this.selectedRowKeys.concat(selectedOrgArr)

          let selectArr = []
          targetArr.forEach((el, index) => {
            let targetArr = []
            let totalCount = 0
            let unCheckCount = 0
            let checkCount = 0

            this.selectedRowKeys.forEach(el => {
              let id = el.split('#')[0]
              let itemId = el.split('#')[2]
              if (targetArr.some(ele => ele.objectId == id)) {
                const idx = targetArr.findIndex(item => item.objectId == id)
                targetArr[idx].itemIds.push(itemId)
              } else {
                targetArr.push({ objectId: id, itemIds: [itemId] })
              }
            })
            targetArr.forEach((countEl, index) => {
              if (el.objectId == countEl.objectId) {
                totalCount = el.childObject ? el.childObject.length : 0
                checkCount = countEl.itemIds ? countEl.itemIds.length : 0
                unCheckCount = el.childObject.length - checkCount
              }
              if (el.childObject.length == 0) {
                totalCount = 0
                checkCount = 0
                unCheckCount = 0
              }
            })
            selectArr.push({
              ...el,
              totalCount: totalCount,
              checkCount: checkCount,
              unCheckCount: unCheckCount,
            })
          })
          this.selectObjectItems = this.selectObjectItems.concat(selectArr)
          this.getCount(this.selectedRowKeys, this.selectObjectItems)
        })
      },
      //折叠面板中对象下列表显示数据统计
      updateData(selectedRowKey, targetArr) {
        this.selectObjectItems = []
        let selectArr = []
        targetArr.forEach((el, index) => {
          let targetArr = []
          let totalCount = 0
          let unCheckCount = 0
          let checkCount = 0
          selectedRowKey.forEach(el => {
            let id = el.split('#')[0]
            let itemId = el.split('#')[2]
            if (targetArr.some(ele => ele.objectId == id)) {
              const idx = targetArr.findIndex(item => item.objectId == id)
              targetArr[idx].itemIds.push(itemId)
            } else {
              targetArr.push({ objectId: id, itemIds: [itemId] })
            }
          })
          targetArr.forEach((countEl, index) => {
            if (el.objectId == countEl.objectId) {
              totalCount = el.childObject.length
              checkCount = countEl.itemIds ? countEl.itemIds.length : 0
              unCheckCount = el.childObject.length - checkCount
            }

            if (el.childObject.length == 0) {
              totalCount = 0
              checkCount = 0
              unCheckCount = 0
            }
          })
          selectArr.push({
            ...el,
            totalCount: totalCount,
            checkCount: checkCount,
            unCheckCount: unCheckCount,
          })
        })
        this.selectObjectItems = this.selectObjectItems.concat(selectArr)
      },
      //折叠面板中对象下列表选中项
      onSelectChange(selectedRowKeys, selectedRows) {
        this.checkedList = selectedRowKeys
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        this.getCount(selectedRowKeys, this.selectObjectItems)
        this.updateData(this.selectedRowKeys, this.selectObjectItems)
      },
      getCount(newArr, objectItems) {
        newArr = newArr.filter(item => {
          return item.indexOf('__ob__: Observer') == -1
        })
        let newObjectArr = []
        let newDeviceArr = []
        for (let i = 0; i < newArr.length; i++) {
          let item = newArr[i]
          let splitItem = item.split('#')

          newObjectArr.push(splitItem[0])

          if (splitItem[0] == splitItem[1]) {
            newDeviceArr.push()
          } else {
            newDeviceArr.push(splitItem[0] + '#' + splitItem[1])
          }
        }
        newObjectArr = [...new Set(newObjectArr)]
        newDeviceArr = [...new Set(newDeviceArr)]
        this.objectCount =
          this.configList.length != 0
            ? this.configList.length
            : objectItems.length != 0
            ? objectItems.length
            : newObjectArr
            ? newObjectArr.length
            : 0
        this.deviceCount = newDeviceArr ? newDeviceArr.length : 0
        this.itemCount = newArr ? newArr.length : 0
      },
      selectObject() {},

      delPartolObject(item) {
        var that = this
        const taskIds = item.objectId
        const names = item.objectName
        that.configList = []
        this.$confirm({
          title: '确认删除该条数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            that.selectIds = that.selectIds.filter(item => item != String(taskIds))
            that.unselectedList = that.orgList.filter(
              selectItem => !that.selectIds.includes(String(selectItem.objectId)),
            )
            that.selectObjectItems = that.selectObjectItems.filter(obj => obj.objectId != item.objectId)
            that.selectedRowKeys = that.selectedRowKeys.filter(item => item.startsWith(String(taskIds)) === false)

            that.getCount(that.selectedRowKeys, that.selectObjectItems)
            that.$message.success(`成功删除该条数据`, 3)
          },
          onCancel() {},
        })
      },
      switchObject(e) {},
      handleExpandChange(activeKeys) {
        // this.activeKey.push('')
        // 处理折叠展示的逻辑
      },
      handleExpand(index) {
        // 处理列表项展开的逻辑
      },
      getList(type) {
        this.dataSource = []

        this.$forceUpdate()
      },
      handleSelectedChange(newVal, oldVal) {
        this.checkedList = newVal
      },
      async typeTargetKeys(Arr) {
        this.propsTargetKeys = Arr
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('on')
      },

      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.modalTitle = '新增'
        this.isInfo = false
        this.isOperation = 1
        this.form = {
          isTemp: 1,
          groupId: null,
          lineId: null,
          patrolType: this.$route.meta.query.patrolType,
          patrolUserId: null,
          planEndTime: null,
          planStartTime: null,
          shiftId: null,
          taskId: null,
          taskName: '',
          is_incomplete: 0,
          taskObjectItems: [
            {
              itemIds: [],
              objectId: null,
            },
          ],
        }
        this.form.shiftId = this.workShiftList[0].shiftId

        this.objectCount = 0
        this.deviceCount = 0
        this.itemCount = 0
        this.selectObjectItems = []
        this.selectedRowKeys = []
        this.$nextTick(() => {
          this.selectUserList()
          setTimeout(() => {
            this.form.groupId = this.workGroupList[0]?.groupId
          }, 300)
        })
      },
      //根据id获取数据详情
      getInfo(record) {
        this.selectObjectItems = []
        this.selectedRowKeys = []
        this.modalLoading = true
        getPatrolTask({ taskId: record.taskId }).then(res => {
          this.modalLoading = false
          this.form = res?.data
          this.configList = res?.data?.objects
          let objectTmpId = ''
          let selectedObjArr = []

          if (this.form.objects.length > 0) {
            this.form.objects.forEach((objItem, objIndex) => {
              objectTmpId += objItem.objectId + ','

              if (objItem.devices.length > 0) {
                objItem.devices.forEach((devicesItem, devicesIndex) => {
                  devicesItem.itemIds.forEach((item, index) => {
                    selectedObjArr.push(`${objItem.objectId}#${devicesItem.objectId}#${item}`)
                  })
                })
              }
              if (objItem.itemIds.length > 0) {
                objItem.itemIds.forEach((item, index) => {
                  selectedObjArr.push(`${objItem.objectId}#${objItem.objectId}#${item}`)
                })
              }
            })
            objectTmpId = objectTmpId.slice(0, -1)
            this.selectIds = objectTmpId ? objectTmpId.split(',') : []
            this.unselectedList = this.orgList.filter(item => !this.selectIds.includes(String(item.objectId)))

            this.selectedRowKeys = selectedObjArr
            this.form.planStartTime = moment(this.form.planStartTime, 'YYYY-MM-DD HH:mm')
            this.form.planEndTime = moment(this.form.planEndTime, 'YYYY-MM-DD HH:mm')
            chooseObjectPatrolLine({ objectId: objectTmpId }).then(res => {
              let arr = res?.data || []
              let targetArr = []
              arr.forEach((el, idx) => {
                targetArr.push({ ...el, childObject: [] })
                el.childObject.forEach((ele, objIndex) => {
                  ele.patrolItemCheckeds?.forEach((item, itemIndex) => {
                    if (item?.itemName) {
                      targetArr[idx].childObject.push({
                        ...ele,
                        ...item,
                        patrolItemCheckeds: null,
                      })
                    }
                  })
                })
              })
              let selectArr = []
              targetArr.forEach((el, index) => {
                let targetArr = []
                let totalCount = 0
                let unCheckCount = 0
                let checkCount = 0
                this.selectedRowKeys.forEach(el => {
                  let id = el.split('#')[0]
                  let itemId = el.split('#')[2]
                  if (targetArr.some(ele => ele.objectId == id)) {
                    const idx = targetArr.findIndex(item => item.objectId == id)
                    targetArr[idx].itemIds.push(itemId)
                  } else {
                    targetArr.push({ objectId: id, itemIds: [itemId] })
                  }
                })
                targetArr.forEach((countEl, index) => {
                  if (el.objectId == countEl.objectId) {
                    totalCount = el.childObject ? el.childObject.length : 0
                    checkCount = countEl.itemIds ? countEl.itemIds.length : 0
                    unCheckCount = el.childObject.length - checkCount
                  }
                })
                selectArr.push({
                  ...el,
                  totalCount: totalCount,
                  checkCount: checkCount,
                  unCheckCount: unCheckCount,
                })
              })
              this.selectObjectItems = this.selectObjectItems.concat(selectArr)
            })
            this.init()
            this.getCount(this.selectedRowKeys, this.selectObjectItems)
          } else {
            this.selectIds = []
            this.init()
            this.unselectedList = this.orgList
          }
        })
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.modalTitle = '修改'
        this.isOperation = 2
        this.isInfo = false
        this.selectUserList()
        this.getInfo(record)
      },
      handleDetail(record) {
        this.open = true
        this.modalTitle = '详情'
        this.isOperation = 3
        this.isInfo = true
        this.selectUserList()
        this.getInfo(record)
      },
      getObjCount(record) {},
      /** 提交按钮 */
      confirm() {
        // this.checkedList
        let checkTempArr = []
        let targetArr = []
        let result = {}
        let result2 = {}
        this.loading = true
        this.selectedRowKeys.forEach(item => {
          let [objectId, deviceId, itemId] = item.split('#')
          if (!result[objectId]) {
            result[objectId] = {
              objectId: objectId,
              itemIds: [],
            }
          }
          if (deviceId == objectId) {
            result[objectId].itemIds.push(parseInt(itemId))
          }

          if (!result2[deviceId]) {
            result2[deviceId] = {
              objectId: deviceId,
              itemIds: [],
            }
          }
          result2[deviceId].itemIds.push(parseInt(itemId))
        })
        let newList = Object.values(result)
        let newList2 = Object.values(result2)
        let mergedArray = newList.concat(newList2)
        let objectIds = {}
        mergedArray.forEach(item => {
          if (!objectIds[item.objectId]) {
            targetArr.push(item)
            objectIds[item.objectId] = item
          } else {
            let existingItem = objectIds[item.objectId]
            existingItem.itemIds = item.itemIds
          }
        })

        this.selectedRowKeys.forEach(el => {
          let id = el.split('#')[0]
          this.selectObjectItems = this.selectObjectItems.filter(ele => ele.objectId != id)
        })

        if (!this.form.taskName) {
          this.$message.warning('请选择巡检线路')
          return
        }
        if (!this.form.lineId) {
          this.$message.warning('请选择巡检线路')
          return
        }
        if (!this.form.shiftId) {
          this.$message.warning('请选择巡检班次')
          return
        }
        if (!this.form.groupId) {
          this.$message.warning('请选择巡检班组')
          return
        }
        if (!this.form.patrolUserId) {
          this.$message.warning('请选择巡检人')
          return
        }

        if (this.selectObjectItems) {
          this.selectObjectItems.forEach(item => {
            targetArr.push({ objectId: item.objectId, itemIds: [] })
          })
        }
        if (!this.form.planStartTime) {
          this.$message.info('请选择开始时间', 3)
          return
        } else {
          this.form.planStartTime = moment(this.form.planStartTime).format('YYYY-MM-DD HH:mm')
        }
        if (!this.form.planEndTime) {
          this.$message.info('请选择结束时间', 3)
          return
        } else {
          this.form.planEndTime = moment(this.form.planEndTime).format('YYYY-MM-DD HH:mm')
        }

        this.form.taskObjectItems = targetArr

        if (this.isOperation == 1) {
          addPatrolTask(this.form)
            .then(res => {
              this.$message.success('新增成功', 3)
              this.loading = false
              this.open = false
              this.$emit('ok')
              this.$emit('close')
              // this.onOperationComplete()
            })
            .catch(() => (this.loading = false))
        } else if (this.isOperation == 2) {
          updatePatrolTask(this.form)
            .then(res => {
              this.$message.success('修改成功', 3)
              this.open = false
              this.loading = false
              this.$emit('ok')
              this.$emit('close')
              // this.onOperationComplete()
            })
            .catch(() => (this.loading = false))
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';

  // 多余滚动条
  ::v-deep .advanced-table {
    overflow-y: visible;
  }

  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background-color: transparent;
  }

  ::v-deep .table-panel {
    display: flex;
    height: 820px;
    width: 100%;

    .left-panel {
      position: relative;
      width: 340px;
    }

    .right-table-panel {
      position: relative;
      width: 100%;
      background-color: #fff;
      height: 700px;

      .project-statistics {
        position: absolute;
        width: calc(100% - 10px);
        margin-left: 20px;
        display: flex;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;

        .project-data {
          flex: 1;
          width: 30%;
          background-color: #1890ff;
          color: #fff;
          height: 60px;
          margin-right: 30px;

          .project-title {
            float: left;
            margin-left: 15px;
            margin-top: 20px;
          }

          .project-num {
            float: right;
            margin-right: 15px;
            margin-top: 20px;
          }
        }
        .project-data:last-child {
          margin-right: 0; /* 设置最后一个box元素的margin-right为0 */
        }
      }

      .project-table {
        position: absolute;
        top: 100px;
        margin-left: 20px;
        width: calc(100% - 10px);
        height: 500px;
      }
    }
  }

  ::v-deep .ant-transfer-list-header {
    color: #fff;
    background: #1890ff;
  }

  ::v-deep .ant-modal-body {
    width: 100% !important;
    height: 740px !important;
    max-height: 740px !important;
    overflow: hidden !important;
  }

  ::v-deep .ant-transfer-list-body-customize-wrapper {
    height: 640px !important;
    overflow-y: hidden;
    overflow-x: hidden;
  }

  ::v-deep .ant-table-wrapper {
    overflow-y: auto;
    overflow-x: hidden;
  }
  ::v-deep .modal-content {
    height: 760px !important;
    width: 100% !important;
  }

  ::v-deep .ant-modal-content {
    height: 860px;
    width: 100%;
    overflow-x: hidden;
  }

  .title {
    background-color: @primary-color;
    border-radius: 2px 2px 0 0;
    line-height: 20px;
    color: #fff;
    padding: 10px;
    width: 340px;
  }
  .model {
    position: absolute;
    top: 40px;
    width: 340px;
    // height: 376px;
    height: 316px;
    background-color: #eee;
  }

  .table-box {
    position: absolute;
    top: 376px;
    width: 340px;
    height: 340px;
    background-color: #eee;

    overflow: hidden;
    position: relative;
    ::v-deep .ant-table-thead {
      position: sticky;
      top: 0px;
      z-index: 2;
    }
  }

  .formItemName {
    margin-left: 10px;
    margin-top: 6px;
  }
  .formItem {
    margin-left: 10px;
    margin-top: -16px;
  }

  ::v-deep .ant-col.ant-col-4.ant-form-item-label {
    width: 68px !important;
  }
  .formItemValue {
    width: 100%;
    margin-left: 16px;
  }
</style>
