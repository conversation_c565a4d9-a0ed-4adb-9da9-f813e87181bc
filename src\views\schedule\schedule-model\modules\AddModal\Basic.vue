<template>
  <a-form-model
    style="margin-top: 24px"
    ref="form"
    :model="form"
    :rules="rules"
    layout="horizontal"
    v-bind="{
      labelCol: { span: 6 },
      wrapperCol: { span: 8 },
    }"
  >
    <a-form-model-item label="方案名称" prop="schedulingName">
      <a-input v-model="form.schedulingName" placeholder="请输入" allow-clear :maxLength="128" />
    </a-form-model-item>
    <a-form-model-item label="模型应用场景" prop="scene">
      <a-radio-group
        v-model="form.scene"
        :options="sceneOptions.map(el => ({ ...el, label: `${el.label}场景` }))"
        style="margin-top: 5px"
        @change="changeScene"
      />
    </a-form-model-item>

    <a-form-model-item label="预演时间" prop="rangeDate">
      <div style="display: flex; align-items: center">
        <a-select
          style="width: 100px"
          v-if="form.scene === 2"
          v-model="dateType"
          :options="[
            { value: 1, label: '未来1天' },
            { value: 2, label: '未来3天' },
            { value: 3, label: '未来7天' },
            { value: 4, label: '自定义' },
          ]"
          @change="changeDateType"
        />

        <a-range-picker
          :disabled="form.scene === 2 && dateType !== 4"
          allow-clear
          :placeholder="['开始时间', '结束时间']"
          :format="['YYYY-MM-DD HH:00', 'YYYY-MM-DD HH:00']"
          style="width: 100%"
          v-model="form.rangeDate"
          :show-time="{ format: 'HH' }"
          :disabled-date="disabledRangeDate"
        />
      </div>
    </a-form-model-item>

    <a-form-model-item label="调度方式" prop="dispathType">
      <a-radio-group v-model="form.dispathType" :options="dispatchTypeOptions" style="margin-top: 5px" />
    </a-form-model-item>
    <a-form-model-item label="预演范围" prop="fcstRange">
      <a-radio-group v-model="form.fcstRange" :options="fcstRangeOptions" style="margin-top: 5px" />
    </a-form-model-item>
  </a-form-model>
</template>

<script>
  import moment from 'moment'
  import { dispatchTypeOptions } from '../../config.js'

  export default {
    name: 'BasicInfo',
    props: ['fcstRangeOptions', 'sceneOptions'],
    data() {
      return {
        dispatchTypeOptions,

        dateType: 2,
        form: {
          schedulingName: undefined,
          scene: this.sceneOptions[1].value,
          dispathType: dispatchTypeOptions[0].value,
          fcstRange: this.fcstRangeOptions[0].value,
          startTime: undefined,
          endTime: undefined,
          rangeDate: [],
        },
        rules: {
          schedulingName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          scene: [{ required: true, message: '模型应用场景不能为空', trigger: 'change' }],
          dispathType: [{ required: true, message: '调度类型不能为空', trigger: 'change' }],
          fcstRange: [{ required: true, message: '预报范围不能为空', trigger: 'change' }],
          rangeDate: [{ required: true, message: '预演时间不能为空', trigger: 'change,blur' }],
        },
      }
    },
    computed: {},
    watch: {
      // inWaterEchoData: {
      //   handler(val) {
      //     if (!!val) {
      //       this.form = {
      //         schedulingName: undefined,
      //         startTime: this.inWaterEchoData.startTime,
      //         endTime: this.inWaterEchoData.endTime,
      //         dispathType: this.inWaterEchoData.dispathType,
      //         fcstRange: this.inWaterEchoData.fcstRange,
      //       }
      //     }
      //   },
      // },
    },
    created() {},
    mounted() {
      this.changeScene({ target: { value: this.sceneOptions[1].value } })
    },
    methods: {
      changeDateType(val) {
        switch (val) {
          case 1:
            this.form.rangeDate = [moment(), moment().add(1, 'days')]
            break
          case 2:
            this.form.rangeDate = [moment(), moment().add(3, 'days')]
            break
          case 3:
            this.form.rangeDate = [moment(), moment().add(7, 'days')]
            break
          case 4:
            this.form.rangeDate = []
            break
        }
      },

      changeScene(val) {
        if (val.target.value === 2) {
          this.dateType = 2
          this.form.rangeDate = [moment(), moment().add(3, 'days')]
          this.$refs.form.validateField('rangeDate')
        } else {
          this.form.rangeDate = []
          this.$refs.form.validateField('rangeDate')
        }
      },

      disabledRangeDate(current) {
        if (this.form.scene === 1) {
          return current < moment().subtract(15, 'days') || current > moment()
        } else {
          return current < moment().subtract(1, 'days') || current > moment().add(15, 'days')
        }
      },

      save() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.$emit('saveData', {
              ...this.form,
              startTime: this.form.rangeDate[0].format('YYYY-MM-DD HH:00'),
              endTime: this.form.rangeDate[1].format('YYYY-MM-DD HH:00'),
              rangeDate: undefined,
            })
          } else {
            this.$emit('saveData', false)
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped></style>
