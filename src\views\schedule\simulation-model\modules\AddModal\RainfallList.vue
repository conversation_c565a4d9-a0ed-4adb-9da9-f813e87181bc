<template>
  <div style="flex: 1">
    <VxeTable
      v-if="columns.length > 0"
      ref="vxeTableRef"
      tableTitle="降雨过程"
      :isShowSetBtn="false"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :tablePage="false"
      :showFooter="true"
      :footerData="footerData"
      :footer-row-style="{ background: '#F8F8F9', fontWeight: 'bold' }"
      :scrollY="{ enabled: true, gt: 0 }"
      :scrollX="{ enabled: true, gt: 0 }"
    ></VxeTable>
  </div>
</template>

<script lang="jsx">
  import { getInWaterRange, getRainfallList } from '../../services'
  import VxeTable from '@/components/VxeTable/index.vue'

  export default {
    name: 'RainfallList',
    props: ['baseInfo'],
    components: { VxeTable },
    data() {
      return {
        loading: false,
        list: [],
        columns: [],
        footerData: [],
      }
    },
    computed: {},
    watch: {
      list: {
        handler(newVal) {
          let obj = {}
          Object.keys(newVal[0].sitesObj).forEach(el => {
            const total = newVal.reduce((pre, cur) => pre + cur.sitesObj[el], 0)
            if (typeof total === 'string') {
              obj[el] = ''
            } else {
              obj[el] = +total.toFixed(1)
            }
          })
          this.footerData = [obj]
        },
        deep: true,
      },
    },
    created() {
      getInWaterRange({ fcstRange: this.baseInfo.fcstRange }).then(res => {
        this.columns = [
          { title: '时间', field: 'tm', minWidth: 150, slots: { footer: ({ row }) => '总降雨量' } },
          ...res.data[0].sites.map(el => ({
            title: el.siteName + '(mm)',
            field: `sitesObj.${el.siteId}`,
            minWidth: 150,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <div class='cell-box'>
                    <a-input-number size='small' step={0.1} min={0} v-model={this.list[rowIndex].sitesObj[el.siteId]} />
                    <a
                      style='margin-left: 5px'
                      onClick={() =>
                        this.list.forEach((item, index) => {
                          if (index > rowIndex) {
                            item.sitesObj[el.siteId] = this.list[rowIndex].sitesObj[el.siteId]
                          }
                        })
                      }
                    >
                      向下填充
                    </a>
                  </div>
                )
              },
              footer: ({ row }) => {
                return this.footerData.reduce((pre, cur) => pre + cur[`${el.siteId}`], 0)
              },
            },
          })),
        ]

        getRainfallList(this.baseInfo).then(resp => {
          this.list = resp.data.map(el => {
            const obj = {}
            el.sites.forEach(ele => (obj[ele.siteId] = ele.rain))
            return { tm: el.tm, sitesObj: obj }
          })
        })
      })
    },
    methods: {
      save() {
        this.$emit(
          'saveData',
          this.list.map(el => {
            const arr = []
            Object.keys(el.sitesObj).forEach(key => {
              arr.push({ siteId: key, rain: el.sitesObj[key] })
            })
            return { tm: el.tm, sites: arr }
          }),
        )
      },
    },
  }
</script>

<style lang="less" scoped>
  .cell-box {
    a {
      display: none;
    }
    &:hover {
      a {
        display: inline;
      }
    }
  }
</style>
