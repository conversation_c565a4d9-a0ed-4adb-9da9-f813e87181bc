<template>
  <div class="common-table-page" :style="isDetail ? { height: '100%' } : {}">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="工程名称">
        <a-tree-select
        :disabled="isDetail"
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-item>

      <a-form-item label="维养类型">
        <a-select
          show-search
          allowClear
          placeholder="请选择"
          v-model="queryParam.curingType"
          option-filter-prop="children"
        >
          <a-select-option v-for="item in curingTypeOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="是否验收">
        <a-select
          show-search
          allowClear
          placeholder="请选择"
          v-model="queryParam.isAccept"
          option-filter-prop="children"
        >
          <a-select-option v-for="item in acceptOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :projectOptions="projectOptions"
          :curingTypeOptions="curingTypeOptions"
          :chargeOptions="chargeOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :projectOptions="projectOptions"
          :curingTypeOptions="curingTypeOptions"
          :chargeOptions="chargeOptions"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree, getSysUserPage } from '@/api/common'
  import { getPlansPage, deletePlans, checkPlans } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'DailyMaintenance',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        chargeOptions: [],
        isProperty: '',
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        isChecked: false,
        checkOptions: [
          { key: 0, value: '否' },
          { key: 1, value: '是' },
        ],
        acceptOptions: [
          { key: 0, value: '未验收' },
          { key: 1, value: '已验收' },
        ],
        curingTypeOptions: [], //项目类型
        planSourceOptions: [], //项目来源
        reportStatusOptions: [], //申报状态
        curingTypes: [],
        planSources: [],
        reportStatuses: [],

        propertyList: [],
        inspectionTypeOptions: [],
        inspectionTypes: [],
        inspectionStatuses: [],

        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        planTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '日常养护',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          curingType: undefined,
          isAccept: undefined,
          isProperty: '',
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            // slots: {
            //   default: ({ row, rowIndex }) => {
            //     return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
            //   },
            // },
          },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 120,
            showOverflow: true,
          },
          {
            title: '部位',
            field: 'part',
            minWidth: 80,
            showOverflow: true,
          },

          {
            title: '维修养护内容',
            field: 'content',
            minWidth: 120,
            showOverflow: true,
          },
          {
            title: '维护情况记录',
            field: 'record',
            minWidth: 120,
            showOverflow: true,
          },

          {
            title: '维养类型',
            field: 'curingType',
            minWidth: 70,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.curingTypes[row.curingType]?.value
              },
            },
          },

          {
            title: '维护开始日期',
            field: 'startTime',
            minWidth: 120,
          },
          {
            title: '维修结束日期',
            field: 'endTime',
            minWidth: 120,
          },
          {
            title: '维护人',
            field: 'createdUserName',
            minWidth: 80,
          },
          {
            title: '维护单位',
            field: 'deptName',
            minWidth: 100,
            showOverflow: true,
          },

          {
            title: '是否验收',
            field: 'isAccept',
            minWidth: 80,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.acceptOptions?.find(el => el.key == row.isAccept)?.value
              },
            },
          },

          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 190,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    {!this.isDetail && (<a-divider type='vertical' />)}
                    {!this.isDetail && (<a onClick={() => this.handleEdit(row)}>修改</a>)}

                    {!this.isDetail && (<a-divider type='vertical' />)}
                    {!this.isDetail && (<a onClick={() => this.handleDelete(row)}>删除</a>)}
                    
                    {!this.isDetail && row.isCheck === 1 && row.isAccept == 0 && (
                      <span>
                        <a-divider type='vertical' />
                        <a v-permission={`${this.$route.meta.menuId}-audit`} onClick={() => this.handleCheck(row)}>
                          验收
                        </a>
                      </span>
                    )}
                    {/* <a onClick={() => this.handleCheck(row)}>验收</a> */}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
      this.queryParam.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
      this.tableTitle = this.isProperty ? '绿化养护' : '日常养护'
      this.getList()

      getOptions('curingType').then(res => {
        this.curingTypeOptions = res.data
        this.curingTypes = getFlatTreeMap(this.curingTypeOptions, 'key')
      })

      getOptions('planSource').then(res => {
        this.planSourceOptions = res.data
        this.planSources = getFlatTreeMap(this.planSourceOptions, 'key')
      })

      getOptions('reportStatus').then(res => {
        this.reportStatusOptions = res.data
        this.reportStatuses = getFlatTreeMap(this.reportStatusOptions, 'key')
      })

      getSysUserPage({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        isDisabled: 0,
        deptId: null,
      }).then(response => {
        this.chargeOptions = response.data?.data
      })
      // getPropertyList({
      //   pageNum: 1,
      //   pageSize: Number.MAX_SAFE_INTEGER,
      //   projectId: null,
      //   propertyCharge: '',
      //   propertyName: '',
      // }).then(res => {
      //   this.propertyList = res?.data?.data
      // })

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })

      // this.inspectionStatuses = getFlatTreeMap(this.inspectionStatusOptions, 'key')
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      onRangeChange(value, planTimes) {
        this.planTimes = value
        if (planTimes.length == 0) {
          return
        }
        this.queryParam.startTime = planTimes[0] ? moment(planTimes[0]).format('YYYY-MM-DD') : undefined
        this.queryParam.endTime = planTimes[1] ? moment(planTimes[1]).format('YYYY-MM-DD') : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getPlansPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.curingId)
        // this.names = valObj.records.map(item => item.planName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          curingType: undefined,
          isAccept: undefined,
          // isProperty: '',
          pageNum: 1,
          projectId: null,
          sort: [],
        }
        this.planTimes = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.curingId ? [row?.curingId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deletePlans({ curingIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      //验收
      handleCheck(row) {
        var that = this
        this.$confirm({
          title: '确认验收所选中数据?',
          content: '请确认是否验收当前数据',
          onOk() {
            checkPlans({ curingId: row?.curingId }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功验收1条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
