<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="工器具名称">
        <a-input v-model="queryParam.toolName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="检查日期">
        <a-range-picker
          allow-clear
          :value="planTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>

      <a-form-model-item label="所属工程">
        <a-tree-select
          :disabled="isDetail"
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          :isInModal="isDetail"
          :modalHeight="modalHeight"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button>
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :userOptions="userOptions"
          :projectOptions="projectOptions"
          :checkStatusOptions="checkStatusOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :userOptions="userOptions"
          :projectOptions="projectOptions"
          :checkStatusOptions="checkStatusOptions"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree, getSysUserPage } from '@/api/common'
  import { getToolPage, deleteTool } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'Appliances',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        userOptions: [],
        checkStatusOptions: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,

        planTimes: [],

        isChecked: false,
        list: [],
        tableTitle: '安全工器具',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          toolName: null,
          projectId: this.projectId,
          startTime: '',
          endTime: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },

          {
            title: '工器具名称',
            minWidth: 120,
            field: 'toolName',
            showOverflow: true,
          },
          {
            title: '检查日期',
            minWidth: 100,
            field: 'checkDate',
          },
          {
            title: '检查人员',
            minWidth: 120,
            field: 'userNames',
            showOverflow: true,
          },
          {
            title: '检查结论',
            minWidth: 100,
            field: 'checkStatus',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.checkStatusOptions.find(el => +el.value === row.checkStatus)?.label
              },
            },
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 90,
            showOverflow: true,
          },
          {
            title: '创建人',
            minWidth: 100,
            field: 'createdUserName',
            showOverflow: true,
          },
          {
            title: '创建时间',
            minWidth: 170,
            field: 'createdTime',
            showOverflow: true,
          },
          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 150,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    {!this.isDetail && (
                      <span>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleEdit(row)}>修改</a>
                        <a-divider type='vertical' />
                        <a onClick={() => this.handleDelete(row)}>删除</a>
                      </span>
                    )}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
      getOptions('checkStatus').then(res => {
        this.checkStatusOptions = res.data.map(el => ({ label: el.value, value: +el.key }))
      })

      getSysUserPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.userOptions = res.data.data.map(el => ({ label: el.name, value: el.userId }))
      })

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      onRangeChange(value, planTimes) {
        this.planTimes = value
        if (planTimes.length == 0) {
          return
        }
        this.queryParam.startTime = planTimes[0] ? moment(planTimes[0]).format('YYYY-MM-DD') : undefined
        this.queryParam.endTime = planTimes[1] ? moment(planTimes[1]).format('YYYY-MM-DD') : undefined
      },
      /** 查询列表 */
      getList() {
        this.loading = true
        this.selectChange({ records: [] })
        getToolPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
        this.names = valObj.records.map(item => item.toolName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.planTimes = []
        this.queryParam = {
          ...this.queryParam,
          toolName: null,
          projectId: null,
          startTime: '',
          endTime: '',
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteTool({ ids: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
