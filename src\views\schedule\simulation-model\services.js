import request from '@/utils/request'

// 分页查询
export function getChSimPage(data) {
  return request({
    url: '/model/ch-sim/page',
    method: 'post',
    data,
  })
}

// 删除
export function deleteChSim(params) {
  return request({
    url: '/model/ch-sim/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 根据水利对象分类编码查询工程
export function getProjectByCategoryId(params) {
  return request({
    url: '/base/project/getProjectByCategoryId',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 配置-列表
export function getChSimRange(params) {
  return request({
    url: '/model/ch-sim/range/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 配置-设置
export function setChSimRange(data) {
  return request({
    url: '/model/ch-sim/range/set',
    method: 'post',
    data,
  })
}

// 获取工程流量过程
export function getChSimFlow(data) {
  return request({
    url: '/model/ch-sim/flow/list',
    method: 'post',
    data,
  })
}

// 执行预报
export function forecast(data) {
  return request({
    url: '/model/ch-sim/forecast',
    method: 'post',
    data,
  })
}

// 推演结果
export function getInferRes(params) {
  return request({
    url: '/model/ch-sim/getInferRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 推演仿真
export function getScaleRes(params) {
  return request({
    url: '/model/ch-sim/getScaleRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 推演概化图-部分水利设施仿真结果列表
export function getScaleResDetails(params) {
  return request({
    url: '/model/ch-sim/getScaleResDetails',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程曲线图
export function getChSimResList(params) {
  return request({
    url: '/model/ch-sim/getChSimResList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 复制
export function getInParameter(params) {
  return request({
    url: '/model/ch-sim/getInParameter',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 保存
export function saveChSim(params) {
  return request({
    url: '/model/ch-sim/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 站点配置-列表
export function getInWaterRange(params) {
  return request({
    url: '/model/in-water/range/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 指标下的站点信息
export function getIndexCodeSites(params) {
  return request({
    url: '/base/site/getIndexCodeSites',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 站点配置-设置
export function setInWaterRange(data) {
  return request({
    url: '/model/in-water/range/set',
    method: 'post',
    data,
  })
}

// 获取降雨过程
export function getRainfallList(data) {
  return request({
    url: '/model/in-water/rainfall/list',
    method: 'post',
    data,
  })
}

// 来水预报详情
export function getInWater(params) {
  return request({
    url: '/model/in-water/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//水位mock
export function getWaterLevelMock(data) {
  return request({
    url: '/model/ch-sim/waterLevel/mock',
    method: 'post',
    data,
  })
}
