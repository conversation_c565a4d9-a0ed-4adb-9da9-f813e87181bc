<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="名称" prop="toolName">
              <a-input v-model="form.toolName" disabled placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属工程" prop="projectId">
              <a-tree-select
                v-model="form.projectId"
                disabled
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="检查时间" prop="checkDate">
              <a-date-picker v-model="form.checkDate" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="检查结论" prop="status">
              <a-select show-search placeholder="请输入" v-model="form.status" option-filter-prop="children">
                <a-select-option v-for="item in toolStatusOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="下一个检查日期" prop="lastDateTime">
              <a-date-picker v-model="form.lastDateTime" format="YYYY-MM-DD" placeholder="请选择" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="检查报告">
              <UploadFile :fileUrl.sync="form.reports" :multiple="true" listType="text" folderName="projectCover" />
            </a-form-model-item>
          </a-col>
          <!-- <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="审核" prop="status">
              <a-radio-group v-model="form.status">
                <a-radio :value="1">通过</a-radio>
                <a-radio :value="2">不通过</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col> -->

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="情况说明">
              <a-textarea
                v-model="form.content"
                placeholder="请输入"
                allow-clear
                :auto-size="{ minRows: 4, maxRows: 6 }"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { checkAdd } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'toolStatusOptions', 'toolType'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        form: {
          checkDate: null,
          content: '',
          lastDateTime: '',
          reports: [],
          status: null,
          toolId: null,
        },
        open: false,
        rules: {
          status: { required: true, message: '请选择检查结论', trigger: 'change' },
          checkDate: [{ required: true, message: '检查时间不能为空', trigger: 'change' }],
          lastDateTime: { required: true, message: '请选择下一个检查日期', trigger: 'change' },
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handle(row) {
        this.open = true
        this.formTitle = '编辑检查记录'
        // this.formTitle = '新增/编辑检查记录'

        // this.form = { ...row }
        this.form.toolName = row?.toolName
        this.form.projectId = row?.projectId

        this.form.toolId = row?.toolId
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            let pram = {
              content: this.form.content,
              checkDate: moment(this.form?.checkDate).format('YYYY-MM-DD'),
              lastDateTime: moment(this.form?.lastDateTime).format('YYYY-MM-DD'),
              reports: this.form.reports,
              status: this.form.status,
              toolId: this.form.toolId,
            }
            checkAdd(pram)
              .then(res => {
                if (res.code == 200) {
                  this.$message.success('检查完成', 3)
                  this.open = false
                  this.loading = false
                  this.$emit('close')
                  this.$emit('ok')
                }
              })
              .finally(() => (this.loading = false))
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped></style>
