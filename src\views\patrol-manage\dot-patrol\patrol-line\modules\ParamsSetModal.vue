<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="modalTitle"
      :loading="modalLoading"
      modalWidth="1200"
      @cancel="cancel"
      modalHeight="800"
    >
      <div slot="content">
        <div class="table-panel" layout="vertical">
          <!-- 左侧 -->
          <div class="left-panel">
            <div
              style="
                position: absolute;
                height: 40px;
                width: 340px;
                text-align: center;
                background-color: #1890ff;
                color: #f2f2f2;
                padding: 8px;
              "
            >
              基本信息
            </div>
            <div
              style="position: absolute; top: 40px; width: 340px; height: 130px; background-color: #eee"
              ref="form"
              :model="form"
              :rules="rules"
            >
              <a-form-model :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-form-model-item label="线路类型" style="margin-left: 10px; margin-top: 6px">
                  <a-select
                    v-model="form.lineType"
                    placeholder="请选择"
                    style="width: 100%; margin-left: 12px"
                    disabled
                  >
                    <a-select-option v-for="(d, index) in lineTypeOptions" :key="index" :value="d.key">
                      {{ d.value }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
                <a-form-model-item label="创建人" style="margin-left: 10px; margin-top: -20px">
                  <a-select
                    v-model="form.createdUserId"
                    placeholder="请选择"
                    style="width: 100%; margin-left: 12px"
                    disabled
                  >
                    <a-select-option v-for="(d, index) in userOptions" :key="index" :value="d.userId">
                      {{ d.name }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
                <a-form-model-item label="创建时间" style="margin-left: 10px; margin-top: -20px">
                  <a-date-picker
                    v-model="form.createdTime"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择"
                    style="width: 100%; margin-left: 12px"
                    disabled
                  />
                </a-form-model-item>
              </a-form-model>
            </div>
            <div
              style="
                position: absolute;
                top: 180px;
                height: 40px;
                width: 340px;
                text-align: center;
                background-color: #1890ff;
                color: #f2f2f2;
                padding: 8px;
              "
            >
              未选设施
            </div>
            <div class="table-box">
              <a-table
                :columns="leftColumns"
                :pagination="false"
                rowKey="objectId"
                style="height: 480px; overflow-y: auto; background-color: #f0f0f0"
                :data-source="unselectedList"
              >
                <span slot="action" slot-scope="text, record">
                  <a class="arrow-right">
                    <a-icon type="arrow-right" @click="selectChange(text, record)" />
                  </a>
                </span>
              </a-table>
            </div>
          </div>

          <!-- 筛选栏 -->
          <div class="right-table-panel">
            <!-- 统计数 -->
            <div class="project-statistics">
              <div class="project-data">
                <p class="project-title">巡检对象数</p>
                <p class="project-num">{{ objectCount }}个</p>
              </div>
              <div class="project-data">
                <p class="project-title">设备数</p>
                <p class="project-num">{{ deviceCount }}个</p>
              </div>
              <div class="project-data">
                <p class="project-title">巡检项数</p>
                <p class="project-num">{{ itemCount }}个</p>
              </div>
            </div>
            <!-- 对象折叠 -->
            <div class="project-table">
              <a-collapse
                v-model="activeKey"
                style="height: 610px; width: 100%; overflow-y: auto; overflow-x: hidden; background-color: #f0f0f0"
                @change="switchObject"
                :expand-icon-position="expandIconPosition"
              >
                <a-collapse-panel v-for="(item, index) in selectObjectItems" :key="String(index)">
                  <template slot="header">
                    <span
                      style="
                        position: absolute;
                        height: 20px;
                        width: 20px;
                        text-align: center;
                        background-color: #1890ff;
                        color: #f2f2f2;
                      "
                    >
                      {{ index + 1 }}
                    </span>
                    <span
                      style="
                        margin-left: 30px;
                        display: inline-block;
                        width: 188px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                      "
                      :title="item.objectName"
                    >
                      {{ item.objectName }}
                    </span>
                    <span style="margin-left: 120px">
                      巡检项：总数{{ item.totalCount }}个 已选{{ item.checkCount }}个 未选{{ item.unCheckCount }}个
                    </span>
                    <a-icon type="delete" style="float: right; color: red" @click="delPartolObject(item)" />
                  </template>
                  <!-- ${item.objectId}# -->
                  <a-table
                    :columns="tableColumns"
                    :rowKey="itemTb => `${item.objectId}#${itemTb.objectId}#${itemTb.itemId}`"
                    :data-source="item.childObject"
                    :pagination="false"
                    :row-selection="{
                      selectedRowKeys: selectedRowKeys,
                      onChange: onSelectChange,
                    }"
                  ></a-table>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </div>
        </div>
      </div>

      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="confirm" :loading="loading">确定</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import { getPatrolObjectList, chooseObjectPatrolLine, getConfigPatrolLine, configPatrolLine } from '../services'
  import draggable from 'vuedraggable'
  import cloneDeep from 'lodash.clonedeep'
  import { getOptions } from '@/api/common'

  import difference from 'lodash/difference'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import TreeGeneral from '@/components/TreeGeneral'

  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'TreeTransfer',
    components: {
      VxeTable,
      VxeTableForm,
    },
    props: ['lineTypeOptions', 'userOptions'],
    components: {
      // ATransfer,
      TreeGeneral,
      AntModal,
      AdvanceTable,
      draggable,
    },
    data() {
      return {
        loading: false,
        modalLoading: false,
        selectObjectItems: [],
        selectListTotal: 0,
        unSelectListTotal: 0,

        labelCol: { span: 4 },
        wrapperCol: { span: 16 },
        expandIconPosition: 'right',
        activeKey: [],
        selectedRowKeys: [], // Check here to configure the default column

        dataSource: [],
        targetKeys: [],
        disabled: false,
        showSearch: true,

        objectCount: 0,
        deviceCount: 0,
        itemCount: 0,

        propsTargetKeys: [],
        treeTabKey: '1',

        patrolObjectId: null,
        patrolItemIds: [],

        open: false,
        modalTitle: '',
        rowInfo: {},
        isShowTableModal: false,
        dragging: false,

        reqType: 'add',
        isActive: false,
        selectedRows: [],
        patrolLineId: null,
        ids: [],
        single: true,
        multiple: true,
        list: [],
        taskType: '普通线路',

        objectTypeOptions: [
          //巡检对象类型(1-水利工程 2-监测站点 3-江河湖泊 9-工程设备 )
          { key: 1, value: '水利工程' },
          { key: 2, value: '监测站点' },
          { key: 3, value: '江河湖泊' },
          { key: 9, value: '工程设备' },
        ],
        objectTypes: {},
        form: {
          createdUserId: 0,
          lineId: 0,
          createdTime: null,
          patrolUserId: 0,
        },
        param: {
          lineId: 0,
          objects: [
            {
              itemIds: [],
              objectId: 0,
            },
          ],
        },
        orgList: [],
        unselectedList: [],
        selectIds: [],
        configList: [],
        leftColumns: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            width: '50px',
          },
          {
            title: '名称',
            dataIndex: 'objectName',
            align: 'left',
            ellipsis: true,
          },
          {
            title: '操作',
            key: 'action',
            width: '50px',
            scopedSlots: { customRender: 'action' },
          },
        ],
        tableColumns: [
          { title: '', type: 'checkbox', width: 30 },
          {
            title: '对象类型',
            dataIndex: 'objectType',
            scopedSlots: { customRender: 'objectType' },
            key: 'key',
            customRender: (_, record, i) => {
              return this.objectTypes[record.objectType]?.value || ''
            },
          },
          { title: '对象编码', dataIndex: 'objectCode' },
          { title: '对象名称', dataIndex: 'objectName' },
          { title: '巡检项', dataIndex: 'itemName' },
          { title: '所在位置', dataIndex: 'address' },
        ],
        rules: {},
      }
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    methods: {
      /** 初始化数据 **/
      init() {
        this.objectTypes = getFlatTreeMap(this.objectTypeOptions, 'key')
        let objParam = {
          lineId: 0,
          objectCategoryId: null,
          objectIds: [],
          objectName: '',
          objectType: null,
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          patrolType: this.$route.meta.query.patrolType,
          sort: [],
        }
        getPatrolObjectList(objParam).then(res => {
          this.orgList = res?.data?.data
          this.unselectedList = this.orgList.filter(item => !this.selectIds.includes(String(item.objectId)))
        })
      },
      /* 未选列表点击选中至右侧 */
      selectChange(text, record) {
        // 使用filter()方法过滤掉objectId为id的数据
        var newList = this.unselectedList.filter(function (obj) {
          return obj.objectId != text.objectId
        })
        // 更新objList数组
        this.unselectedList = newList
        this.configList = []
        chooseObjectPatrolLine({ objectId: text.objectId }).then(res => {
          let arr = res?.data || []
          let targetArr = []
          let selectedOrgArr = []
          arr.forEach((el, idx) => {
            targetArr.push({ ...el, childObject: [] })
            el.childObject.forEach((ele, ibjIndex) => {
              ele.patrolItemCheckeds?.forEach((item, itemIndex) => {
                this.selectIds.push(String(el.objectId))
                if (item?.itemName) {
                  selectedOrgArr.push(`${el.objectId}#${ele.objectId}#${item.itemId}`)
                  targetArr[idx].childObject.push({
                    ...ele,
                    ...item,
                    patrolItemCheckeds: null,
                  })
                }
              })
            })
          })
          this.selectIds = [...new Set(this.selectIds)]
          this.selectedRowKeys = this.selectedRowKeys.concat(selectedOrgArr)

          let selectArr = []
          targetArr.forEach((el, index) => {
            let targetArr = []
            let totalCount = 0
            let unCheckCount = 0
            let checkCount = 0
            this.selectedRowKeys.forEach(el => {
              let id = el.split('#')[0]
              let itemId = el.split('#')[2]
              if (targetArr.some(ele => ele.objectId == id)) {
                const idx = targetArr.findIndex(item => item.objectId == id)
                targetArr[idx].itemIds.push(itemId)
              } else {
                targetArr.push({ objectId: id, itemIds: [itemId] })
              }
            })
            targetArr.forEach((countEl, index) => {
              if (el.objectId == countEl.objectId) {
                totalCount = el.childObject.length
                checkCount = countEl.itemIds ? countEl.itemIds.length : 0
                unCheckCount = el.childObject.length - checkCount
              }

              if (el.childObject.length == 0) {
                totalCount = 0
                checkCount = 0
                unCheckCount = 0
              }
            })
            selectArr.push({
              ...el,
              totalCount: totalCount,
              checkCount: checkCount,
              unCheckCount: unCheckCount,
            })
          })
          this.selectObjectItems = this.selectObjectItems.concat(selectArr)
          this.getCount(this.selectedRowKeys, this.selectObjectItems)
        })
      },
      /* 设备配置 */
      handleShow(record) {
        this.open = true
        this.modalTitle = '内容配置'
        this.rowInfo = record

        this.form = {
          createdUserId: record.createdUserId,
          lineType: String(record.lineType),
          createdTime: record.createdTime,
        }
        this.patrolLineId = record.lineId

        this.selectedRowKeys = []
        this.selectObjectItems = []
        this.configList = []
        this.objectCount = 0
        this.deviceCount = 0
        this.itemCount = 0
        this.modalLoading = true

        getConfigPatrolLine({ lineId: record.lineId }).then(res => {
          this.modalLoading = false
          let orgArr = res?.data
          this.configList = res?.data
          let r = 0
          let objectTmpId = ''
          let selectedObjArr = []
          if (orgArr.length > 0) {
            orgArr.forEach((el, idx) => {
              objectTmpId += el.objectId + ','
              el.childObject.forEach((ele, ibjIndex) => {
                ele.patrolItemCheckeds?.forEach((item, itemIndex) => {
                  selectedObjArr.push(`${el.objectId}#${ele.objectId}#${item.itemId}`)
                })
              })
            })
            objectTmpId = objectTmpId.slice(0, -1)
            this.selectIds = objectTmpId ? objectTmpId.split(',') : []
            this.unselectedList = this.orgList.filter(item => !this.selectIds.includes(String(item.objectId)))
            this.selectedRowKeys = selectedObjArr
            chooseObjectPatrolLine({ objectId: objectTmpId }).then(res => {
              let arr = res?.data || []
              let targetArr = []

              arr.forEach((el, idx) => {
                targetArr.push({ ...el, childObject: [] })
                el.childObject.forEach((ele, ibjIndex) => {
                  ele.patrolItemCheckeds?.forEach((item, itemIndex) => {
                    if (item?.itemName) {
                      targetArr[idx].childObject.push({
                        ...ele,
                        ...item,
                        patrolItemCheckeds: null,
                      })
                    }
                  })
                })
              })
              let selectArr = []
              targetArr.forEach((el, index) => {
                let targetArr = []
                let totalCount = 0
                let unCheckCount = 0
                let checkCount = 0
                this.selectedRowKeys.forEach(el => {
                  let id = el.split('#')[0]
                  let itemId = el.split('#')[2]
                  if (targetArr.some(ele => ele.objectId == id)) {
                    const idx = targetArr.findIndex(item => item.objectId == id)
                    targetArr[idx].itemIds.push(itemId)
                  } else {
                    targetArr.push({ objectId: id, itemIds: [itemId] })
                  }
                })
                targetArr.forEach((countEl, index) => {
                  if (el.objectId == countEl.objectId) {
                    totalCount = el.childObject.length
                    checkCount = countEl.itemIds.length
                    unCheckCount = el.childObject.length - checkCount
                  }
                })
                selectArr.push({
                  ...el,
                  totalCount: totalCount,
                  checkCount: checkCount,
                  unCheckCount: unCheckCount,
                })
              })
              this.selectObjectItems = this.selectObjectItems.concat(selectArr)
            })
            this.init()
            this.getCount(this.selectedRowKeys, this.selectObjectItems)
          } else {
            this.selectIds = []
            this.init()
            this.unselectedList = this.orgList
            this.getCount(this.selectedRowKeys, this.selectObjectItems)
          }
        })
      },
      /* 删除折叠面板列表 */
      delPartolObject(item) {
        var that = this
        const taskIds = item.objectId
        const names = item.objectName
        that.configList = []
        this.$confirm({
          title: '确认删除该条数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            that.selectIds = that.selectIds.filter(item => item != String(taskIds))
            that.unselectedList = that.orgList.filter(
              selectItem => !that.selectIds.includes(String(selectItem.objectId)),
            )

            that.selectObjectItems = that.selectObjectItems.filter(obj => obj.objectId != item.objectId)
            that.selectedRowKeys = that.selectedRowKeys.filter(item => item.startsWith(String(taskIds)) === false)

            that.getCount(that.selectedRowKeys, that.selectObjectItems)
            that.$message.success(`成功删除该条数据`, 3)
          },
          onCancel() {},
        })
      },
      //折叠面板中对象下列表显示数据统计
      updateData(selectedRowKey, targetArr) {
        this.selectObjectItems = []
        let selectArr = []
        targetArr.forEach((el, index) => {
          let targetArr = []
          let totalCount = 0
          let unCheckCount = 0
          let checkCount = 0
          selectedRowKey.forEach(el => {
            let id = el.split('#')[0]
            let itemId = el.split('#')[2]
            if (targetArr.some(ele => ele.objectId == id)) {
              const idx = targetArr.findIndex(item => item.objectId == id)
              targetArr[idx].itemIds.push(itemId)
            } else {
              targetArr.push({ objectId: id, itemIds: [itemId] })
            }
          })
          targetArr.forEach((countEl, index) => {
            if (el.objectId == countEl.objectId) {
              totalCount = el.childObject.length
              checkCount = countEl.itemIds ? countEl.itemIds.length : 0
              unCheckCount = el.childObject.length - checkCount
            }

            if (el.childObject.length == 0) {
              totalCount = 0
              checkCount = 0
              unCheckCount = 0
            }
          })
          selectArr.push({
            ...el,
            totalCount: totalCount,
            checkCount: checkCount,
            unCheckCount: unCheckCount,
          })
        })
        this.selectObjectItems = this.selectObjectItems.concat(selectArr)
      },
      //折叠面板中对象下列表选中项
      onSelectChange(selectedRowKeys, selectedRows) {
        this.getCount(selectedRowKeys, this.selectObjectItems)
        this.selectedRowKeys = selectedRowKeys
        this.updateData(this.selectedRowKeys, this.selectObjectItems)
      },
      /* 统计对象、设备、巡检项总数 */
      getCount(newArr, objectItems) {
        newArr = newArr.filter(item => {
          return item.indexOf('__ob__: Observer') == -1
        })
        let newObjectArr = []
        let newDeviceArr = []
        if (newArr.length) {
        }
        for (let i = 0; i < newArr.length; i++) {
          let item = newArr[i]
          let splitItem = item.split('#')

          newObjectArr.push(splitItem[0])
          if (splitItem[0] == splitItem[1]) {
            newDeviceArr.push()
          } else {
            newDeviceArr.push(splitItem[0] + '#' + splitItem[1])
          }
        }
        newObjectArr = [...new Set(newObjectArr)]
        newDeviceArr = [...new Set(newDeviceArr)]
        this.objectCount =
          this.configList.length != 0
            ? this.configList.length
            : objectItems.length != 0
            ? objectItems.length
            : newObjectArr
            ? newObjectArr.length
            : 0
        this.deviceCount = newDeviceArr ? newDeviceArr.length : 0
        this.itemCount = newArr ? newArr.length : 0
      },

      switchObject(e) {},
      handleExpandChange(activeKeys) {
        // this.activeKey.push('')
        // 处理折叠展示的逻辑
      },
      handleExpand(index) {
        // 处理列表项展开的逻辑
      },
      getList(type) {
        this.dataSource = []
        this.$forceUpdate()
      },
      async typeTargetKeys(Arr) {
        this.propsTargetKeys = Arr
      },
      // 取消按钮
      cancel() {
        this.open = false
        // this.$emit('on')
        this.$emit('ok')
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.open = true
        this.modalTitle = '新增'
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.modalTitle = '修改'
        this.rowInfo = record
      },
      /** 提交按钮 */
      confirm() {
        let objectArr = {
          lineId: this.patrolLineId,
          objects: [],
        }

        this.loading = true
        let targetArr = []
        if (this.selectedRowKeys.length > 0) {
          let result = {}
          let result2 = {}
          this.selectedRowKeys.forEach(item => {
            let [objectId, deviceId, itemId] = item.split('#')
            if (!result[objectId]) {
              result[objectId] = {
                objectId: objectId,
                itemIds: [],
              }
            }
            if (deviceId == objectId) {
              result[objectId].itemIds.push(parseInt(itemId))
            }

            if (!result2[deviceId]) {
              result2[deviceId] = {
                objectId: deviceId,
                itemIds: [],
              }
            }
            result2[deviceId].itemIds.push(parseInt(itemId))
          })

          let newList = Object.values(result)
          let newList2 = Object.values(result2)
          let mergedArray = newList.concat(newList2)

          let objectIds = {}
          mergedArray.forEach(item => {
            if (!objectIds[item.objectId]) {
              targetArr.push(item)
              objectIds[item.objectId] = item
            } else {
              let existingItem = objectIds[item.objectId]
              existingItem.itemIds = item.itemIds
            }
          })
          //配置对象下无设备、巡检项的
          this.selectedRowKeys.forEach(el => {
            let id = el.split('#')[0]
            this.selectObjectItems = this.selectObjectItems.filter(ele => ele.objectId != id)
          })
          if (this.selectObjectItems) {
            this.selectObjectItems.forEach(item => {
              targetArr.push({ objectId: item.objectId, itemIds: [] })
            })
          }
          objectArr.objects = targetArr
          if (objectArr.length != 0) {
            configPatrolLine(objectArr)
              .then(res => {
                this.$message.success('内容配置成功', 3)
                this.open = false
                this.$emit('ok')
                // this.$emit('close')
              })
              .catch(() => (this.loading = false))
          }
        } else {
          configPatrolLine(objectArr)
            .then(res => {
              this.$message.success('内容配置成功', 3)
              this.open = false
              this.$emit('ok')
              // this.$emit('close')
            })
            .catch(() => (this.loading = false))
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';

  // 多余滚动条
  ::v-deep .advanced-table {
    overflow-y: visible;
  }

  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background-color: transparent;
  }

  ::v-deep .table-panel {
    display: flex;
    height: 820px;
    width: 100%;
    .left-panel {
      position: relative;
      width: 340px;
    }

    .right-table-panel {
      position: relative;
      width: 100%;
      background-color: #fff;
      height: 700px;

      .project-statistics {
        position: absolute;
        width: calc(100% - 10px);
        margin-left: 20px;
        display: flex;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;

        .project-data {
          flex: 1;
          width: 30%;
          background-color: #1890ff;
          color: #fff;
          height: 60px;
          margin-right: 30px;

          .project-title {
            float: left;
            margin-left: 15px;
            margin-top: 20px;
          }

          .project-num {
            float: right;
            margin-right: 15px;
            margin-top: 20px;
          }
        }
        .project-data:last-child {
          margin-right: 0; /* 设置最后一个box元素的margin-right为0 */
        }
      }

      .project-table {
        position: absolute;
        top: 100px;
        margin-left: 20px;
        width: calc(100% - 10px);
        height: 500px;
      }
    }
  }

  ::v-deep .ant-transfer-list-header {
    color: #fff;
    background: #1890ff;
  }

  ::v-deep .ant-modal-body {
    width: 100% !important;
    height: 740px !important;
    max-height: 740px !important;
    overflow: hidden !important;
  }

  ::v-deep .ant-transfer-list-body-customize-wrapper {
    height: 640px !important;
    overflow-y: hidden;
    overflow-x: hidden;
  }

  ::v-deep .ant-table-wrapper {
    overflow-y: auto;
    overflow-x: hidden;
  }

  ::v-deep .modal-content {
    height: 760px !important;
    width: 100% !important;
  }

  ::v-deep .ant-modal-content {
    height: 860px;
    width: 100%;
    overflow-x: hidden;
  }

  .table-box {
    position: absolute;
    top: 220px;
    width: 340px;
    height: 488px;
    background-color: #eee;

    overflow: hidden;
    position: relative;
    ::v-deep .ant-table-thead {
      position: sticky;
      top: 0px;
      z-index: 2;
    }
  }
</style>
