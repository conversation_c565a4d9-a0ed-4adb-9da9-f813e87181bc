<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="预案名称">
        <a-input
          v-model="queryParam.emergencyPlanName"
          placeholder="请输入"
          allow-clear
          @keyup.enter.native="handleQuery"
        />
      </a-form-item>

      <a-form-item label="预案类型">
        <a-select
          show-search
          placeholder="请输入"
          v-model="queryParam.emergencyPlanType"
          option-filter-prop="children"
        >
          <a-select-option v-for="item in emergencyTypeOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="批复时间">
        <a-date-picker
          allow-clear
          :value="queryParam.year"
          format="YYYY"
          valueFormat="YYYY"
          mode="year"
          :open="showYearPicker"
          @panelChange="
            val => {
              queryParam.year = val
              showYearPicker = false
            }
          "
          @focus="() => (showYearPicker = true)"
          placeholder="请选择"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-model-item label="所属工程">
        <a-tree-select
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :emergencyTypeOptions="emergencyTypeOptions"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :emergencyTypeOptions="emergencyTypeOptions"
          :projectOptions="projectOptions"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getPlanPage, deletePlan } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'EmergencyPlan',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
    },
    data() {
      return {
        unitList: [],
        unitArr: [],
        isChecked: false,
        emergencyTypeOptions: [],
        emergencyTypes: [],
        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        replyTimes: [],
        archivesOptions: [],
        showYearPicker: false,

        list: [],
        tableTitle: '应急预案',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          emergencyPlanName: '',
          emergencyPlanType: null,
          endTime: null,
          pageNum: 1,
          pageSize: 10,
          projectId: null,
          sort: [],
          startTime: null,
          year: undefined,
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '预案名称',
            field: 'emergencyPlanName',
            minWidth: 120,
          },
          {
            title: '所属工程',
            minWidth: 120,
            showOverflow: true,
            field: 'projectName',
            // slots: {
            //   default: ({ row, rowIndex }) => {
            //     return this.projects[row.projectId]?.projectName || ''
            //   }
            // }
          },
          {
            title: '预案类型',
            field: 'emergencyPlanType',
            minWidth: 120,

            slots: {
              default: ({ row, rowIndex }) => {
                return this.emergencyTypes[row.emergencyPlanType]?.value || ''
              },
            },
          },

          {
            title: '批复时间',
            field: 'replyTime',
            minWidth: 120,
          },
          {
            title: '负责人',
            field: 'chargeName',
            minWidth: 100,
          },
          {
            title: '创建人',
            field: 'createdUserName',
            minWidth: 100,
          },
          {
            title: '创建时间',
            field: 'createdTime',
            minWidth: 170,
          },
          {
            title: '操作',
            field: 'operate',
            width: 138,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
      getOptions('emergencyType').then(res => {
        this.emergencyTypeOptions = res.data
        this.emergencyTypes = getFlatTreeMap(this.emergencyTypeOptions, 'key')
      })

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getPlanPage({
          ...this.queryParam,
          year: this.queryParam.year ? moment(this.queryParam.year).format('YYYY') : this.queryParam.year,
        }).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.emergencyPlanId)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          levelResponse: null,
          pageNum: 1,
          projectId: null,
          year: undefined,
          sort: [],
          year: null,
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      }, // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.emergencyPlanId ? [row?.emergencyPlanId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deletePlan({ emergencyPlanIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
