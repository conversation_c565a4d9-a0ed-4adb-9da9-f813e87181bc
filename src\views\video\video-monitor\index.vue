<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane
            key="HP"
            tab="水利工程视频监测点"
          >
            <TreeGeneral
              hasTab
              style="width: 320px"
              ref="treeGeneralRef"
              :treeOptions="projectTreeOptions"
              @onTreeMounted="onTreeMounted"
              @select="node => clickTreeNode(node, 'project')"
            />
          </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <div class="easy-player-wrapper" v-if="videoSrc">
        <VideoEasyPlayer
          v-if="videoSrc"
          :videoSrc="videoSrc"
          :isShowDirection="isShowDirection"
          @setDirection="setDirection"
          @pullFlow="pullFlow"
        />
        <div class="delete" @click.stop="() => onDeleteClick()">
          <a-icon type="delete" style="font-size: 20px; color: #dddddd" />
        </div>
      </div>
      <div class="ant-card video-monitor-wrapper">
        <div
          class="video-monitor-item"
          v-for="(item, index) in treeVideoMonitorList"
          :key="index"
          @click="showTreeVideo(item)"
        >
          <img class="img" :src="item?.ext?.snapUrl" @error="noFindImg" />
          <p class="status-text">
            <span class="text" :title="item?.ext?.cameraName">{{ item?.ext?.cameraName }}</span>
            <span :class="[item.ext.onLine == 1 ? 'online' : 'offline']"></span>
          </p>
        </div>
      </div>
      <div class="no-video-wrapper" v-if="treeVideoMonitorList?.length == 0">
        <div>
          <img src="/no_video_img.png" />
          <p>暂无数据</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import {
    getVideoCategoryTree,
    getEasyVideo,
    setEasyVideoDirection,
  } from './services'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import VideoEasyPlayer from '@/components/EasyPlayer'
  import TreeGeneral from '../split/videoSingleTree.vue'
  import moment from 'moment'

  const types = ['project', 'river', 'site', 'other']

  export default {
    name: 'video-monitor',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      VideoEasyPlayer,
    },
    data() {
      return {
        treeVideoMonitorList: [],

        treeTabKey: 'HP',
        projectTreeOptions: {
          getDataApi: getVideoCategoryTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'id',
          },
        },

        videoSrc: '',
        parentItem: {},
        isShowDirection: false,
        selectList: [],
        queryParam: {
          // "videoName": "",
          channel: null,
          device: null,
          protocol: 'flv',
        },
      }
    },
    created() {
    
    },
    mounted() {
      // getVideoCategoryTree().then(res => {
      //   this.projectTreeOptions.dataSource = res?.data
      // })
    },
    methods: {
      getVideo() {
        this.loading = true
        getEasyVideo(this.queryParam).then(res => {
          if (res?.data) {
            this.videoSrc = res?.data
          } else {
            this.selectList = []
            this.$message.warning('该视频无信号')
          }
          this.loading = false
        })
      },
      //播放异常，重新播放
      pullFlow() {
        this.getVideo()
      },
      //
      setDirection(dir) {
        let dirParams = {
          channel: this.queryParam.channel,
          command: dir,
          device: this.queryParam.device,
          direction: dir,
          speed: 50,
        }
        let dirParams2 = Object.assign({}, dirParams)
        dirParams2.command = 'stop'
        setEasyVideoDirection(dirParams).then(res => {
          if (res) {
            setEasyVideoDirection(dirParams2).then(res => {
              if (res) {
                console.log('调整成功')
              }
            })
          }
        })
      },
      onDeleteClick() {
        this.videoSrc = ''
        this.selectList = []
      },
      restartPlayer() {
        console.log('restartPlayer')
      },
      showTreeVideo(row) {
        this.queryParam.channel = row?.ext?.channelCode
        this.queryParam.device = row?.ext?.deviceCode
        // this.queryParam.protocol = row.protocol;
        if (row?.ext?.cameraType == 2) {
          this.isShowDirection = true
        } else {
          this.isShowDirection = false
        }
        this.getVideo()
        this.selectList = []
        this.selectList.push(row)
      },

      // 树加载完成后//ownerId:device  channelCode:channel  protocol: flv
      onTreeMounted(data) {
        if (data?.children?.length) {
          data?.children.forEach(el => {
            if (el?.type == 'data') {
              this.treeVideoMonitorList = []
              this.$nextTick(() => {
                this.treeVideoMonitorList.push(el)
              })
            }
          })
        }
      },
      clickTreeNode(node) {
        this.videoSrc = ''
        this.treeVideoMonitorList = []
        let nodeItem = node?.$options?.propsData?.dataRef
        if (types.includes(nodeItem?.type)) {
          if (nodeItem?.children?.length) {
            nodeItem?.children?.forEach(el => {
              this.treeVideoMonitorList.push(el)
            })
          }
        } else if (nodeItem?.type == 'data') {
          this.getTreeParentById(this.projectTreeOptions.dataSource, nodeItem.ext.projectId)
          this.parentItem?.children?.forEach(el => {
            this.treeVideoMonitorList.push(el)
          })
          this.showTreeVideo(nodeItem)
          this.selectList = []
          this.selectList.push(nodeItem)
        }
      },
      noFindImg(event) {
        let img = event.target
        img.src = '/no_video_img.png'
        img.background = '#F7F8FA'
        img.onerror = null //防止一直跳动
      },
      getTreeParentById(treeData, parentId) {
        for (let i = 0; i < treeData.length; i++) {
          if (treeData[i].key == parentId) {
            this.parentItem = treeData[i]
          } else if (treeData[i]?.children?.length) {
            this.getTreeParentById(treeData[i].children, parentId)
          }
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-right-panel {
    width: 100%;
    height: calc(100vh - 120px);
    background: #fff;
    position: relative;
    .vxe-table-form {
      height: 63px;
    }
  }
  .easy-player-wrapper {
    width: 100%;
    height: calc(100vh - 120px);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    .delete {
      display: none;
      position: absolute;
      padding: 8px;
      border-radius: 4px;
      right: 10px;
      top: 10px;
      background-color: #000;
      z-index: 9999;
    }
    &:hover {
      .delete {
        display: block;
        cursor: pointer;
      }
    }
  }
  .video-monitor-wrapper {
    padding-top: 10px;
    overflow-x: auto;
    display: flex;
    flex-flow: wrap;
    color: #1d2129;
    z-index: 10;
    .video-monitor-item {
      width: 177px;
      height: 158px;
      margin-left: 10px;
      margin-bottom: 10px;
      cursor: pointer;
      .img {
        width: 177px;
        height: 122px;
        background: #f7f8fa;
        border-radius: 4px;
      }
      .status-text {
        display: flex;
        padding: 6px;
      }
      .text {
        display: inline-block;
        width: 160px;
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .online,
      .offline {
        width: 6px;
        height: 6px;
        border-radius: 6px;
        margin-top: 9px;
        display: inline-block;
      }
      .online {
        background: #9fdb1d;
      }
      .offline {
        background: #86909c;
      }
    }
  }
  .no-video-wrapper {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
  .close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 20px;
    cursor: pointer;
    color: #f94565;
    z-index: 999;
  }
</style>
