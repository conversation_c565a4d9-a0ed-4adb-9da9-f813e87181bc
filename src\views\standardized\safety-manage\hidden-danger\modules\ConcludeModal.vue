<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="是否通过" prop="status">
              <a-radio-group v-model="form.status">
                <a-radio :value="3">通过</a-radio>
                <a-radio :value="4">不通过</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item v-if="form.status === 4" label="不通过原因" prop="remark">
              <a-textarea
                v-model="form.remark"
                placeholder="请输入"
                allow-clear
                :auto-size="{ minRows: 3, maxRows: 3 }"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { concludeHidden } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormModal',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'hiddenTypeOptions', 'userOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        open: false,
        form: {
          hiddenId: undefined,
          status: 3,
          remark: undefined,
        },
        rules: {
          status: [{ required: true, message: '是否通过不能为空', trigger: 'change' }],
          remark: [{ required: true, message: '隐患描述不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        if (row != undefined) {
          this.form.hiddenId = row?.hiddenId
          this.formTitle = '办结'
        }
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            const params = {
              ...this.form,
            }

            this.loading = true

            concludeHidden(params)
              .then(res => {
                if (res.code == 200) {
                  this.$message.success('办结成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                }
              })
              .finally(() => (this.loading = false))
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
