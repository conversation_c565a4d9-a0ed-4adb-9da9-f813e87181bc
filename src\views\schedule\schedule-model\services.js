import request from '@/utils/request'

// 分页查询
export function getSchedulingPage(data) {
  return request({
    url: '/model/scheduling/page',
    method: 'post',
    data,
  })
}

// 删除
export function deleteScheduling(params) {
  return request({
    url: '/model/scheduling/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取降雨过程
export function getRainfallList(data) {
  return request({
    url: '/model/scheduling/rainfall/list',
    method: 'post',
    data,
  })
}

// 降雨过程-未来预报实测降雨
export function getFutureRainfallList(data) {
  return request({
    url: '/model/scheduling/rainfall/futureRains',
    method: 'post',
    data,
  })
}

// 添加-手动预报
export function addForecast(data) {
  return request({
    url: '/model/scheduling/forecast',
    method: 'post',
    data,
  })
}

// 入参-初始状态
export function getChWaterList(params) {
  return request({
    url: '/model/scheduling/getChWaterList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 入参-进出口边界水位
export function getProjectInOutWater(params) {
  return request({
    url: '/model/scheduling/getProjectInOutWater',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 入参-获取调度过程
export function getDispathProjectList(data) {
  return request({
    url: '/model/scheduling/getDispathProjectList',
    method: 'post',
    data,
  })
}

// 获取模型进度
export function getModelRunProcess(params) {
  return request({
    url: '/model/scheduling/getModelRunProcess',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取调度方案详细信息
export function getEleVo(params) {
  return request({
    url: '/model/scheduling/getEleVo',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 详情
export function getSchedulingDetail(params) {
  return request({
    url: '/model/scheduling/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
