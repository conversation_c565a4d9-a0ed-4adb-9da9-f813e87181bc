<template>
  <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff">
    <div style="height: 100%; display: flex; flex-direction: column; padding: 16px;">
      <!-- 第一行：标题和模型选择 -->
      <div
        style="display: flex; flex-direction: row; align-items: center; justify-content: space-between; margin-bottom: 16px;">
        <div class="page-title">洪涝灾害评价详情</div>
        <div style="display: flex; align-items: center;">
          <span>模型: </span>&nbsp;&nbsp;
          <a-select v-model="modelName" allowClear style="width: 350px;" placeholder="请选择" :options="modelOptions"
            show-search @change="handleModelChange" :loading="modelListLoading"></a-select>
        </div>
      </div>

      <!-- 第二行：信息组 -->
      <div class="info-group" style="border-bottom: 1px solid #E5E6EB; margin-bottom: 16px;">
        <div style="display: flex; flex-direction: row; justify-content: space-between; margin-bottom: 16px;">
          <div class="info-card" v-for="(item, index) in infoCards" :key="index">
            <div class="info-icon">
              <img :src="getIconSrc(index)" style="width: 40px; height: 40px;" />
            </div>
            <div class="info-content">
              <div class="info-title">{{ item.title }}</div>
              <div class="info-value-container">
                <span class="info-value">{{ formatNumber(item.value) }}</span>
                <span class="info-unit">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- 第三行：地图和表格 -->
      <div style="flex: 1; display: flex; flex-direction: row;">
        <!-- 左侧地图区域 -->
        <div style="flex: 0.6; display: flex; flex-direction: column; margin-right: 16px;">
          <!-- 地图标题 -->
          <div class="map-title" v-if="currentModelData">
            <span class="area-name">{{ currentModelData.schedulingName }}</span>
            <!-- <span class="date-range"> {{ currentModelData.startDate }} ~ {{ currentModelData.endDate }}</span> -->
          </div>
          <!-- 地图容器 -->
          <div style="flex: 1; position: relative; border: 1px solid #e8e8e8;">
            <div
              style="width: 100%; height: 100%; background-color: #f5f5f5; display: flex; justify-content: center; align-items: center;">
              <Mapbox @onMapMounted="onMapMounted" mapBoxId="evaluate" />
              <div v-if="activeTab == 'river'"
                    style="position: absolute;width: 320px; height: 35px; display: flex; flex-direction: column;  bottom: 10px; right: 10px; background: rgba(255, 255, 255, 0.7); padding: 5px; border-radius: 3px; z-index: 1000;"
                    >
                    <div style="font-size: 10px;height: 15px; line-height: 10px; font-weight: bold; margin-bottom: 1px; ">最大超警戒水深</div>
                    <div style="width: 100%;height: 20px; ">
                       <div style="height: 3px; width: 25px; margin-top: 6px; float: left; background-color: #00FF00;"></div>
                       <div style="height: 15px; line-height: 15px; float: left;margin-left: 3px;margin-right: 6px; font-size: 10px; ">h < 0.3m</div>
                       <div style="height: 3px; width: 25px; margin-top: 6px; float: left; background-color: #FFFF00;"></div>
                       <div style="height: 15px; line-height: 15px; float: left;margin-left: 3px;margin-right: 6px; font-size: 10px; "> 0.3m <= h < 0.39m</div>
                       <div style="height: 3px; width: 25px; margin-top: 6px; float: left; background-color: #FF0000;"></div>
                       <div style="height: 15px; line-height: 15px; float: left;margin-left: 3px;margin-right: 6px; font-size: 10px; "> h >= 0.39m</div>
                    </div>
                </div>
            </div>
          </div>
        </div>

        <!-- 右侧表格 -->
        <div style="flex: 0.4;">
          <a-tabs v-model="activeTab" @change="handleTabChange" style="height: 100%;">
            <a-tab-pane key="area" tab="片区灾害评价">
              <div style="height: 100%;">
                <a-spin :spinning="tableLoading">
                  <VxeTable ref="areaTableRef" size="small" :isShowTableHeader="false" :columns="areaColumns"
                    :tableData="areaTableData" :tablePage="false" :isShowSetBtn="false" height="600px"></VxeTable>
                </a-spin>
              </div>
            </a-tab-pane>
            <a-tab-pane key="river" tab="河道灾害评价">
              <div style="height: 100%;">
                <a-spin :spinning="tableLoading">
                  <VxeTable ref="riverTableRef" size="small" :isShowTableHeader="false" :columns="riverColumns"
                    :tableData="riverTableData" :tablePage="false" :isShowSetBtn="false" height="600px"></VxeTable>
                </a-spin>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VxeTable from '@/components/VxeTable/index.vue'
import { getRiverEvaluateData, getAreaEvaluateData, getSchedulingPage, getEleVo } from './services'
import evaluateRain from '@/assets/images/evaluate-rain.png'
import evaluateSide from '@/assets/images/evaluate-side.png'
import evaluateJainchan from '@/assets/images/evaluate-jainchan.png'
import evaluateMianji from '@/assets/images/evaluate-mianji.png'
import evaluateShuiwei from '@/assets/images/evaluate-shuiwei.png'
import evaluateTime from '@/assets/images/evaluate-time.png'

import { mapBoundGeo } from '@/utils/mapBounds.js'
import axios from 'axios'
import Mapbox from './Mapbox/index.vue'
import Mapbox from './Mapbox/index.vue'
import MapStyle from '@/components/MapBox/MapStyle.vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import * as turf from '@turf/turf'

const colors = [
  "#00FF00", "#19FF00", "#33FF00", "#4DFF00", "#66FF00", "#80FF00", "#99FF00", "#B3FF00", "#CCFF00", "#E6FF00",
  "#FFFF00", "#FFE600", "#FFCC00", "#FFB300", "#FF9900", "#FF8000", "#FF6600", "#FF4D00", "#FF3300", "#FF1900",
  "#FF0000"
]
let paiShuiUrl = "https://www.sthzxgq.cn:11120/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=sthgq_vector%3Asthgq_paishuiqu&maxFeatures=5000&outputFormat=application%2Fjson"
let shuiDongLiUrl = "https://www.sthzxgq.cn:11120/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=sthgq_vector%3AHP005&maxFeatures=50000&outputFormat=application%2Fjson"

export default {
  name: 'Evaluate',
  mapIns: null,
  components: { VxeTable, Mapbox },
  data() {
    return {
      mapLoading: false,
      currentModelData: null,
      tableLoading: false,
      modelListLoading: false,

      // 模型选择
      modelName: null,
      modelOptions: [],

      // 图标映射
      iconMapping: {
        0: evaluateRain,    // 预报总降雨量
        1: evaluateMianji,  // 田间总排水量
        2: evaluateTime,    // 最大淹没深度
        3: evaluateJainchan,// 涝灾减产率
        4: evaluateShuiwei, // 河道最高水位
        5: evaluateSide,    // 河道最大超警戒水深
      },

      // 信息卡片数据
      infoCards: [
        { title: '预报总降雨量', value: '0', unit: 'mm' },
        { title: '田间总排水量', value: '0', unit: '万m³' },
        { title: '最大淹没深度', value: '0', unit: 'm' },
        { title: '涝灾减产率', value: '0', unit: '%' },
        { title: '河道最高水位', value: '0', unit: 'm' },
        { title: '河道最大超警戒水深', value: '0', unit: 'm' }
      ],

      // 表格相关
      activeTab: 'area',
      areaColumns: [
        { type: 'seq', title: '序号', width: 60 },
        { title: '片区名称', field: 'areaName', minWidth: 120 },
        { title: '排水量(万m³)', field: 'waterDischarge', minWidth: 120 },
        { title: '最大淹没深度(m)', field: 'maxDepth', minWidth: 120 },
        { title: '持续时间(h)', field: 'floodHour', minWidth: 120 },
        { title: '涝灾减产率(%)', field: 'reductionRate', minWidth: 120 }
      ],
      riverColumns: [
        { type: 'seq', title: '序号', width: 60 },
        { title: '河道名称', field: 'projectName', minWidth: 120 },
        { title: '最高水位(m)', field: 'maxWlv', minWidth: 120 },
        { title: '最大超警戒水深(m)', field: 'alertWlv', minWidth: 120 },
        { title: '超警戒河段长度(km)', field: 'submergedLength', minWidth: 120 },
        { title: '超警戒持续时间(h)', field: 'alertTime', minWidth: 120 }
      ],
      areaTableData: [],
      riverTableData: [],

      // 路由参数
      routeSchedulingId: null,
      routeSchedulingName: null
    }
  },
  methods: {
    // 格式化数字为两位小数
    formatNumber(value) {
      if (!value || isNaN(Number(value))) return '0.00'
      return Number(value).toFixed(2)
    },

    onMapMounted(mapIns) {
      this.mapIns = mapIns
      this.initMap(mapIns)
    },
    async initMap(mapIns) {
      // 天地图
      mapIns.addLayer(
        {
          id: 'mapbox-wmts-base-layer',
          type: 'raster',
          source: {
            type: 'raster',
            tiles: [
              `${process.env.VUE_APP_TIANDI_BASE}/img_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
            ],
            tileSize: 256,
          },
        },
        mapIns.getStyle().layers[0].id,
      )
      // 天地图标注
      mapIns.addLayer(
        {
          id: 'mapbox-wmts-label-layer',
          type: 'raster',
          source: {
            type: 'raster',
            tiles: [
              `${process.env.VUE_APP_TIANDI_BASE}/cia_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
            ],
            tileSize: 256,
          },
        },
        mapIns.getStyle().layers[1].id,
      )
      await this.addLineLayer(mapIns)
      await this.addAreaLayer(mapIns)
      this.$nextTick(() => {
        if (this.activeTab == 'river') {
          mapIns.setLayoutProperty('gq-paiShui', 'visibility', 'none');
          mapIns.setLayoutProperty('gq-shuiDongLi', 'visibility', 'visible');
          mapIns.setLayoutProperty('gq-paiShui-border', 'visibility', 'none');
        } else {
          mapIns.setLayoutProperty('gq-paiShui', 'visibility', 'visible');
          mapIns.setLayoutProperty('gq-shuiDongLi', 'visibility', 'none');
          mapIns.setLayoutProperty('gq-paiShui-border', 'visibility', 'visible');
        }
      })
    },

    async addAreaLayer(mapIns) {
      let hoveredPolygonId = null;
      await axios(paiShuiUrl).then(resp => {
        const self = this
        this.paiShuiShp = resp.data
        // mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
        mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
        mapIns.addSource('paiShuiSource', {
          type: 'geojson',
          data: resp.data, //区划的面数据
          'generateId': true // 确保所有特征都有唯一的ID
        })
        mapIns.addLayer({
          id: 'gq-paiShui',
          type: 'fill',
          source: "paiShuiSource",
          paint: {
            'fill-color': "rgba(255,255,255,0.5)", // blue color fill
            'fill-opacity': [
              'case',
              ['boolean', ['feature-state', 'hover'], false],
              1,
              0.4
            ]
          }
        })
        mapIns.addLayer({
          'id': 'gq-paiShui-border',
          'type': 'line',
          'source': 'paiShuiSource',
          'layout': {},
          'paint': {
            'line-color': '#507EF7',
            'line-width': 2
          }
        });

        mapIns.addInteraction('gq-paiShui-click-interaction', {
          type: 'click',
          target: { layerId: 'gq-paiShui' },
          handler: (e) => {
            const coordinates = turf.centroid(turf.polygon(e.feature.geometry.coordinates)).geometry.coordinates;
            const areaName = e.feature.properties.name;
            // "areaName": "大山圩闸片区集水区",
            // "area": 24.71,
            // "floodArea": 0.0,
            // "floodHour": 0.0,
            // "reductionRate": 0.0
            const target = this.areaTableData.find(ele => ele.areaName && ele.areaName.startsWith(areaName))
            let content
            if (target) {
              // 受涝 面积 平方米
              content = `<div class='outerPaiShui'>
                            <div class='innerTop'>${areaName}</div>
                            <div class='innerBottom'>${"排水量：" + target.waterDischarge + "万m³"}</div>
                            <div class='innerBottom'>${"最大淹没深度：" + target.maxDepth + "m"}</div>
                            <div class='innerBottom'>${"持续时间：" + target.floodHour + "h"}</div>
                            <div class='innerBottom'>${"涝灾减产率：" + target.reductionRate}</div>
                            </div>`
            } else {
              content = `<div class='outerPaiShui'>
                            <div class='innerTop'>${areaName}</div>
                               <div class='innerBottom'>数据为空！</div>
                            </div>`
            }

            new mapboxgl.Popup()
              .setLngLat(coordinates)
              .setHTML(content)
              .addTo(mapIns);
          }
        });
        mapIns.on('mousemove', 'gq-paiShui', (e) => {
          if (e.features.length > 0) {
            if (hoveredPolygonId !== null) {
              mapIns.setFeatureState(
                { source: 'paiShuiSource', id: hoveredPolygonId },
                { hover: false }
              );
            }
            hoveredPolygonId = e.features[0].id;
            mapIns.setFeatureState(
              { source: 'paiShuiSource', id: hoveredPolygonId },
              { hover: true }
            );
          }
        });
        mapIns.on('mouseleave', 'gq-paiShui', () => {
          if (hoveredPolygonId !== null) {
            mapIns.setFeatureState(
              { source: 'paiShuiSource', id: hoveredPolygonId },
              { hover: false }
            );
          }
          hoveredPolygonId = null;
        });
      })
    },
    async addLineLayer(mapIns) {
      let hoveredPolylineId = null;
      await axios(shuiDongLiUrl).then(resp => {
        this.shuiDongLiShp = resp.data
        resp.data.features.forEach((ele, index) => {
          ele.properties.color = "#00FF00"
        })
        // mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
        mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
        mapIns.addSource('shuiDongLiSource', {
          type: 'geojson',
          data: resp.data, //区划的面数据
          'generateId': true // 确保所有特征都有唯一的ID
        })
        mapIns.addLayer({
          'id': 'gq-shuiDongLi',
          'type': 'line',
          'source': 'shuiDongLiSource',
          'layout': {},
          'paint': {
            'line-color': ['get', 'color'],
            'line-width': [
              'case',
              ['boolean', ['feature-state', 'hover'], false],
              6,
              3
            ],
            'line-opacity': 1
          }
        });
        mapIns.addInteraction('gq-shuiDongLi-click-interaction', {
          type: 'click',
          target: { layerId: 'gq-shuiDongLi' },
          handler: (e) => {
            // Copy coordinates array.
            const coordinates = e.lngLat;
            const lineName = e.feature.properties.object_name;
            const target = this.riverTableData.find(ele => ele.projectName && ele.projectName.startsWith(lineName))
            let content
            if (target) {
              // 受涝 面积 平方米
              content = `<div class='outerPaiShui'>
                            <div class='innerTop'>${lineName}</div>
                            <div class='innerBottom'>${"最高水位：" + target.maxWlv + "m"}</div>
                            <div class='innerBottom'>${"最大超警戒水深：" + target.alertWlv + "m"}</div>
                            <div class='innerBottom'>${"超警戒河段长度：" + target.submergedLength + "km"}</div>
                            <div class='innerBottom'>${"超警戒持续时间：" + target.alertTime + "h"}</div>
                            </div>`
            } else {
              content = `<div class='outerPaiShui'>
                            <div class='innerTop'>${lineName}</div>
                               <div class='innerBottom'>数据为空！</div>
                            </div>`
            }

            new mapboxgl.Popup()
              .setLngLat(coordinates)
              .setHTML(content)
              .addTo(mapIns);
          }
        });
        mapIns.on('mousemove', 'gq-shuiDongLi', (e) => {
          if (e.features.length > 0) {
            if (hoveredPolylineId !== null) {
              mapIns.setFeatureState(
                { source: 'shuiDongLiSource', id: hoveredPolylineId },
                { hover: false }
              );
            }
            hoveredPolylineId = e.features[0].id;
            mapIns.setFeatureState(
              { source: 'shuiDongLiSource', id: hoveredPolylineId },
              { hover: true }
            );
          }
        });
        mapIns.on('mouseleave', 'gq-shuiDongLi', () => {
          if (hoveredPolylineId !== null) {
            mapIns.setFeatureState(
              { source: 'shuiDongLiSource', id: hoveredPolylineId },
              { hover: false }
            );
          }
          hoveredPolylineId = null;
        });
      })
    },
    // 获取模型列表
    async getModelList() {
      this.modelListLoading = true
      try {
        const response = await getSchedulingPage({
          pageNum: 1,
          pageSize: 20
        })

        if (response && response.data) {
          this.modelOptions = response.data.data.map(item => ({
            label: item.schedulingName,
            value: item.schedulingId
          }))
          // 如果有路由参数中的schedulingId，优先使用它
          if (this.routeSchedulingId) {
            this.modelName = this.routeSchedulingId
            await this.loadModelData(this.routeSchedulingId)
          } else if (this.modelOptions.length > 0) {
            // 否则默认选择第一个
            this.modelName = +this.$route.query.schedulingId || this.modelOptions[0].value
            await this.loadModelData(this.modelName)
          }
        } else {
          this.$message.warning('暂无模型数据')
        }
      } catch (error) {
        this.$message.error('获取模型列表失败，请重试')
      } finally {
        this.modelListLoading = false
      }
    },

    // 切换模型
    async handleModelChange(value) {
      if (value) {
        await this.loadModelData(value)
      }
    },

    // 加载模型数据
    async loadModelData(schedulingId) {
      if (!schedulingId) return

      try {
        // 并行调用统计接口和表格数据接口
        const [statisticsResponse] = await Promise.all([
          getEleVo({ schedulingId }),
          this.loadTableData(schedulingId)
        ])

        // 处理统计数据
        if (statisticsResponse && statisticsResponse.data) {
          const data = statisticsResponse.data
          this.currentModelData = {
            schedulingId: data.schedulingId,
            schedulingName: data.schedulingName,
            schedulingCode: data.schedulingCode
          }
          // 更新信息卡片数据
          this.infoCards = [
            { title: '预报总降雨量', value: data.sumRaun || '0', unit: 'mm' },
            { title: '田间总排水量', value: data.waterDischargeSum || '0', unit: '万m³' },
            { title: '最大淹没深度', value: data.maxDepth || '0', unit: 'm' },
            { title: '涝灾减产率', value: data.reductionRate || '0', unit: '%' },
            { title: '河道最高水位', value: data.maxWlv || '0', unit: 'm' },
            { title: '河道最大超警戒水深', value: data.maxAlertWlv || '0', unit: 'm' }
          ]
        } else {
          // 如果统计接口返回为空，设置默认值
          const selectedModel = this.modelOptions.find(item => item.value === schedulingId)
          this.currentModelData = {
            schedulingId: schedulingId,
            schedulingName: selectedModel ? selectedModel.label : '',
            schedulingCode: ''
          }

          // 重置信息卡片为默认值
          this.infoCards = [
            { title: '预报总降雨量', value: '0', unit: 'mm' },
            { title: '田间总排水量', value: '0', unit: '万m³' },
            { title: '最大淹没深度', value: '0', unit: 'm' },
            { title: '涝灾减产率', value: '0', unit: '%' },
            { title: '河道最高水位', value: '0', unit: 'm' },
            { title: '河道最大超警戒水深', value: '0', unit: 'm' }
          ]
        }
      } catch (error) {
        console.error('加载模型数据失败:', error)
        this.$message.error('加载模型数据失败，请重试')
      }
    },

    // 加载表格数据
    async loadTableData(schedulingId) {
      this.tableLoading = true
      try {
        // 并行调用两个接口
        const [areaResponse, riverResponse] = await Promise.all([
          getAreaEvaluateData({ schedulingId }),
          getRiverEvaluateData({ schedulingId })
        ])

        // 处理片区数据
        if (areaResponse && areaResponse.data) {
          this.areaTableData = Array.isArray(areaResponse.data) ? areaResponse.data : []
          setTimeout(() => {
            let tempShp = JSON.parse(JSON.stringify(this.paiShuiShp))
            tempShp.features.forEach((ele, index) => {
              ele.properties['has'] = false
            })
            this.areaTableData.forEach((item, index) => {
              tempShp.features.forEach((feature, i) => {
                if (item.areaName && feature.properties.name && item.areaName.startsWith(feature.properties.name)) {
                  feature.properties['has'] = true
                }
              })
            })
            tempShp.features = tempShp.features.filter(ele => ele.properties.has)
            mapBoundGeo(tempShp, this.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
            this.mapIns.getSource('paiShuiSource').setData(tempShp)
          }, 1000)
        } else {
          this.areaTableData = []
        }

        // 处理河道数据
        if (riverResponse && riverResponse.data) {
          this.riverTableData = Array.isArray(riverResponse.data) ? riverResponse.data : []
          setTimeout(() => {
            let tempShp = JSON.parse(JSON.stringify(this.shuiDongLiShp))
            tempShp.features.forEach((ele, index) => {
              ele.properties['has'] = false
              ele.properties['color'] = '#666666'
            })
            this.riverTableData.forEach((item, index) => {
              tempShp.features.forEach((feature, i) => {
                if (item.projectCode && feature.properties.id && item.projectCode.endsWith(feature.properties.id)) {
                  feature.properties['has'] = true
                  item.alertWlv = parseFloat(item.alertWlv)
                  if (item.alertWlv != 0 && (!item.alertWlv)) {
                    feature.properties['color'] = '#666666'
                  } else if (item.alertWlv < 0.3) {
                    feature.properties['color'] = '#00FF00'
                  } else if (item.alertWlv >= 0.3 && item.alertWlv < 0.39) {
                    feature.properties['color'] = '#FFFF00'
                  } else {
                    feature.properties['color'] = '#FF0000'
                  }
                }
              })
            })
            tempShp.features = tempShp.features.filter(ele => ele.properties.has)
            mapBoundGeo(tempShp, this.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
            this.mapIns.getSource('shuiDongLiSource').setData(tempShp)
          }, 1000)
        } else {
          this.riverTableData = []
        }
      } catch (error) {
        console.error('加载表格数据失败:', error)
        this.$message.error('加载表格数据失败，请重试')
        // 出错时清空数据
        this.areaTableData = []
        this.riverTableData = []
      } finally {
        this.tableLoading = false
      }
    },

    // 切换Tab
    handleTabChange(activeKey) {
      this.activeTab = activeKey
      if (this.activeTab == 'river') {
        this.mapIns.setLayoutProperty('gq-paiShui', 'visibility', 'none');
        this.mapIns.setLayoutProperty('gq-shuiDongLi', 'visibility', 'visible');
        this.mapIns.setLayoutProperty('gq-paiShui-border', 'visibility', 'none');
      } else {
        this.mapIns.setLayoutProperty('gq-paiShui', 'visibility', 'visible');
        this.mapIns.setLayoutProperty('gq-shuiDongLi', 'visibility', 'none');
        this.mapIns.setLayoutProperty('gq-paiShui-border', 'visibility', 'visible');
      }
    },

    // 获取图标源
    getIconSrc(index) {
      return this.iconMapping[index]
    },

    // 获取路由参数
    getRouteParams() {
      // 从路由参数中获取schedulingId
      if (this.$route.query && this.$route.query.schedulingId) {
        this.routeSchedulingId = Number(this.$route.query.schedulingId)
        this.routeSchedulingName = this.$route.query.schedulingName
      }
    }
  },

  async mounted() {
    // 先获取路由参数
    this.getRouteParams()
    // 然后获取模型列表
    await this.getModelList()
  },

  // 监听路由变化
  watch: {
    '$route.query.schedulingId': {
      handler(newVal) {
        if (newVal && Number(newVal) !== this.modelName) {
          this.routeSchedulingId = Number(newVal)
          this.modelName = Number(newVal)
          this.loadModelData(Number(newVal))
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="less" scoped>
.info-card {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  // background-color: #f9f9f9;
  border-radius: 4px;
  width: 15%;

  .info-icon {
    margin-right: 12px;
  }

  .info-content {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .info-title {
      line-height: 1;
      font-size: 14px;
      color: #4E5969;
    }

    .info-value-container {
      display: flex;
      align-items: flex-end;

      .info-value {
        font-size: 24px;
        font-weight: bold;
        color: #1D2129;
        line-height: 1;
      }

      .info-unit {
        margin-left: 2px;
        color: #1D2129;
        font-size: 12px;
        font-weight: 500;
        line-height: 1.5;
      }
    }
  }
}

.page-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.map-title {
  font-size: 16px;
  margin-bottom: 12px;
  padding: 12px 16px;
  border-radius: 4px;

  .area-name {
    color: #1D2129;
    font-size: 16px;
    font-weight: 600;
    display: inline;
  }

  .date-range {
    color: #1D2129;
    font-size: 14px;
    font-weight: 400;
    display: inline;
  }
}

:deep(.ant-tabs) {
  .ant-tabs-nav {
    margin-bottom: 10px;

    &::before {
      display: none; // 去掉底部边框线
    }

    .ant-tabs-tab {
      padding: 8px 16px;
      margin: 0 8px 0 0;
      background: #F2F3F5;
      border-radius: 4px;
      transition: all 0.3s;
      border: none; // 去掉边框
      border-bottom: none !important;

      .ant-tabs-tab-btn {
        color: #4E5969;
      }

      &.ant-tabs-tab-active {
        background: #1890ff;
        color: #ffffff;

        .ant-tabs-tab-btn {
          color: #ffffff;
        }
      }

      &:hover {
        background: #1890ff;

        .ant-tabs-tab-btn {
          color: #ffffff;
        }
      }
    }

    .ant-tabs-ink-bar {
      display: none !important; // 强制隐藏下划线
    }
  }
}

:deep(.ant-tabs-bar) {
  border-bottom: none !important;

}

::v-deep .outerPaiShui {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .innerTop {
    width: 200px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    background-color: rgb(83, 132, 254);
  }

  .innerBottom {
    // height: 40px;
    // line-height: 40px;
    // background-color: red;
    text-align: center;
    font-size: 11px;
    font-weight: 350;
    color: #000000;
    margin-top: 5px;
    // margin-bottom: 5px;
  }

  .innerBottom:last-child {
    margin-bottom: 5px;
  }

}

::v-deep .mapboxgl-popup-content {
  padding: 0px;

  .mapboxgl-popup-close-button {
    color: #fff;
  }
}
</style>
