<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      :maskClosable="false"
      modalWidth="1000"
      @cancel="cancel"
      modalHeight="888"
    >
      <div slot="content">
        <a-form-model ref="form" :model="form" :rules="rules">
          <a-row class="form-row" :gutter="32">
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item :label="form.typeMenu == 1 ? '新闻标题' : '活动标题'" prop="newsName">
                <a-input
                  allowClear
                  v-model="form.newsName"
                  :maxLength="100"
                  show-word-limit
                  @change="handleInputChange"
                  placeholder="请输入"
                />
                <div class="character-count">{{ currentLength }} / 100</div>
              </a-form-model-item>
            </a-col>
            <a-col :lg="24" :md="24" :sm="24">
              <div class="summary-tip">“首页摘要”中内容仅在首页的新闻模块进行展示。</div>
              <a-form-model-item label="首页摘要">
                <a-input
                  allowClear
                  v-model="form.summary"
                  :maxLength="100"
                  show-word-limit
                  @change="handleSummaryChange"
                  placeholder="请输入"
                />
                <div class="character-count">{{ currentSummaryLength }} / 100</div>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item :label="form.typeMenu == 1 ? '新闻类别' : '活动类别'" prop="type">
                <a-select allowClear v-model="form.type" placeholder="请选择" :options="typeOptions"></a-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24" v-if="form.typeMenu == 1">
              <a-form-model-item label="发表时间">
                <a-date-picker
                  v-model="form.startDate"
                  :show-time="{ format: 'HH:mm:ss' }"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="12" :md="12" :sm="24" v-if="form.typeMenu == 2">
              <a-form-model-item label="活动开始时间">
                <a-date-picker
                  v-model="form.startDate"
                  :show-time="{ format: 'HH:mm:ss' }"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  :disabled-date="disabledStartDate"
                  @openChange="handleStartOpenChange"
                  placeholder="请选择"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" v-if="form.typeMenu == 2">
              <a-form-model-item label="活动结束时间">
                <a-date-picker
                  v-model="form.endDate"
                  :show-time="{ format: 'HH:mm:ss' }"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  :disabled-date="disabledEndDate"
                  @openChange="handleEndOpenChange"
                  :open="endOpen"
                  placeholder="请选择"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" v-if="form.typeMenu == 2">
              <a-form-model-item label="活动状态">
                <a-select allowClear v-model="form.state" placeholder="请选择" :options="stateOptions"></a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24" v-if="form.typeMenu == 2">
              <a-form-model-item label="活动部门">
                <a-tree-select
                  v-model="form.deptIds"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="deptOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'deptName',
                    key: 'deptId',
                    value: 'deptId',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24">
              <div class="upload-tip">仅限1张图片，png、jpg、jpeg格式，10M以内，建议图片比例：16:9。</div>
              <a-form-model-item label="封面图" prop="coverImage">
                <UploadFile
                  :fileUrl.sync="form.coverImage"
                  :multiple="false"
                  listType="picture-card"
                  folderName="projectCover"
                />
              </a-form-model-item>
            </a-col>

            <a-col :lg="24" :md="24" :sm="24" :span="24">
              <div class="pub-content">
                <a-form-model-item label="发布内容" prop="context">
                  <TinyMCE
                    v-if="isTinyMCE"
                    class="setTinyMce"
                    :height="188"
                    v-model="form.context"
                    ref="tinyMceRef"
                  ></TinyMCE>
                </a-form-model-item>
              </div>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" v-if="this.previewEditType == 1" @click="submitForm(1)" :loading="loading">
          预览
        </a-button>
        <a-button type="primary" v-if="this.previewEditType == 2" @click="submitForm(2)" :loading="loading">
          预览
        </a-button>
      </template>
    </ant-modal>
    <FormModalPreview
      v-if="showFormPreview"
      :typeOptions="typeOptions"
      :stateOptions="stateOptions"
      :deptOptions="deptOptions"
      ref="formPreviewRef"
      @ok="onOperationComplete"
      @close="showFormPreview = false"
    />
  </div>
</template>
<script lang="jsx">
  import { addNews, updateNews, getNews } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import TinyMCE from '@/components/TinyMCE/index.vue'
  import moment from 'moment'
  import FormModalPreview from './FormModalPreview.vue'

  export default {
    name: 'FormModal',
    components: { AntModal, UploadFile, TinyMCE, FormModalPreview },
    props: ['typeOptions', 'stateOptions', 'deptOptions'],
    data() {
      return {
        endOpen: false,
        updateInterval: null,
        isTinyMCE: false,
        showFormPreview: false,
        loading: false,
        modalLoading: false,
        currentLength: 0,
        currentSummaryLength: 0,
        formTitle: '',
        type: 0,
        editType: 0,
        previewEditType: 0,
        form: {
          isSave: 0,
          status: undefined,

          context: '',
          coverImage: null,
          deptIds: null,
          endDate: null,
          newsName: '',
          startDate: null,
          state: null,
          summary: '',
          type: undefined,
          typeMenu: 1,
        },
        open: false,
        rules: {
          context: [{ required: true, message: '发布内容不能为空', trigger: 'blur' }],
          newsName: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
          type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
          coverImage: [{ required: true, message: '附件不能为空', trigger: 'change' }],
        },
      }
    },
    created() {
      this.form.typeMenu = this.$route.meta.query?.type
    },
    mounted() {},
    beforeDestroy() {
      // 组件销毁前清除定时器
      if (this.updateInterval) {
        clearInterval(this.updateInterval)
      }
    },
    methods: {
      disabledStartDate(startValue) {
        const endValue = moment(this.form.endDate)
        if (!startValue || !endValue) {
          return false
        }
        return startValue.valueOf() > endValue.valueOf()
      },
      disabledEndDate(endValue) {
        const startValue = moment(this.form.startDate)
        if (!endValue || !startValue) {
          return false
        }
        return startValue.valueOf() >= endValue.valueOf()
      },
      handleStartOpenChange(open) {
        if (!open) {
          this.endOpen = true
        }
      },
      handleEndOpenChange(open) {
        this.endOpen = open
      },
      handleInputChange(value) {
        // 更新当前输入的字符长度
        this.currentLength = this.form.newsName.length
      },
      handleSummaryChange(value) {
        // 更新当前输入的字符长度
        this.currentSummaryLength = this.form.summary.length
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      onClick(e) {
        // console.log('.... e', e)
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      remove() {
        this.$refs.tinyMceRef.remove()
      },
      /** 新增按钮操作 */
      handle(row, previewEditType) {
        this.open = true
        this.formTitle = '新增'

        this.previewEditType = previewEditType == 3 ? 2 : previewEditType
        if (row != undefined) {
          this.formTitle = '修改'
          this.currentLength = row.newsName.length
          this.currentSummaryLength = row.summary.length
          if (row.newsId && row.editType == 2) {
            this.type = 2

            this.form = row
            this.$nextTick(() =>
              setTimeout(() => {
                this.form = row
                this.form.type = String(this.form.type)
              }, 200),
            )
          } else {
            this.form = row
            this.$nextTick(() =>
              setTimeout(() => {
                this.form = row
                this.form.type = String(this.form.type)
                this.form.status = 0
                this.type = 1
              }, 200),
            )
          }
        } else {
          this.form.status = 0
          this.type = 1
        }
        //显示tinyMce组件
        setTimeout(() => {
          this.$nextTick(() => {
            this.isTinyMCE = true
          })
        }, 200)
      },
      // 操作完成后
      onOperationComplete() {
        this.$emit('ok')
      },
      /** 提交按钮 */
      submitForm(type) {
        // console.log('start 预览跳转', type)

        this.$refs.form.validate(valid => {
          if (valid) {
            this.open = false
            this.isTinyMCE = false
            this.showFormPreview = true
            this.form.isSave = 2

            this.$nextTick(() =>
              setTimeout(() => {
                this.$refs.formPreviewRef.handle(this.form, type)
              }, 200),
            )
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .character-count {
    position: relative;
    // display: block;
    width: 80px;
    float: right;
    text-align: right;
    margin-right: 20px;
    margin-top: -26px;
    // background: red;
  }

  .summary-tip {
    position: absolute;
    top: 5px;
    left: 86px;
    width: 330px;
    height: 16px;
  }
  .upload-tip {
    position: absolute;
    top: 5px;
    left: 76px;
    width: 440px;
    height: 16px;
    // background: yellowgreen;
  }
  .pub-content {
    width: 100%;
    height: 200px;
    // background: greenyellow;
    .setTinyMce {
      width: 100%;
      height: 200px;
      // background: blueviolet;
    }
  }
</style>
