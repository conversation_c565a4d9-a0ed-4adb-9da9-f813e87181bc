import request from '@/utils/request'

// 水利对象分类-根据编码获取树--水系
export function getTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'RL' },
  })
}

// 水利对象分类-根据编码获取树--站点类别
export function getSideTreeByCode(params) {
  return request({
    url: '/war/history/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// export function getSideTreeByCode(params) {
//   return request({
//     url: '/base/objectCategory/getTreeByCode',
//     method: 'post',
//     headers: {
//       'Content-Type': 'application/x-www-form-urlencoded'
//     },
//     params: { ...params, objectCategoryCode: 'MS' }
//   })
// }

// 列表
export function getRealTimeList(data) {
  return request({
    url: '/war/realTime/list',
    method: 'post',
    data,
  })
}
//排序
export function updateRealTimeSort(data) {
  return request({
    url: '/war/realTime/updateSort',
    method: 'post',
    data,
  })
}
//置顶
export function setRealTimeTop(params) {
  return request({
    url: '/war/realTime/isTop',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
