<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane key="3" tab="设备类别">
          <TreeGeneral
            v-if="treeTabKey === '3'"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="deviceCategoryOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'deviceCategory')"
          />
        </a-tab-pane>
        <a-tab-pane key="1" tab="工程">
          <TreeGeneral
            v-if="treeTabKey === '1'"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="treeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'category')"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="行政区划">
          <TreeGeneral
            v-if="treeTabKey === '2'"
            :key="2"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="districtTreeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'district')"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="设备编码">
          <a-input
            v-model="queryParam.deviceCode"
            placeholder="请输入设备编码"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="设备名称">
          <a-input
            v-model="queryParam.deviceName"
            placeholder="请输入设备名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="外部标识">
          <a-input
            v-model="queryParam.outerId"
            placeholder="请输入外部标识"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" @click="handleExport" icon="download" :loading="exportLoading">导出</a-button>
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>

    <FormDrawer
      v-if="showFormDrawer"
      ref="formDrawerRef"
      :districtOptions="districtTreeOptions.dataSource"
      :projectOptions="projectOptions"
      :deviceCategoryOptions="deviceCategoryOptions.dataSource"
      @ok="onOperationComplete"
      @close="showFormDrawer = false"
    />

    <ProjectModal ref="ProjectModalRef" v-if="showProjectModal" @close="showProjectModal = false" />
  </div>
</template>

<script lang="jsx">
  import { getDevicePage, deleteDevice } from './services'
  import { getDistrictTree, getProjectTree, getProjectCategoryTree, getDeviceCategoryTree } from '@/api/common'

  import FormDrawer from './modules/FormDrawer'
  import TreeGeneral from '@/components/TreeGeneral'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'
  import ProjectModal from '@/views/project/hydraulic-project/modules/DetailModal/index.vue'

  export default {
    name: 'EquipmentManage',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      FormDrawer,
      ProjectModal,
    },
    data() {
      return {
        treeTabKey: '3',
        exportLoading: false,
        treeOptions: {
          getDataApi: getProjectCategoryTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'key',
          },
        },
        deviceCategoryOptions: {
          getDataApi: getDeviceCategoryTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'deviceCategoryName',
            key: 'deviceCategoryId',
            value: 'deviceCategoryId',
          },
        },
        districtTreeOptions: {
          getDataApi: getDistrictTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'districtName',
            key: 'districtCode',
            value: 'districtCode',
          },
        },
        showFormDrawer: false,
        projectOptions: [],
        showProjectModal: false,

        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          treeNodeId: undefined,
          treeNodeType: undefined,
          deviceCode: undefined,
          deviceName: undefined,
          districtCode: undefined,
          outerId: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        districtTypes: [],
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '设备编码',
            field: 'deviceCode',
            minWidth: 210,
            showOverflow: 'tooltip',
          },
          {
            title: '设备名称',
            field: 'deviceName',
            minWidth: 180,
          },
          {
            title: '设备分类',
            field: 'deviceCategoryName',
            minWidth: 140,
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 180,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return <a onClick={() => this.handleProject(row)}>{row.projectName}</a>
              },
            },
          },
          {
            title: '外部标识',
            field: 'outerId',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 140,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      getDistrictTree().then(res => {
        this.districtTreeOptions.dataSource = res.data
        this.districtTypes = getFlatTreeMap(res.data, 'districtCode')
      })
    },
    computed: {},
    watch: {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        // 根据树默认选中id来判断
        if (this.queryParam.treeNodeId || this.queryParam.districtCode || this.queryParam.deviceCategoryId) {
          this.getList()
        }
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getDevicePage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          deviceCode: undefined,
          deviceName: undefined,
          outerId: undefined,

          pageNum: 1,
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.deviceId)
        this.names = valObj.records.map(item => item.deviceName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 树加载完成后
      onTreeMounted(data) {
        if (this.treeTabKey === '1') {
          this.queryParam.treeNodeId = data[0].key.substr(1, data[0].key.length)
          this.queryParam.treeNodeType = data[0].type
          this.queryParam.districtCode = ''
          this.queryParam.deviceCategoryId = ''
          this.tableTitle = data[0].name
          this.treeOptions.dataSource = data
        } else if (this.treeTabKey === '2') {
          this.queryParam.treeNodeId = ''
          this.queryParam.treeNodeType = ''
          this.queryParam.deviceCategoryId = ''
          this.queryParam.districtCode = data[0].districtCode
          this.tableTitle = data[0].districtName
        } else if (this.treeTabKey === '3') {
          this.queryParam.treeNodeId = ''
          this.queryParam.treeNodeType = ''
          this.queryParam.districtCode = ''
          this.queryParam.deviceCategoryId = data[0].deviceCategoryId
          this.tableTitle = data[0].deviceCategoryName
          this.deviceCategoryOptions.dataSource = data
        }

        // 获取工程树
        getProjectTree({ objectCategoryId: this.queryParam.treeNodeId }).then(res => {
          this.projectOptions = res.data
        })
        this.getList()
      },
      clickTreeNode(node, type) {
        if (type === 'category') {
          const key = node.$options.propsData.dataRef.key
          this.queryParam.treeNodeId = key.substr(1, key.length)
          this.queryParam.treeNodeType = node.$options.propsData.dataRef.type
        } else if (type === 'district') {
          this.queryParam.districtCode = node.$options.propsData.eventKey
        } else if (type === 'deviceCategory') {
          this.queryParam.deviceCategoryId = node.$options.propsData.eventKey
        }

        this.tableTitle = node.$options.propsData.dataRef.title
        this.queryParam.pageNum = 1
        this.getList()
      },

      /* 查看工程 */
      handleProject(record) {
        this.showProjectModal = true
        this.$nextTick(() => this.$refs.ProjectModalRef.handleDetail(record))
      },

      /* 新增 */
      handleAdd() {
        this.showFormDrawer = true
        if (this.treeTabKey === '1') {
          this.$nextTick(() =>
            this.$refs.formDrawerRef.handleAdd(
              {
                objectCategoryId: this.queryParam.objectCategoryId,
                treeNodeId: this.queryParam.treeNodeType === 'category' ? undefined : this.queryParam.treeNodeId,
              },
              '1',
            ),
          )
        } else if (this.treeTabKey === '2') {
          this.$nextTick(() => this.$refs.formDrawerRef.handleAdd({ districtCode: this.queryParam.districtCode }, '2'))
        } else if (this.treeTabKey === '3') {
          this.$nextTick(() =>
            this.$refs.formDrawerRef.handleAdd({ deviceCategoryId: this.queryParam.deviceCategoryId }, '3'),
          )
        }
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const deviceIds = row.deviceId ? [row.deviceId] : this.ids
        const names = row.deviceName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteDevice({ deciveIds: deviceIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        getDevicePage({ ...this.queryParam, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
          this.exportLoading = false

          const columnsList = [
            {
              title: '设备编码',
              field: 'deviceCode',
              minWidth: 210,
            },
            {
              title: '设备名称',
              field: 'deviceName',
              minWidth: 180,
            },
            {
              title: '设备简称',
              field: 'deviceNameAbbr',
              minWidth: 180,
            },
            {
              title: '设备分类',
              field: 'deviceCategoryName',
              minWidth: 140,
            },
            {
              title: '行政区划',
              field: 'deviceCategoryName',
              minWidth: 140,
            },
            {
              title: '所属工程',
              field: 'projectName',
              minWidth: 140,
            },
            {
              title: '备注',
              field: 'remark',
              minWidth: 140,
            },
          ]

          const data = (res.data?.data || []).map(el => ({
            ...el,
            districtCode: this.districtTypes[el.districtCode]?.districtName || '',
          }))

          excelExport(columnsList, data, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
