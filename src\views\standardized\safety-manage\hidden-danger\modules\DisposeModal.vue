<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="600"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="处置反馈" prop="remark">
              <a-textarea
                v-model="form.remark"
                placeholder="请输入"
                allow-clear
                :auto-size="{ minRows: 3, maxRows: 3 }"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <a-form-model-item label="处置照片">
              <UploadFile
                :fileUrl.sync="form.attaches"
                :multiple="true"
                listType="picture-card"
                folderName="safety-manage"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { disposeHidden } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormModal',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'hiddenTypeOptions', 'userOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        formTitle: '',
        open: false,
        form: {
          hiddenId: undefined,
          remark: undefined,
          attaches: [],
        },
        rules: {
          remark: [{ required: true, message: '隐患描述不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        if (row != undefined) {
          this.form.hiddenId = row?.hiddenId
          this.formTitle = '处置'
        }
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            const params = {
              ...this.form,
            }

            this.loading = true

            disposeHidden(params)
              .then(res => {
                if (res.code == 200) {
                  this.$message.success('处置成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                }
              })
              .finally(() => (this.loading = false))
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
