<template>
  <div class="container">
    <a-card :bordered="false" style="margin-bottom: 10px">
      <div class="table-page-search-wrapper" style="padding-left: 20px; padding-right: 20px">
        <a-form layout="inline">
          <a-row :gutter="40">
            <a-col :span="24">
              <a-form-item label="">
                <a-radio-group v-model="siteTerminalVal" @change="onTabChange">
                  <a-radio-button v-for="(item, i) in siteTerminalData" :key="i" :value="item.indexCode">
                    {{ item.name }}
                  </a-radio-button>
                </a-radio-group>
              </a-form-item>
            </a-col>

            <a-col :span="5">
              <a-form-item label="选择时间">
                <a-select v-model="timeRangeVal" :options="timeRangesOptions" />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="" v-if="timeRangeVal !== '2'">
                <div class="time-picker">
                  <a-date-picker
                    style="width: 30%"
                    v-model="queryParam.startDate"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :allowClear="false"
                  />
                  <a-select :options="hoursOptions" v-model="queryParam.startHour" style="width: 15%" />
                  <a-date-picker
                    style="width: 30%"
                    v-model="queryParam.endDate"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :allowClear="false"
                  />
                  <a-select :options="hoursOptions" v-model="queryParam.endHour" style="width: 15%" />
                </div>
              </a-form-item>

              <a-form-item label="" v-if="timeRangeVal == '2'">
                <div class="time-picker">
                  <a-range-picker
                    v-model="queryParam.rangeDate"
                    style="width: 100%"
                    format="YYYY-MM-DD"
                    :allowClear="false"
                    valueFormat="YYYY-MM-DD"
                  />
                </div>
              </a-form-item>
            </a-col>

            <a-col :span="7">
              <span class="table-page-search-submitButtons">
                <a-button type="primary" icon="search" @click="this.handleQuery">查询</a-button>
                <a-button style="margin-left: 8px" icon="redo" @click="this.resetQuery">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>

    <main class="content">
      <div class="info-bar">
        <div class="title">{{ site.name }}</div>
        <div class="terminals">
          <a-radio-group v-model="terminalVal" @change="handleQuery">
            <a-radio-button v-for="(item, i) in terminalsOptions" :key="i" :value="item.terminalId">
              {{ item.terminalName }}
            </a-radio-button>
          </a-radio-group>
        </div>

        <Infos
          v-if="!!statisticData"
          :siteTerminalData="siteTerminalData"
          :indexCode="siteTerminalVal"
          :dataSource="statisticData"
          :source="siteTerminalVal"
          :multiSourceOptions="multiSourceOptions"
          :waterQualityName="waterQualityName"
        />

        <a-button
          style="margin-left: 8px"
          type="primary"
          icon="download"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出
        </a-button>
      </div>

      <AllChart
        v-if="siteTerminalVal == 'all'"
        :siteTerminalData="siteTerminalData"
        :key="siteTerminalVal"
        :dataSource="list"
      />
      <WaterLevelChart v-if="siteTerminalVal == 'waterLevel'" :key="siteTerminalVal" :dataSource="list" />
      <RainfallChart v-if="siteTerminalVal == 'rainfall'" :key="siteTerminalVal" :dataSource="list" />
      <FlowChart v-if="siteTerminalVal == 'flow'" :key="siteTerminalVal" :dataSource="list" />
      <MultiSourceChart
        v-if="
          siteTerminalVal !== 'waterLevel' &&
          siteTerminalVal !== 'rainfall' &&
          siteTerminalVal !== 'flow' &&
          siteTerminalVal !== 'all'
        "
        :key="siteTerminalVal"
        :source="siteTerminalVal"
        :multiSourceOptions="multiSourceOptions"
        :dataSource="list"
      />

      <Table
        ref="tableRef"
        :siteTerminalData="siteTerminalData"
        :indexCode="siteTerminalVal"
        :loading="loading"
        :list="tableList"
        :source="siteTerminalVal"
        :multiSourceOptions="multiSourceOptions"
      />
    </main>
  </div>
</template>

<script lang="jsx">
  import { siteTerminal, allList, rainWaterList, statistics, multiSourceList } from './services'
  import { timeRangesOptions, hoursOptions, chartColors } from './config.js'
  import Infos from './infos.vue'
  import AllChart from './charts/allChart.vue'
  import WaterLevelChart from './charts/waterLevelChart.vue'
  import MultiSourceChart from './charts/multiSourceChart.vue'
  import RainfallChart from './charts/rainfallChart.vue'
  import FlowChart from './charts/flowChart.vue'
  import Table from './table.vue'
  import moment from 'moment'
  import { dealNumber, getFixedNum } from '@/utils/dealNumber.js'
  import excelExport from '@/utils/excelExport.js'
  import { getOptions } from '@/api/common'

  export default {
    name: 'RainWaterLine',
    components: {
      Infos,
      AllChart,
      MultiSourceChart,
      WaterLevelChart,
      RainfallChart,
      FlowChart,
      Table,
    },
    props: {
      site: {
        type: Object,
        default: () => ({ key: 12, name: '测试站点' }),
      },
    },
    data() {
      return {
        waterQualityName: '',
        multiSourceOptions: {}, //拼接指标数据
        exportLoading: false,
        siteTerminalData: [],
        siteTerminalVal: undefined,
        timeRangesOptions: timeRangesOptions,
        timeRangeVal: timeRangesOptions[1].value,
        hoursOptions: hoursOptions,

        terminalsOptions: [],
        terminalVal: undefined,

        statisticData: null,

        tableKey: 1,
        loading: false,
        list: [],

        queryParam: {
          siteId: this.site.key || undefined,
          startDate: undefined,
          startHour: undefined,
          endDate: moment().format('YYYY-MM-DD'),
          endHour: moment().format('HH:00:00'),
          rangeDate: [moment().subtract(1, 'months'), moment()],
        },
      }
    },
    computed: {
      tableList() {
        return JSON.parse(JSON.stringify(this.list)).reverse()
      },
    },
    watch: {
      site: {
        handler(newVal, oldVal) {
          if (newVal.icon === 'MS001_SL') {
            this.timeRangesOptions = [{ label: '逐日', value: '2' }]
            this.timeRangeVal = '2'
          } else {
            this.timeRangesOptions = timeRangesOptions
            this.timeRangeVal = '1'
          }

          this.tableKey += 1
          this.queryParam.siteId = newVal.key
          this.getSiteTerminal('update')
        },
        deep: true,
      },
    },
    created() {
      getOptions('monitoringIndex').then(res => {
        this.terminals = res.data?.map(el => ({ label: el.value, value: el.key, option1: el.option1 }))

        this.$nextTick(() => {
          setTimeout(() => {
            this.multiSourceOptions = this.mergedObj(this.terminals)
            this.resetTime()
            this.getSiteTerminal()
          }, 300)
        })
      })
    },
    methods: {
      //拼接
      mergedObj(list) {
        const obj = {}
        // 遍历List数组
        list?.forEach((item, index) => {
          // 使用模运算符来循环获取颜色（如果chartColors长度小于List长度）
          const colorIndex = index % chartColors.length
          const color = chartColors[colorIndex]
          // 从item中提取所需的值
          const { label, value, option1 } = item
          // const label = value // 假设label和value相同，或者你可以根据需要修改
          const unit = option1 ? `(${option1})` : '' // 如果option1存在，则作为单位，否则为空字符串

          // 将数据添加到obj中
          obj[value] = {
            label,
            value, // 假设你希望value是key（根据你的数据结构可能有所不同）
            unit,
            unit2: option1,
            color,
          }
        })
        return obj
      },
      /** 查询列表 */
      getList(isExport, callback) {
        if (!isExport) this.loading = true
        if (this.siteTerminalVal == 'all') {
          const params = {
            siteId: this.queryParam.siteId,
            startTime: `${this.queryParam.startDate} ${this.queryParam.startHour}`,
            endTime: `${this.queryParam.endDate} ${this.queryParam.endHour}`,
            indexCodes: this.siteTerminalData.filter(el => el.indexCode != 'all').map(el => el.indexCode),
            type: this.timeRangeVal,
            rangeDate: undefined,
          }
          if (this.timeRangeVal == '2') {
            params.startTime = moment(this.queryParam.rangeDate[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
            params.endTime = moment(this.queryParam.rangeDate[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
          }
          allList(params).then(res => {
            const list = (res.data || []).map(el => {
              return {
                ...el,
                rain: getFixedNum(el.rain, 1),
                sumRain: getFixedNum(el.sumRain, 1),
                waterLevel: getFixedNum(el.waterLevel, 2),
                flow: dealNumber(el.flow, 3),
                flowWaterLevel: getFixedNum(el.flowWaterLevel, 2),
              }
            })
            if (isExport) {
              callback(list)
            } else {
              this.list = list
              this.loading = false
            }
          })
        } else {
          const params = {
            siteId: this.queryParam.siteId,
            type: this.timeRangeVal,
            startTime: `${this.queryParam.startDate} ${this.queryParam.startHour}`,
            endTime: `${this.queryParam.endDate} ${this.queryParam.endHour}`,
            indexCodes: [this.siteTerminalVal],
            terminalId: this.terminalVal,
            rangeDate: undefined,
          }

          if (this.timeRangeVal == '2') {
            params.startTime = moment(this.queryParam.rangeDate[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
            params.endTime = moment(this.queryParam.rangeDate[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
          }

          if (this.isOneOfRequiredCodes(this.siteTerminalVal)) {
            rainWaterList(params).then(res => {
              const list = (res.data || []).map(el => {
                return {
                  ...el,
                  rain: getFixedNum(el.rain, 1),
                  sumRain: getFixedNum(el.sumRain, 1),
                  waterLevel: getFixedNum(el.waterLevel, 2),
                  flow: dealNumber(el.flow, 3),
                  flowWaterLevel: getFixedNum(el.flowWaterLevel, 2),
                }
              })
              if (isExport) {
                callback(list)
              } else {
                this.list = list
                this.loading = false
              }
            })
          } else if (!this.isOneOfRequiredCodes(this.siteTerminalVal)) {
            if (isExport) {
              callback(this.list)
            } else {
              this.loading = false
            }
          }
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getStatistics()
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetTime()
        this.queryParam = {
          ...this.queryParam,
          endDate: moment().format('YYYY-MM-DD'),
          endHour: moment().format('HH:00:00'),
        }

        this.handleQuery()
      },
      //判断是否是 雨量和水位和流量 之外的指标，不是的话，调用综合监测接口
      isOneOfRequiredCodes(siteTerminalVal) {
        const requiredCodes = ['all', 'waterLevel', 'rainfall', 'flow']
        return requiredCodes.includes(siteTerminalVal)
      },
      //判断是否存在 雨量和水位 或 雨量和流量
      hasAtLeastOnePair(list) {
        const requiredPairs = [
          ['waterLevel', 'rainfall'],
          ['rainfall', 'flow'],
        ]
        const hasPair = requiredPairs.some(pair => {
          return pair.every(code => list.some(item => item.indexCode === code))
        })
        return hasPair
      },
      getSiteTerminal(type) {
        siteTerminal({ siteId: this.site.key }).then(res => {
          this.siteTerminalData = res.data
            .filter(el => this.terminals.some(ele => ele.value == el.indexCode))
            .map(el => {
              let obj = this.terminals.find(ele => ele.value == el.indexCode)
              return { ...el, name: obj.label }
            })
          let isAll = this.hasAtLeastOnePair(this.siteTerminalData)

          if (isAll) {
            this.siteTerminalData.unshift({
              name: '全部',
              indexCode: 'all',
            })
          }

          if (this.siteTerminalData?.length === 0) {
            this.list = []
            return
          }

          this.siteTerminalVal = this.siteTerminalData[0].indexCode

          if (this.siteTerminalData?.length >= 1) {
            this.onTabChange({ target: { value: this.siteTerminalData[0].indexCode } })
          }

          if (!!type && type == 'update') {
            this.resetQuery()
          } else {
            this.handleQuery()
          }
        })
      },
      getStatistics() {
        const params = {
          siteId: this.queryParam.siteId,
          type: this.timeRangeVal,
          startTime: `${this.queryParam.startDate} ${this.queryParam.startHour}`,
          endTime: `${this.queryParam.endDate} ${this.queryParam.endHour}`,
          terminalId: this.siteTerminalVal == 'all' ? undefined : this.terminalVal,
          indexCodes:
            this.siteTerminalVal == 'all'
              ? this.siteTerminalData.filter(el => el.indexCode !== 'all').map(el => el.indexCode)
              : [this.siteTerminalVal],
        }
        const params2 = {
          type: this.timeRangeVal,
          startTime: `${this.queryParam.startDate} ${this.queryParam.startHour}`,
          endTime: `${this.queryParam.endDate} ${this.queryParam.endHour}`,
          terminalId: this.siteTerminalVal == 'all' ? undefined : this.terminalVal,
          indexCode: this.siteTerminalVal == 'all' ? undefined : this.siteTerminalVal,
        }
        if (this.timeRangeVal == '2') {
          params.startTime = moment(this.queryParam.rangeDate[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
          params.endTime = moment(this.queryParam.rangeDate[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
          params2.startTime = moment(this.queryParam.rangeDate[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
          params2.endTime = moment(this.queryParam.rangeDate[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
        }
        if (this.isOneOfRequiredCodes(this.siteTerminalVal)) {
          statistics(params).then(res => {
            this.statisticData = res?.data || {}
          })
        } else if (!this.isOneOfRequiredCodes(this.siteTerminalVal)) {
          this.getMultiSourceList(params2)
        }
      },
      getMultiSourceList(params) {
        multiSourceList(params).then(res => {
          this.list = res?.data?.dwDates || []
          this.loading = false
          // console.log('***** 434 site', res?.data?.dwDates[0])
          this.waterQualityName = res?.data?.dwDates?.[0]?.waterQualityName
          this.statisticData = res?.data?.countDates || {}
        })
      },
      resetTime() {
        if (this.timeRangeVal == '2') {
          this.queryParam.rangeDate = [moment().subtract(1, 'months'), moment()]
        } else {
          // const eightTime = moment(`${moment().format('YYYY-MM-DD')} 08:00:00`).valueOf()
          // if (moment().valueOf() > eightTime) {
          //   this.queryParam.startDate = moment().format('YYYY-MM-DD')
          // } else {
          //   this.queryParam.startDate = moment().subtract(1, 'day').format('YYYY-MM-DD')
          // }
          this.queryParam.startDate = moment().subtract(12, 'hour').format('YYYY-MM-DD')
          this.queryParam.startHour = moment().subtract(12, 'hour').format('HH:00:00')
        }
      },

      onTabChange(e) {
        this.terminalsOptions = this.siteTerminalData.find(el => el.indexCode == e.target.value)?.terminals || []
        if (this.terminalsOptions.length > 0 && e.target.value !== 'all') {
          this.terminalVal = this.terminalsOptions[0].terminalId
        } else {
          if (e.target.value !== 'all') {
            this.list = []
            return
          }
        }

        this.handleQuery()
      },

      handleExport() {
        this.exportLoading = true
        this.getList(true, data => {
          this.exportLoading = false
          excelExport(
            this.$refs.tableRef.columns.slice(1, this.$refs.tableRef.columns.length),
            data,
            `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`,
          )
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .container {
    height: 100%;
    display: flex;
    flex-direction: column;
    .time-picker {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .content {
      flex: 1;
      background-color: #fff;
      border-radius: 2px;
      display: flex;
      flex-direction: column;

      .info-bar {
        display: flex;
        align-items: center;
        padding: 10px 16px;

        .title {
          font-size: 16px;
          font-weight: 700;
          margin-right: 16px;
        }
      }
    }
  }
</style>
