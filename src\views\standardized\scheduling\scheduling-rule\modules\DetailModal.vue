<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-row :gutter="32">
        <a-col :lg="24" :md="24" :sm="24">
          <div class="item">
            <label class="common-label-text">调度规则名称：</label>
            <span class="common-value-text">
              {{ data.controlName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">年份：</label>
            <span class="common-value-text">
              {{ data.year }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">启用时间：</label>
            <span class="common-value-text">
              {{ data.enableTime }}
            </span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">
              {{ data.projectName }}
            </span>
          </div>
        </a-col>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div>
            <label class="common-label-text">附件：</label>
            <div
              class="file-item"
              v-for="(el, i) in data.positionAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" :title="el.attachName" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getSchedulingPlan } from '../services'
  import { getOptions } from '@/api/common'
  import { getTraining } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'DetailModal',
    props: ['statusImplementationOptions'],
    components: { AntModal },
    data() {
      return {
        modalLoading: false,
        open: false,
        modalTitle: '',
        data: {},
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 查看按钮操作 */
      handleDetail(record) {
        this.open = true
        this.modalLoading = true
        this.modalTitle = '查看'

        getSchedulingPlan({ controlPlanId: record.controlPlanId }).then(res => {
          this.data = res.data
          this.modalLoading = false
        })
      },
      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
</style>
