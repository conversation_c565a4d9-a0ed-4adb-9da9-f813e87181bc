<template>
  <div class="common-table-page" :style="isDetail ? { height: '100%' } : {}">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-model-item label="工程名称">
        <a-tree-select
        :disabled="isDetail"
          v-model="queryParam.projectId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item>

      <a-form-item label="年度">
        <a-date-picker
          mode="year"
          format="YYYY"
          v-model="queryParam.year"
          placeholder="请选择"
          allow-clear
          style="width: 100%"
          :open="yearShowOne"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
          @keyup.enter.native="handleQuery"
        ></a-date-picker>
      </a-form-item>
      <a-form-item label="审核状态">
        <a-select show-search allowClear placeholder="请选择" v-model="queryParam.status" option-filter-prop="children">
          <a-select-option v-for="item in statusOptions" :key="item.key" :value="item.key">
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <!-- <a-form-item label="计划完成时间">
        <a-range-picker
          allow-clear
          :value="planTimes"
          format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item> -->

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          :projectOptions="projectOptions"
          :specialTypeOptions="specialTypeOptions"
          :specialProgressOptions="specialProgressOptions"
          :statusOptions="statusOptions"
          @ok="onOperationComplete"
          @close="showForm = false"
        />
        <FormDetails
          v-if="showFormDetails"
          ref="formDetailsRef"
          :projectOptions="projectOptions"
          :specialTypeOptions="specialTypeOptions"
          :specialProgressOptions="specialProgressOptions"
          :statusOptions="statusOptions"
          @ok="onOperationComplete"
          @close="showFormDetails = false"
        />

        <FormExamine
          v-if="showFormExamine"
          ref="formExamineRef"
          @ok="onOperationComplete"
          @close="showFormExamine = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getProjectTree } from '@/api/common'
  import { getSpecialPage, deleteSpecial } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import FormDetails from './modules/FormDetails.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormExamine from './modules/FormExamine.vue'
  import moment from 'moment'
  import getFlatTreeMap from '@/utils/getMapFlatTree'

  export default {
    name: 'Special',
    components: {
      VxeTable,
      VxeTableForm,
      FormDetails,
      FormDrawer,
      FormExamine,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        isProperty: '',
        observationType: 1,
        showFormExamine: false,

        inspectionStatusOptions: [
          { key: 1, value: '未判定' },
          { key: 2, value: '正常' },
          { key: 3, value: '异常' },
        ],
        planTypeOptions: [
          { key: 1, value: '计划内' },
          { key: 2, value: '计划外' },
        ],
        isChecked: false,

        specialTypeOptions: [],
        specialTypes: [],
        specialProgressOptions: [],
        specialProgresses: [],
        statusOptions: [],
        statuses: [],

        inspectionTypeOptions: [],
        inspectionTypes: [],
        inspectionStatuses: [],
        planTypes: [],
        projects: [],
        projectOptions: [],
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        showForm: false,
        showFormDetails: false,
        planTimes: [],
        archivesOptions: [],

        list: [],
        tableTitle: '专项维修',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          projectId: this.projectId,
          status: undefined,
          year: null,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '工程名称',
            field: 'projectName',
            minWidth: 90,
            showOverflow: true,
          },
          // {
          //   title: '工程类型',
          //   field: 'planType',
          //   minWidth: 90,
          //   slots: {
          //     default: ({ row, rowIndex }) => {
          //       return this.planTypes[row.planType]?.value || ''
          //     },
          //   },
          // },

          {
            title: '年度',
            minWidth: 60,
            field: 'year',
          },
          {
            title: '维修内容',
            field: 'content',
            minWidth: 100,
            showOverflow: true,
          },

          {
            title: '维修类型',
            field: 'specialType',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.specialTypes[row.specialType]?.value || ''
              },
            },
          },

          {
            title: '进度(%)',
            minWidth: 120,
            field: 'specialSchedule',
          },
          {
            title: '合同额(元)',
            minWidth: 120,
            field: 'capital',
          },
          {
            title: '维修进展',
            field: 'specialProgress',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.specialProgresses[row.specialProgress]?.value || ''
              },
            },
          },

          // {
          //   title: '检查结果',
          //   field: 'inspectionStatus',
          //   minWidth: 90,
          //   slots: {
          //     default: ({ row, rowIndex }) => {
          //       let statusPointColor = 'common-status-incomplete'
          //       if (row.inspectionStatus == 2) {
          //         statusPointColor = 'common-status-completed'
          //       } else if (row.inspectionStatus == 3) {
          //         statusPointColor = 'common-status-abnormal'
          //       }
          //       return (
          //         <div class='common-status-box'>
          //           <i class={['common-status-icon', statusPointColor]}></i>
          //           <span>{this.inspectionStatuses[row.inspectionStatus]?.value}</span>
          //         </div>
          //       )
          //     },
          //   },
          // },
          {
            title: '上报人',
            field: 'createdUserName',
            minWidth: 90,
          },
          {
            title: '上报时间',
            field: 'createdTime',
            minWidth: 150,
          },
          {
            title: '审核状态',
            field: 'status',
            minWidth: 80,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.statuses[row.status]?.value
              },
            },
          },

          {
            title: '操作',
            field: 'operate',
            width: this.isDetail ? 70 : 150,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>

                    <a onClick={() => this.handleEdit(row)}>查看</a>
                    {!this.isDetail && (<a-divider type='vertical' />)}
                    {!this.isDetail && (<a onClick={() => this.handleDelete(row)}>删除</a>)}
                    
                    {!this.isDetail && row.isCheck === 1 && row.status == 1 && (
                      <span>
                        <a-divider type='vertical' />
                        <a v-permission={`${this.$route.meta.menuId}-audit`} onClick={() => this.handleExamine(row)}>
                          审核
                        </a>
                      </span>
                    )}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      getOptions('specialType').then(res => {
        this.specialTypeOptions = res.data
        this.specialTypes = getFlatTreeMap(this.specialTypeOptions, 'key')
      })
      getOptions('specialProgress').then(res => {
        this.specialProgressOptions = res.data
        this.specialProgresses = getFlatTreeMap(this.specialProgressOptions, 'key')
      })

      getOptions('specialStatus').then(res => {
        this.statusOptions = res.data
        this.statuses = getFlatTreeMap(this.statusOptions, 'key')
      })
      this.getList()

      // 获取工程树
      getProjectTree({ objectCategoryId: undefined }).then(res => {
        this.projectOptions = res.data
        this.projects = getFlatTreeMap(this.projectOptions, 'id')
      })
      this.planTypes = getFlatTreeMap(this.planTypeOptions, 'key')
      this.inspectionStatuses = getFlatTreeMap(this.inspectionStatusOptions, 'key')
    },
    mounted() {},
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },
      // 得到年份选择器的值
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
      },
      onRangeChange(value, planTimes) {
        this.planTimes = value
        if (planTimes.length == 0) {
          return
        }
        this.queryParam.startTime = planTimes[0] ? moment(planTimes[0]).format('YYYY-MM-DD') : undefined
        this.queryParam.endTime = planTimes[1] ? moment(planTimes[1]).format('YYYY-MM-DD') : undefined
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getSpecialPage(this.queryParam).then(response => {
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.specialId)
        this.names = valObj.records.map(item => item?.inspectionName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,

          projectId: undefined,
          status: undefined,
          year: null,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 审核 */
      handleExamine(record) {
        this.showFormExamine = true
        this.$nextTick(() => this.$refs.formExamineRef.handle(record))
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /* 查看 */
      handleDetails(record) {
        this.showFormDetails = true
        this.$nextTick(() => this.$refs.formDetailsRef.details(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.specialId ? [row?.specialId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteSpecial({ specialIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
