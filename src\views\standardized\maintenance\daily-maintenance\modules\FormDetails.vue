<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">工程名称：</label>
              <span class="common-value-text">
                {{ form.projectName }}
              </span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">工程部位：</label>
              <span class="common-value-text">
                {{ form.part }}
              </span>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">维养类型：</label>
              <span class="common-value-text">
                {{ this.curingTypeOptions.find(el => el.key == form.curingType)?.value }}
              </span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">维护开始时间：</label>
              <span class="common-value-text">
                {{ form.endTime }}
              </span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">维护结束时间：</label>
              <span class="common-value-text">
                {{ form.endTime }}
              </span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">维护单位：</label>
              <span class="common-value-text">
                {{ form.deptName }}
              </span>
            </div>
          </a-col>
          <!-- <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">验收人：</label>
              <span class="common-value-text">
                {{ form.acceptUserName }}
              </span>
            </div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">验收时间：</label>
              <span class="common-value-text">
                {{ form.acceptDate }}
              </span>
            </div>
          </a-col> -->
          <a-col :lg="24" :md="24" :sm="24">
            <div class="item">
              <label class="common-label-text">维护养护内容：</label>
              <span class="common-value-text">
                {{ form.content }}
              </span>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div class="item">
              <label class="common-label-text">维护情况记录：</label>
              <span class="common-value-text">
                {{ form.record }}
              </span>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 20px">
              <div class="title">相关附件</div>

              <div
                class="file-item"
                v-for="(el, i) in form.positionAttaches"
                :key="i"
                @click="() => downLoad(el.attachUrl)"
              >
                <a-icon type="paper-clip" />
                <div class="file-name" style="margin-left: 5px">
                  {{ el.attachName }}
                </div>
              </div>
            </div>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <div style="margin-bottom: 20px">
              <div class="title">图片</div>
              <div style="display: flex; flex-wrap: wrap">
                <div
                  class="file-img-item"
                  v-for="(el, i) in form.phoneAttaches"
                  :key="i"
                  @click="() => downLoad(el.attachUrl)"
                >
                  <div class="file-img">
                    <img :src="el.attachUrl" alt="" />
                  </div>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getPlansById, getProjectListByPropertyId } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import Upload from '@/components/UploadFile'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    components: { AntModal, Upload },
    props: ['projectOptions', 'curingTypeOptions', 'chargeOptions'],
    data() {
      return {
        modalLoading: false,
        yearShowOne: false, //年份打开关闭状态，true为打开，false为关闭
        formTitle: '',
        form: {},
        open: false,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 物业或外委单位格式化
      propertyFormat(value) {
        if (value) {
          return this.propertyList.find(item => item.projectId == value)?.propertyName
        }
      },
      // 检查结果格式化
      inspectionStatusFormat(value) {
        if (value) {
          return this.inspectionStatusOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 检查类型格式化
      inspectionTypeFormat(value) {
        if (value) {
          return this.inspectionTypeOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '查看'
          getPlansById({
            curingId: row.curingId,
          }).then(res => {
            if (res.code == 200) {
              this.form = {
                ...res.data,

                // phoneAttaches: res.data.phoneAttaches?.map(el => el.attachUrl),
                // positionAttaches: res.data.positionAttaches?.map(el => el.attachUrl),
              }
              this.form.deptName = row.deptName
              this.form.acceptDate = row.acceptDate
              let chargeId = this.projectOptions.find(el => el.projectId == row.projectId)?.chargeId
              this.form.acceptUserName = row.acceptUserId
                ? this.chargeOptions.find(el => el.userId == row.acceptUserId)?.name
                : this.chargeOptions.find(el => el.userId == chargeId)?.name

              this.modalLoading = false
            }
          })
        }
      },

      downLoad(url) {
        window.open(url)
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .file-img-item {
    display: flex;
    // line-height: 25px;
    flex-wrap: wrap;
    margin: 10px 0 0 10px;
  }
  // .file-img-item:nth-child(1) {
  //   margin-left: 0px;
  // }
  .file-img {
    width: 102px;
    height: 102px;
    cursor: pointer;
  }
  .file-img img {
    width: 100%;
    height: 100%;
  }
</style>
