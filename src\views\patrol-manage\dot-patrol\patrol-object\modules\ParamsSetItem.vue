<template>
  <div>
    <ant-modal
      :visible="open"
      :modal-title="modalTitle"
      :loading="modalLoading"
      modalWidth="1440"
      @cancel="cancel"
      modalHeight="800"
    >
      <div slot="content">
        <div class="table-panel" layout="vertical">
          <!-- 左侧树 -->
          <div class="tree-panel">
            <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
              <a-tab-pane key="1" tab="巡检项类别">
                <TreeGeneral
                  v-if="treeTabKey == '1' && treeOptions.dataSource.length"
                  :key="1"
                  hasTab
                  style="width: 220px"
                  ref="treeGeneralRef"
                  :treeOptions="treeOptions"
                  @onTreeMounted="onTreeMounted"
                  @select="node => clickTreeNode(node, 'category')"
                />
              </a-tab-pane>
            </a-tabs>
          </div>

          <!-- 筛选栏 -->
          <div class="right-table-panel">
            <!-- 左侧 选择项 -->
            <div class="left-table">
              <div class="tab-title">
                <span style="float: left; margin-top: 8px; margin-left: 8px; color: #fff">{{ leftDataTotal }}项</span>
                <span style="float: right; margin-top: 8px; margin-right: 8px; color: #fff">选择项</span>
              </div>
              <a-table
                class="tab-table"
                :scroll="{ y: 606 }"
                rowKey="itemId"
                :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                :columns="leftColumns"
                :data-source="leftData"
                :pagination="false"
              ></a-table>
            </div>
            <!-- 新增、删除按钮 -->
            <div class="table-button">
              <div class="tab-group-div">
                <div class="tab-group">
                  <a-button size="small" class="tab-btn" type="primary" :disabled="!hasSelected" @click="add">
                    <a-icon type="right" />
                  </a-button>
                  <a-button size="small" class="tab-btn" type="primary" :disabled="!hasObjSelected" @click="del">
                    <a-icon type="left" />
                  </a-button>
                </div>
              </div>
            </div>
            <!-- 右侧 已选项 -->
            <div class="right-table">
              <div class="tab-title">
                <span style="float: left; margin-top: 8px; margin-left: 8px; color: #fff">{{ rightDataTotal }}项</span>
                <span style="float: right; margin-top: 8px; margin-right: 8px; color: #fff">已选项</span>
              </div>

              <a-table
                class="tab-table"
                :scroll="{ y: 606 }"
                rowKey="itemId"
                :row-selection="{ selectedRowKeys: selectedObjRowKeys, onChange: onSelectObjChange }"
                :columns="rightColumns"
                :data-source="rightData"
                :pagination="false"
              ></a-table>
            </div>
          </div>
        </div>
      </div>

      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="confirm" :loading="loading">确定</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import {
    getPatrolObjectList,
    patrolObjectItemAdd,
    getPatrolObjectSiteList,
    getPatrolItemCategoryTree,
    patrolObjectItemList,
    getChooseItem,
  } from '../services'
  import draggable from 'vuedraggable'
  import cloneDeep from 'lodash.clonedeep'
  import { getOptions } from '@/api/common'

  import 'ant-design-vue/es/transfer/style'
  import * as _ from 'lodash'
  import difference from 'lodash/difference'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import TreeGeneral from '@/components/TreeGeneral'

  export default {
    name: 'TreeTransfer',
    props: {},
    components: {
      TreeGeneral,
      AntModal,
      AdvanceTable,
      draggable,
    },
    data() {
      return {
        dataSource: [], //左侧列表数据,
        originData: [], //右侧表格数据
        targetKeys: [], //右侧已选中数据
        selectItemList: [], //根据接口已选中

        leftData: [],
        leftDataTotal: 0,
        rightDataTotal: 0,
        rightData: [],

        paramObject: {},
        selectedRows: [],
        selectedRowKeys: [],
        selectedObjRowKeys: [],

        loading: false,
        modalLoading: false,
        treeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'itemCategoryName',
            key: 'itemCategoryId',
            value: 'itemCategoryId',
          },
        },

        lineTypeOptions: [
          { key: 1, value: '单行文本框' },
          { key: 2, value: '多行文本框' },
          { key: 3, value: '数值' },
          { key: 4, value: '下拉框' },
        ],
        lineTypes: [],

        disabled: false,
        treeTabKey: '1',

        open: false,
        modalTitle: '',
        rowInfo: {}, //选中分页列表行数据

        tableTitle: ' ',

        ids: [],
        single: true,
        multiple: true,
        objectCategoryId: null, //7,

        leftParam: {
          inputType: null,
          itemCategoryId: null,
          itemIds: [],
          itemName: '',
          pageNum: 1,
          pageSize: Number.MAX_SAFE_INTEGER,
          patrolType: this.$route.meta.query.patrolType,
          sort: [],
        },

        leftColumns: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            align: 'center',
            width: '55px',
          },
          {
            title: '名称',
            dataIndex: 'itemName',
            align: 'center',
            width: '120px',
            ellipsis: true,
          },

          {
            title: '类型',
            dataIndex: 'inputTypeName',
            align: 'center',
            width: '120px',
            ellipsis: true,
          },
        ],
        rightColumns: [
          {
            title: '序号',
            customRender: (text, record, index) => index + 1,
            ellipsis: true,
            align: 'center',
            width: '55px',
          },
          {
            title: '名称',
            dataIndex: 'itemName',
            align: 'center',
            width: '120px',
            ellipsis: true,
          },
          {
            title: '类型',
            dataIndex: 'inputTypeName',
            align: 'center',
            width: '120px',
            ellipsis: true,
          },
          {
            title: '操作',
            width: '60px',
            dataIndex: 'customRow',
            scopedSlots: { customRender: 'customRow' },
            key: 'action',
            customRender: (_, record, i) => {
              return (
                <div>
                  <span
                    style='margin-left:-10px;cursor:pointer;color: #1890ff;'
                    onclick={() => {
                      this.handleDelete(record)
                    }}
                  >
                    删除
                  </span>
                </div>
              )
            },
          },
        ],
      }
    },
    computed: {
      hasSelected() {
        return this.selectedRowKeys.length > 0
      },
      hasObjSelected() {
        return this.selectedObjRowKeys.length > 0
      },
    },
    watch: {},
    created() {
      this.lineTypes = getFlatTreeMap(this.lineTypeOptions, 'key')
      // this.init()
    },
    mounted() {},
    methods: {
      init() {
        getPatrolItemCategoryTree({ keywords: null, patrolType: this.$route.meta.query.patrolType }).then(res => {
          this.treeOptions.dataSource = res.data
        })

        patrolObjectItemList(this.leftParam).then(res => {
          let objArr = res?.data?.data || []
          this.modalLoading = false

          this.originData = objArr.map((obj, index) => ({
            ...obj,
            title: obj.itemName,
            inputTypeName: this.lineTypes[obj.inputType]?.value || '',
          }))

          this.getRightList()
        })
      },
      // 树加载完成后
      onTreeMounted(data) {
        if (this.treeTabKey == '1') {
          this.leftParam.itemCategoryId = data[0].objectCategoryId
          this.tableTitle = data[0].objectCategoryName
          this.treeOptions.dataSource = data
        }
      },
      //树点击事件
      clickTreeNode(node, type) {
        this.leftParam.itemCategoryId = node.$options.propsData.eventKey
        this.tableTitle = node.$options.propsData.dataRef.title
        this.leftParam.pageNum = 1
        this.getList()
      },
      getRightList() {
        this.rightData = []
        //根据已选中的itemId，获取右侧列表数据
        this.originData.forEach(item => {
          if (this.selectItemList.indexOf(item.itemId) !== -1) {
            this.rightData.push(item)
          }
        })
        this.rightDataTotal = this.rightData?.length
      },
      getList() {
        patrolObjectItemList(this.leftParam).then(res => {
          let objArr = res?.data?.data || []
          this.modalLoading = false
          let list = objArr.map((obj, index) => ({
            ...obj,
            title: obj.itemName,
            inputTypeName: this.lineTypes[obj.inputType]?.value || '',
          }))

          this.leftData = list.filter(item => !this.selectItemList.includes(item.itemId))
          this.leftDataTotal = this.leftData?.length || 0
        })
      },
      // 已选项新增
      add() {
        if (!this.selectedRowKeys) {
          return
        }
        // 使用filter方法来创建一个新数组，其中不包含select中的元素
        let list = this.selectItemList.concat(this.selectedRowKeys)
        this.selectItemList = [...new Set(list)]

        this.getRightList()
        this.getList()
        this.selectedRowKeys = []
      },
      // 已选项删除
      del() {
        if (!this.selectedObjRowKeys) {
          return
        }
        // 使用filter方法来创建一个新数组，其中不包含select中的元素
        let list = this.selectItemList.filter(item => !this.selectedObjRowKeys.includes(item))
        this.selectItemList = [...new Set(list)]
        this.getRightList()
        this.getList()
        this.selectedObjRowKeys = []
      },
      handleDelete(record) {
        let list = this.selectItemList.filter(item => item != record.itemId)
        this.selectItemList = [...new Set(list)]
        this.getRightList()
        this.getList()
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.leftParam.itemCategoryId = 0 //
        this.$emit('ok')
        this.$emit('close')
      },
      handleShow(record) {
        this.open = true
        this.modalTitle = '巡检对象巡检项选择'
        this.modalLoading = true
        this.objectCategoryId = record.objectId
        this.rowInfo = record
        this.leftParam.objectId = record.objectId
        getChooseItem({ objectId: record.objectId }).then(res => {
          this.selectItemList = res?.data
        })

        this.$nextTick(() => {
          setTimeout(() => {
            this.init()
            this.getList()
          }, 100)
        })
      },
      //选择项选中项集合
      onSelectChange(selectedRowKeys) {
        this.selectedRowKeys = selectedRowKeys
      },
      // 已选项选中项集合
      onSelectObjChange(selectedRowKeys) {
        this.selectedObjRowKeys = selectedRowKeys
      },

      /** 提交按钮 */
      confirm() {
        let objectArr = {
          itemId: this.selectItemList,
          objectId: this.objectCategoryId,
        }
        this.loading = true
        patrolObjectItemAdd(objectArr)
          .then(res => {
            this.$message.success('新增成功', 3)
            this.open = false
            this.$emit('ok')
            this.$emit('close')
          })
          .catch(() => (this.loading = false))
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';

  // 多余滚动条
  ::v-deep .advanced-table {
    overflow-y: visible;
  }

  ::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background-color: transparent;
  }

  ::v-deep .table-panel {
    display: flex;
    height: 820px;
    width: 100%;
    // height: calc(100vh - 207px);
    // position: relative;

    .tree-panel {
      background-color: #fff;
      margin-right: 10px;
      border-radius: 2px;
      border: 1px solid #d9d9d9;
      // height: calc(100vh - 110px);
      height: 700px;
      // padding: 16px 10px;

      .treeGeneral {
        margin: 7px 0;
        overflow-y: auto;
        overflow-x: hidden;
        height: 600px;
        // height: calc(100% - 110px);
      }

      .tab-tree-panel-box {
        margin: 7px 0;
        overflow-y: auto;
        overflow-x: hidden;
        height: 580px;
        // height: calc(100vh - 207px);
      }
    }

    .right-table-panel {
      flex: 1;
      width: 1160px;
      height: 700px;
      // height: calc(100vh - 207px);
      // position: relative;

      .left-table {
        // display: inline-block;
        box-sizing: border-box;
        // width: 550px;
        width: 45%;
        // height: 740px;
        // height: 100%;
        float: left;
        border: 1px solid #d9d9d9;

        .tab-title {
          // width: 550px;
          height: 40px;
          background-color: #1890ff;
        }
        .tab-input {
          height: 30px;
          margin: 10px 12px 0px;
          width: 95%;
        }
        .tab-table {
          // width: 550px;
          margin-top: 10px;
          height: 690px;
          // height: 100%;
        }
      }
      .table-button {
        // display: inline-block;
        box-sizing: border-box;
        // width: 72px;
        width: 10%;
        height: 100%;
        float: left;
        // margin-left: 15px;
        // background-color: #1890ff;
        // margin-top: 360px;
        .tab-group-div {
          // background-color: #1890ff !important;
          width: 100% !important;
          height: 100% !important;
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          .tab-group {
            width: 100px;
            height: 100px;
            display: flex !important;
            flex-direction: column;
            justify-content: center !important;
            align-items: center !important;
            .tab-btn {
              width: 68px !important;
              height: 24px !important;
              margin-top: 10px !important;
            }
            // margin: 0 auto !important;
          }
        }

        // float: center;
      }
      .right-table {
        // display: inline-block;
        box-sizing: border-box;
        // width: 550px;
        width: 45%;
        // height: 100%;
        // height: 740px;
        float: left;
        border: 1px solid #d9d9d9;

        .tab-title {
          // width: 550px;
          height: 40px;
          background-color: #1890ff;
        }

        .tab-input {
          height: 30px;
          margin: 10px 12px 0px;
          width: 95%;
        }

        .tab-table {
          // width: 550px;
          margin-top: 10px;
          height: 690px;
        }
      }
    }
  }
  ::v-deep .ant-input-affix-wrapper .ant-input {
    margin-top: -15px !important;
    margin-left: 10px !important;
    width: 200px !important;
    position: relative;
    text-align: inherit;
  }

  ::v-deep .ant-transfer-list-header {
    color: #fff;
    background: #1890ff;
  }

  ::v-deep .ant-modal-body {
    width: 100% !important;
    height: 740px !important;
    max-height: 740px !important;
    overflow: hidden !important;
  }

  ::v-deep .ant-transfer-list-body-customize-wrapper {
    height: 640px !important;
    overflow-y: hidden;
    overflow-x: hidden;
  }

  ::v-deep .ant-table-wrapper {
    height: 650px !important;
    overflow-y: auto;
    overflow-x: hidden;
  }

  ::v-deep .modal-content {
    height: 760px !important;
    width: 100% !important;
  }

  ::v-deep .ant-modal-content {
    height: 860px;
    width: 100%;
    overflow-x: hidden;
  }

  ::v-deep .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
    height: 40px;
    margin-right: 4px;
    margin-top: 0px;
    line-height: 40px;
  }

  .table-box {
    position: absolute;
    height: 100%;
    background-color: #f0f0f0;

    overflow: hidden;
    position: relative;
    ::v-deep .ant-table-thead {
      position: sticky;
      top: 0px;
      z-index: 2;
    }
  }
  /*.table 为全局表格自定义样式*/
  .table .ant-table-body,
  .table .ant-table-header {
    overflow-y: auto !important;
  }
  ::v-deep .ant-table-tbody > tr > td {
    height: 42px !important;
    cursor: pointer !important;
  }
  ::v-deep .ant-table-default .ant-table-thead > tr > th,
  ::v-deep .ant-table-default .ant-table-tbody > tr > td {
    padding: 10.6px 6px;
  }
  ::v-deep .ant-table-default .ant-table-thead > tr > th {
    background: #f2f3f5;
  }
  ::v-deep .ant-table-default .ant-table-placeholder {
    height: 600px !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
