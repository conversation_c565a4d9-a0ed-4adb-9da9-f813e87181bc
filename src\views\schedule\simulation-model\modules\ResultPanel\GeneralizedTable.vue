<template>
  <div style="height: auto; width: 60%; margin-left: 10px; margin-top: 16px; background-color: red">
    <!-- <a-tabs v-model="active" size="small">
      <a-tab-pane v-for="(el, idx) in allData" v-model="active" :key="el.projectId" :tab="el.projectName">
        <div style="flex: 1">
          
        </div>
      </a-tab-pane>
    </a-tabs> -->
    <VxeTable
      ref="vxeTableRef"
      size="small"
      :isShowTableHeader="false"
      :isDrop="false"
      :columns="columns"
      :tableData="allData"
      :loading="loading"
      :tablePage="false"
    ></VxeTable>
    <div class="curve-panel" v-if="!!activeProcess">
      <div class="header">
        <div class="icon">{{ activeProcess?.chProjectName?.slice(0, 1) }}</div>
        <div class="name">{{ activeProcess?.projectName }}</div>
        <div class="close" @click="activeProcess = null">X</div>
      </div>
      <div class="chart"><ProcessChart style="margin-right: 20px" :dataSource="activeProcess" /></div>
    </div>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'
  import { loading } from 'vxe-pc-ui'
  import { getChSimResList } from '../../services.js'
  import ProcessChart from './components/TableProcessChart.vue'

  export default {
    name: 'ResultTable',
    props: ['chSimId', 'dataSource'],
    components: { VxeTable, ProcessChart },
    data() {
      return {
        active: undefined,
        loading: true,
        allData: [],
        activeProcess: null,
        activeProcessShow: false,
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '调度对象',
            fixed: 'left',
            minWidth: 120,
            slots: {
              default: ({ row }) => {
                return (
                  <span>
                    {
                      <span>
                        <a onClick={() => this.projectClick(row)}>{row.projectName}</a>
                      </span>
                    }
                  </span>
                )
              },
            },
          },
          {
            field: 'chProjectName',
            title: '所属干渠',
            minWidth: 80,
          },
          {
            field: 'startUpWlv',
            title: '初始上游水位(m)',
            minWidth: 80,
          },
          {
            field: 'startDownWlv',
            title: '初始下游水位(m)',
            minWidth: 80,
          },
          {
            field: 'endUpWlv',
            title: '末期上游水位(m)',
            minWidth: 80,
          },
          {
            field: 'endDownWlv',
            title: '末期下游水位(m)',
            minWidth: 80,
          },
          {
            field: 'sumOutFlow',
            title: '累计过闸流量(万m³)',
            minWidth: 80,
          },
          // {
          //   title: '闸泵工况',
          //   minWidth: 150,
          //   slots: {
          //     default: ({ row, rowIndex }) => {
          //       const obj = row.recordsObj[ele.projectId]
          //       if (obj.type === 3) return '-'

          //       if (obj.type === 0 || obj.type === 2) {
          //         return (
          //           <div class='cell-box'>
          //             <span>闸流量:{obj.outFlow}</span>&nbsp;&nbsp;
          //             <span>开度:{obj.open}</span>
          //           </div>
          //         )
          //       }

          //       if (obj.type === 1 || obj.type === 4) {
          //         return <span>泵流量:{obj.inFlow}</span>
          //       }
          //     },
          //   },
          // },
          // ...el.projects.map(ele => ({
          //   title: ele.projectName,
          //   children: [

          //   ],
          // })),
        ],
      }
    },
    computed: {},
    watch: {
      dataSource: {
        handler(newVal, oldVal) {
          this.loading = false
          // this.active = newVal?.[0]?.projectId
          this.allData = newVal
          // this.allData = newVal.map((el, index) => {
          //   // const resArr = el.resVOS.map(ele => {
          //   //   let obj = {}
          //   //   ele.records.forEach(ele => (obj[ele.projectId] = ele))
          //   //   return { ...ele, recordsObj: obj }
          //   // })
          //   return { ...el, index }
          // })
        },
      },
    },
    created() {},
    methods: {
      projectClick(item) {
        this.activeProcessShow = true
        getChSimResList({ chSimId: this.chSimId, projectId: item.projectId }).then(res => {
          this.activeProcess = { ...item, chartData: res.data }
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .curve-panel {
    position: absolute;
    z-index: 99999999;
    bottom: 34%;
    right: 8px;
    width: 580px;
    display: flex;
    border-radius: 4px;
    overflow: hidden;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    border: 2px solid rgba(78, 89, 105, 0.3);
    .header {
      background: #f2f3f5;
      color: #1d2129;
      line-height: 20px;
      padding: 6px 8px;
      display: flex;
      // align-items: center;

      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }
      .name {
        flex: 1;
        font-weight: 600;
        margin: 0 0 0 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .close {
        margin-left: auto;
        cursor: pointer;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        color: #8f959e;
      }
    }
    .chart {
      flex: 1;
      padding-top: 10px;
    }
  }
</style>
