<template>
  <div style="flex: 1">
    <div v-if="loading" style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
      <a-spin></a-spin>
    </div>

    <div
      v-else-if="!!errorInfo"
      style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center"
    >
      <a-result status="error" :sub-title="errorInfo"></a-result>
    </div>

    <ResultPanel v-else-if="!!resultData" :resultData="resultData" style="margin-top: 10px" />
  </div>
</template>

<script lang="jsx">
  import { forecast, getInWater } from '../../../services'
  import { SocketClient } from '@/utils/sockClient.js'
  import ResultPanel from '../../../components/ResultPanel.vue'

  export default {
    name: 'Result',
    props: ['baseInfo', 'rainfall'],
    components: { ResultPanel },
    data() {
      return {
        loading: false,
        socketIns: null,
        errorInfo: null,
        resultData: null,
      }
    },
    computed: {},
    created() {
      this.loading = true
      this.$emit('update:isDisabledBtn', true)
      this.errorInfo = null

      forecast({ ...this.baseInfo, rains: this.rainfall }).then(res => {
        this.socketIns = new SocketClient()

        this.socketIns.connect('/topic/model/result', response => {
          if (response.code == 200) {
            if (res.data == response.data.id && response.data.modelType === 'LSYB') {
              // 发请求
              getInWater({ inWaterId: response.data.id })
                .then(resp => {
                  this.resultData = resp.data
                })
                .finally(() => {
                  this.$emit('update:isDisabledBtn', false)
                  this.loading = false
                })
            }
          } else {
            this.$emit('update:isDisabledBtn', false)
            this.loading = false
            this.errorInfo = response.message || '模型计算异常了'
          }
        })
      })
    },
    mounted() {},
    // activated() {
    //   this.socketIns.connect('/topic/model/result', response => {})
    // },
    // deactivated() {
    //   this.socketIns.disconnect()
    // },
    beforeDestroy() {
      this.socketIns.disconnect()
    },
    methods: {
      save() {
        this.$emit('saveData', this.resultData)
      },
    },
  }
</script>

<style lang="less" scoped></style>
