<template>
  <div style="height: 100%; display: flex; flex-direction: column">
    <div style="margin-top: 10px; display: flex; align-items: center; justify-content: space-between">
      <span style="font-size: 16px; font-weight: bold">仿真结果</span>
      <a-radio-group v-model="active" buttonStyle="solid" @change="onTabChange">
        <a-radio-button
          v-for="el in [
            { label: '来水预报结果', value: 0 },
            { label: '推演结果', value: 1 },
            { label: '推演仿真', value: 2 },
            { label: '推演概化图', value: 3 },
          ]"
          :value="el.value"
          :key="el.value"
        >
          {{ el.label }}
        </a-radio-button>
      </a-radio-group>
    </div>

    <div style="flex: 1; position: relative">
      <WaterForecast v-if="active === 0" :chSimId="chSimId" @getWaterData="$listeners.getWaterData" />

      <ResultTable v-if="active === 1" :dataSource="data1" />

      <Simulation v-if="active === 2 && !!data2 && !!data1" :chSimId="chSimId" :mapData="data1" :dataSource="data2" />

      <div style="display: flex; width: 100%; height: 100%" v-if="active === 3">
        <GeneralizedDiagram
          v-if="!!data1 && !!data3"
          :chSimId="chSimId"
          :fcstRange="fcstRange"
          :mapData="data1"
          :dataSource="data3"
          :modelId="modelId"
        />
        <GeneralizedTable :chSimId="chSimId" :dataSource="data3" />
      </div>

      <div
        v-if="loading"
        style="
          position: absolute;
          width: 100%;
          height: 100%;
          z-index: 9999;
          display: flex;
          justify-content: center;
          align-items: center;
        "
      >
        <a-spin></a-spin>
      </div>
    </div>
  </div>
</template>

<script>
  import { getInferRes, getScaleRes, getScaleResDetails } from '../../services'
  import ResultTable from './ResultTable.vue'
  import Simulation from './Simulation.vue'
  import axios from 'axios'
  import { getChSimRange } from '../../services'
  import GeneralizedTable from './GeneralizedTable.vue'
  import GeneralizedDiagram from './GeneralizedDiagram/index.vue'
  import WaterForecast from './WaterForecast/index.vue'
  import { getOptions } from '@/api/common'

  export default {
    name: 'ResultPanel',
    props: ['chSimId', 'fcstRange', 'modelId'],
    components: { WaterForecast, ResultTable, Simulation, GeneralizedTable, GeneralizedDiagram },
    data() {
      return {
        active: 0,
        data1: null,
        data2: null,
        data3: [], //概化图 列表
        loading: false,
      }
    },
    computed: {},
    watch: {},
    created() {
      this.getData()
    },
    methods: {
      onTabChange(val) {
        this.data1 = null
        this.data2 = null
        this.data3 = []
        this.$nextTick(() => this.getData())
      },
      async getData() {
        if (this.active === 1) {
          const res = await getInferRes({ chSimId: this.chSimId })
          this.data1 = res.data
        }
        if (this.active === 2) {
          this.loading = true
          getInferRes({ chSimId: this.chSimId }).then(res => {
            this.data1 = res.data.map(el => ({
              ...el,
              resVOS: el.resVOS.map(item => ({
                ...item,
                records: (item.records || []).map(element => ({
                  ...element,
                  ditch: {
                    ...el,
                    projectObj: el.projects?.find(ele => ele.projectCode === element.projectCode),
                    projects: null,
                    resVOS: null,
                  },
                })),
              })),
            }))
          })

          getChSimRange({ type: 1 }).then(resp => {
            axios.get(`${process.env.VUE_APP_MODEL_BASE}/${this.modelId}.json`).then(res => {
              this.$nextTick(() => {
                resp.data[0].projects.forEach(el => {
                  res.data[el.projectCode] = { ...el, ...res.data[el.projectCode] }
                })
                this.data2 = res.data
                this.loading = false
              })
            })
          })
        }
        if (this.active === 3) {
          getInferRes({ chSimId: this.chSimId }).then(res => {
            this.data1 = res.data.map(el => ({
              ...el,
              resVOS: el.resVOS.map(item => ({
                ...item,
                records: item.records?.map(element => ({ ...element, ditch: { ...el, projects: null, resVOS: null } })),
              })),
            }))
          })
          getScaleResDetails({ chSimId: this.chSimId }).then(res => {
            this.data3 = res.data
          })
        }
      },
    },
  }
</script>

<style lang="less" scoped></style>
