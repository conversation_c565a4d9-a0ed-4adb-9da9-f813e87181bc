<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <TreeGeneral
        style="width: 220px"
        ref="treeGeneralRef"
        :treeOptions="treeOptions"
        @onTreeMounted="onTreeMounted"
        @select="clickTreeNode"
      />
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="对象名称">
          <a-input
            v-model="queryParam.objectName"
            placeholder="请输入对象名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :stripe="false"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tree-config="{
              iconOpen: 'vxe-icon-square-minus-fill',
              iconClose: 'vxe-icon-square-plus-fill',
              line: true,
              rowField: 'objectId',
              parentField: 'parentId',
              hasChild: 'hasChild',
              indent: 20,
              reserve: true,
            }"
            :checkbox-config="{ highlight: true, checkStrictly: true, showHeader: true }"
            :row-config="{ isHover: true, keyField: 'objectId' }"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" :disabled="executeLoading" @click="handleExecute()">同步对象</a-button>
              <a-button type="primary" @click="handleObjectSet()">
                <a-icon type="plus" />
                选择巡检对象
              </a-button>
              <!-- <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button> -->
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>

    <FormDrawer v-if="showFormDrawer" ref="formDrawerRef" @ok="onOperationComplete" @close="showFormDrawer = false" />
    <!-- 巡检对象配置 -->
    <ParamsSetModal
      v-if="showParamsSetModal"
      @ok="onOperationComplete"
      ref="paramsSetModalRef"
      @close="showParamsSetModal = false"
    />
    <!-- 设备配置 -->
    <ParamsSetSite
      v-if="showParamsSetSite"
      @ok="onOperationComplete"
      ref="paramsSetSiteRef"
      @close="showParamsSetSite = false"
    />
    <!-- 巡检项配置 -->
    <ParamsSetItem
      v-if="showParamsSetItem"
      @ok="onOperationComplete"
      ref="paramsSetItemRef"
      @close="showParamsSetItem = false"
    />
  </div>
</template>

<script lang="jsx">
  import {
    getObjectCategoryTree,
    getPatrolObjectPage,
    deletePatrolObject,
    patrolObjectExecute,
    listByObjectCategoryCode,
  } from './services'
  import { getDistrictTree, getProjectTree, getProjectChildren } from '@/api/common'
  import FormDrawer from './modules/FormDrawer'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import TreeGeneral from '@/components/TreeGeneral'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import { getFlatTree } from '@/utils/getMapFlatTree'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import cloneDeep from 'lodash.clonedeep'
  import ParamsSetModal from './modules/ParamsSetModal.vue'
  import ParamsSetSite from './modules/ParamsSetSite.vue'
  import ParamsSetItem from './modules/ParamsSetItem.vue'

  export default {
    name: 'ProjectManage',
    components: {
      AdvanceTable,
      VxeTable,
      VxeTableForm,
      TreeGeneral,
      ParamsSetModal,
      ParamsSetSite,
      ParamsSetItem,
      FormDrawer,
    },
    data() {
      return {
        treeOptions: {
          getDataApi: getObjectCategoryTree,
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
            value: 'objectCategoryId',
          },
        },
        showFormDrawer: false,

        showParamsSetModal: false,
        showParamsSetSite: false,
        showParamsSetItem: false,

        objectId: '',

        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,
        objectTypeList: [
          { key: 1, value: '水利工程' },
          { key: 2, value: '监测站点' },
          { key: 3, value: '江河湖泊' },
          { key: 9, value: '工程设备' },
        ],
        objectTypes: [],
        executeLoading: false,
        queryParam: {
          objectCategoryId: null,
          objectIds: [],
          objectName: '',
          objectParentId: undefined,
          objectType: null,
          pageNum: 1,
          pageSize: 10,
          patrolType: this.$route.meta.query.patrolType,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30, align: 'center' },
          {
            title: '对象编码',
            field: 'objectCode',
            minWidth: 100,
            treeNode: true,
            showOverflow: 'tooltip',
            sortable: true,
          },
          {
            title: '对象名称',
            field: 'objectName',
            minWidth: 120,
            showOverflow: 'tooltip',
            sortable: true,
          },
          {
            title: '对象类别',
            field: 'objectType',
            minWidth: 90,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.objectTypes[row.objectType]?.value || ''
              },
            },
          },
          {
            title: '上级工程',
            field: 'objectParentName',
            showOverflow: 'tooltip',
            minWidth: 120,
          },
          {
            title: '详细地址',
            field: 'address',
            showOverflow: 'tooltip',
            minWidth: 120,
          },
          {
            title: '包含设备数',
            field: 'deviceCount',
            minWidth: 90,
          },
          {
            title: '巡检项数',
            field: 'itemCount',
            minWidth: 80,
          },
          {
            title: '备注',
            field: 'remark',
            showOverflow: 'tooltip',
            minWidth: 120,
          },
          {
            title: '操作',
            field: 'operate',
            width: 220,
            align: 'center',
            slots: {
              // 使用 JSX 渲染
              default: ({ row, rowIndex }) => {
                return [
                  <span>
                    <a onClick={() => this.handleItemConfig(row)}>巡检项配置</a>
                    <a-divider type='vertical' />
                    {row.parentId == 0 && row.objectType != 3 ? (
                      <span>
                        <a onClick={() => this.handleSiteConfig(row)}>设备配置</a>
                        <a-divider type='vertical' />
                      </span>
                    ) : (
                      ''
                    )}
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>,
                ]
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      this.objectTypes = getFlatTreeMap(this.objectTypeList, 'key')
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        if (this.queryParam.objectCategoryId || this.queryParam.districtCode) {
          this.getList()
        }
      },
      // 参数配置
      handleObjectSet() {
        this.showParamsSetModal = true
        this.$nextTick(() => {
          this.$refs.paramsSetModalRef.handleShow()
        })
      },
      /*同步对象*/
      handleExecute() {
        this.executeLoading = true
        patrolObjectExecute().then(res => {
          this.executeLoading = false
          this.$message.success(`同步成功了${res.data}条`)
          this.handleQuery()
        })
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getPatrolObjectPage(this.queryParam).then(response => {
          this.list = response.data.data.map(el => ({
            ...el,
            hasChild: el.devices?.length ? true : false,
          }))
          this.list.forEach(obj => {
            if (obj.parentId === null) {
              obj.parentId = 0
            }
            obj.children = obj.devices.map(el => ({ ...el, hasChild: false }))
            obj.isLeaf = obj.devices?.length > 0
            delete obj.devices
          })
          this.total = response.data.total
          this.loading = false
        })
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },

      // 查询子节点
      loadChildrenMethod(valObj) {
        return Promise.resolve(
          valObj.row.children?.map(el => ({
            ...el,
            hasChild: el.children?.length ? true : false,
          })),
        )
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          objectName: '',
          objectParentId: undefined,
          objectType: undefined,
          pageNum: 1,
          patrolType: this.$route.meta.query.patrolType,
          sort: [],
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete(row, params) {
        /*  封装在vxe-table组件里面
          1. 获取所有已展开的 key, (也要获取它们的所有parentId)
          2. 目标行的 key(这里是params.parentId), (和获取所有parentId)
        */
        if (row) {
          this.getList({
            callback: () => {
              this.$refs.vxeTableRef.setTreeExpand([params.parentId], 'projectId')
            },
          })
        } else {
          this.getList()
        }
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 多选框选中
      selectChange(valObj) {
        if (valObj.records.length == 0) {
          const vxeTableIns = this.$refs.vxeTableRef.$refs.vxeTableRef
          vxeTableIns.clearCheckboxRow()
        }
        this.ids = valObj.records.map(item => item.projectId)
        this.names = valObj.records.map(item => item.projectName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 树加载完成后
      onTreeMounted(data) {
        this.queryParam.objectCategoryId = data[0].objectCategoryId
        this.queryParam.districtCode = ''
        this.tableTitle = data[0].objectCategoryName
        this.treeOptions.dataSource = data

        this.getList()
      },
      clickTreeNode(node, type) {
        this.queryParam.objectCategoryId = node.$options.propsData.eventKey
        this.tableTitle = node.$options.propsData.dataRef.title

        this.getList()
      },

      /* 新增 */
      handleAdd(record) {
        this.showFormDrawer = true
        const type = record ? 'leaf' : 'root'
        {
          /* if (this.treeTabKey === '1') {
          const r = record ? record : { objectCategoryId: this.queryParam.objectCategoryId }
          this.$nextTick(() => this.$refs.formDrawerRef.handleAdd(type, r, '1'))
        } */
        }
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },
      /* 设备配置 */
      handleSiteConfig(record) {
        // this.showFormDrawer = true
        // this.$nextTick(() => this.$refs.formDrawerRef.handleSiteConfig(record))
        this.showParamsSetSite = true
        this.$nextTick(() => {
          this.$refs.paramsSetSiteRef.handleShow(record)
        })
      },
      /* 巡检项配置 */
      handleItemConfig(record) {
        this.showParamsSetItem = true
        this.$nextTick(() => {
          this.$refs.paramsSetItemRef.handleShow(record)
        })

        // this.showFormDrawer = true
        // this.$nextTick(() => this.$refs.formDrawerRef.handleItemConfig(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const objectIds = row.objectId ? [row.objectId] : this.ids
        const names = row.objectName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deletePatrolObject({ objectIds: objectIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
