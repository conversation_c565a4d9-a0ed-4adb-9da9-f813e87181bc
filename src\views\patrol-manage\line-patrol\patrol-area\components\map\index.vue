<template>
  <div class="map">
    <div class="search">
      <a-select
        show-search
        allowClear
        :defaultActiveFirstOption="false"
        placeholder="请输入"
        style="width: 260px"
        :filter-option="false"
        :not-found-content="null"
        @search="onSearchChange"
        @change="handleChange"
      >
        <a-spin v-if="!objectList.length" slot="notFoundContent" size="small" />
        <a-select-option v-for="d in objectList" :key="d.objectId">
          {{ d.objectName }}
        </a-select-option>
      </a-select>
    </div>

    <div class="tip" v-if="isDraw">点击地图绘制，双击完成</div>

    <div class="form-panel" v-if="isDraw">
      <div class="form-panel-title">新增</div>
      <a-form-model ref="form" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 17 }">
        <a-form-model-item label="范围名称" prop="name">
          <a-input v-model="form.name" placeholder="请输入" allow-clear />
        </a-form-model-item>
        <a-form-model-item label="备注" prop="remark">
          <a-textarea v-model="form.remark" placeholder="请输入" allow-clear />
        </a-form-model-item>
        <div style="text-align: center">
          <a-button @click="cancel">取消</a-button>
          <a-button
            type="primary"
            @click="submitForm"
            style="margin-left: 10px"
            :disabled="!form.range"
            :loading="loading"
          >
            确定
          </a-button>
        </div>
      </a-form-model>
    </div>

    <MapBox :options="{}" @onMapMounted="onMapMounted" @onMapStyleLoad="onMapStyleLoad" />

    <div class="legend">
      <div class="legend-item" v-for="(el, i) in allObject" :key="i">
        <div class="legend-icon" :style="{ background: legendColors[i] }"></div>
        {{ el.name }}　　
        <span style="font-weight: 600">{{ el.data.length }}</span>
      </div>
    </div>

    <div style="position: absolute; right: 16px; bottom: 16px">
      <MapStyle v-if="!!mapIns" :mapIns="mapIns" ref="mapStyleRef" />
    </div>
  </div>
</template>

<script lang="jsx">
  import mapboxgl from 'mapbox-gl'
  import MapBox from '@/components/MapBox/index.vue'
  import MapboxDraw from '@mapbox/mapbox-gl-draw'
  import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css'
  import { dealAllPoint, mapBound, mapBoundGeo } from '@/utils/mapBounds.js'
  import { clearSourceAndLayer } from '@/utils/mapUtils'
  import * as turf from '@turf/turf'
  import debounce from 'lodash/debounce'
  import { getPatrolObjectList } from '../../services'
  import MapStyle from '@/components/MapBox/MapStyle.vue'

  export default {
    name: 'Map',
    components: { MapBox, MapStyle },
    props: {
      isDraw: { default: false },
      dataSource: {
        default: () => [],
      },
      rowInfo: {
        default: null,
      },
    },
    data() {
      return {
        mapIns: null,
        drawIns: null,
        allGeoJson: null,

        loading: false,
        form: {
          name: undefined,
          remark: undefined,
          range: undefined,
          lineId: undefined,
        },
        rules: {
          name: [{ required: true, message: '请输入范围名称', trigger: 'blur' }],
        },
        activeObject: null,
        legendColors: ['#B040F9', '#408EF9', '#1AC95C', '#FFCE00', '#6240F9', '#00DDDF'],

        allObject: [],
        objectList: [],
        allObjectList: [],
      }
    },
    computed: {},
    watch: {
      isDraw(newVal, oldVal) {
        if (newVal) {
          this.mapIns._controlContainer.style.display = 'block'
          this.drawIns.changeMode('draw_polygon')
        } else {
          this.drawIns.changeMode('simple_select')
          this.mapIns._controlContainer.style.display = 'none'
        }
      },
      dataSource: {
        handler(newVal, oldVal) {
          this.dealLayers(newVal)
        },
        deep: true,
      },
      rowInfo: {
        handler(newVal, oldVal) {
          this.form.name = newVal?.lineName || undefined
          this.form.remark = newVal?.remark || undefined
          this.form.lineId = newVal?.lineId || undefined

          this.activeArea()
        },
        deep: true,
      },
      allObjectList: {
        handler(newVal) {
          this.drawMarks(newVal)
        },
        deep: true,
      },
    },
    created() {
      this.getObjectList()
    },
    methods: {
      onMapMounted(mapIns) {
        if (this.mapIns) return

        this.mapIns = mapIns

        this.drawIns = new MapboxDraw({
          displayControlsDefault: false,
          controls: {
            polygon: true,
            trash: true,
          },
          styles: [
            {
              id: 'gl-draw-line',
              type: 'line',
              filter: ['all', ['==', '$type', 'LineString'], ['!=', 'mode', 'static']],
              layout: {
                'line-cap': 'round',
                'line-join': 'round',
              },
              paint: {
                'line-color': '#00C911',
                // 'line-dasharray': [0.2, 2],
                'line-width': 2,
              },
            },

            // polygon fill
            {
              id: 'gl-draw-polygon-fill',
              type: 'fill',
              filter: ['all', ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
              paint: {
                'fill-color': '#00FF16',
                'fill-outline-color': '#00FF16',
                'fill-opacity': 0.15,
              },
            },
            {
              id: 'gl-draw-polygon-stroke-active',
              type: 'line',
              filter: ['all', ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
              layout: {
                'line-cap': 'round',
                'line-join': 'round',
              },
              paint: {
                'line-color': '#00C911',
                // 'line-dasharray': [0.2, 2],
                'line-width': 2,
              },
            },
            {
              id: 'gl-draw-polygon-and-line-vertex-halo-active',
              type: 'circle',
              filter: ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point'], ['!=', 'mode', 'static']],
              paint: {
                'circle-radius': 6,
                'circle-color': '#007B0A',
              },
            },
            // polygon mid points
            {
              id: 'gl-draw-polygon-midpoint',
              type: 'circle',
              filter: ['all', ['==', '$type', 'Point'], ['==', 'meta', 'midpoint']],
              paint: {
                'circle-radius': 5,
                'circle-color': '#FFA318',
              },
            },
          ],
        })

        this.mapIns.addControl(this.drawIns, 'top-right')

        this.mapIns._controlContainer.style.display = 'none'

        this.mapIns.on('draw.create', updateArea)
        this.mapIns.on('draw.delete', updateArea)
        this.mapIns.on('draw.update', updateArea)

        let that = this
        function updateArea(e) {
          that.form.range = that.drawIns.getAll()

          // 计算范围面积
          // if (data.features.length > 0) {
          //   const area = turf.area(data)
          //   const rounded_area = Math.round(area * 100) / 100  // 平方米
          // }
        }

        // 导航控制栏
        const element = document.querySelector('.mapboxgl-ctrl-top-right .mapboxgl-ctrl-group')
        if (element) {
          element.style.display = 'flex'
          element.style.height = '46px'
          element.style.padding = '8px'
          element.style.background = '#fff'
          element.style['box-shadow'] = '0px 1px 8px rgba(0, 0, 0, 0.2)'
          element.style.borderRadius = '8px'
          element.firstChild.title = ''
          element.firstChild.style.borderRadius = '0'
          element.lastChild.title = ''
          element.lastChild.style['border'] = 'none'
          element.lastChild.style['border-left'] = '1px solid #ddd'
          element.lastChild.style.borderRadius = '0'
        }

        this.dealLayers(this.dataSource)
        this.drawMarks(this.allObjectList)
      },

      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.drawIns.changeMode('simple_select')
            this.drawIns.deleteAll()
            this.$emit('ok', this.form, () => {
              this.loading = false
            })
          }
        })
      },

      cancel() {
        this.drawIns.changeMode('simple_select')
        this.drawIns.deleteAll()
        this.form.range = undefined
        this.$emit('cancel')
      },

      onMapStyleLoad(mapIns) {
        this.dealLayers(this.dataSource)
        this.drawMarks(this.allObjectList)
      },

      // 图层
      dealLayers(list) {
        if (!this.mapIns) return
        if (!list?.length) return

        if (this.mapIns.getSource('all-area')) {
          clearSourceAndLayer(this.mapIns, ['all-area'], ['all-area', 'all-point'])
        }

        let allGeoJson = { type: 'FeatureCollection', features: [] }
        list.forEach(ele => {
          allGeoJson.features.push.apply(allGeoJson.features, ele.lineRange?.features)

          ele?.lineRange &&
            allGeoJson.features.push({
              type: 'Feature',
              properties: { ...ele, lineRange: null },
              geometry: turf.centroid(ele.lineRange).geometry,
            })
        })

        this.mapIns.addSource('all-area', { type: 'geojson', data: allGeoJson })

        this.allGeoJson = allGeoJson
        mapBoundGeo(this.allGeoJson, this.mapIns)

        this.mapIns.addLayer({
          id: 'all-area',
          type: 'fill',
          source: 'all-area',
          paint: {
            'fill-color': '#00FF16',
            'fill-opacity': 0.35,
          },
          filter: ['==', '$type', 'Polygon'],
        })

        this.mapIns.addLayer({
          id: 'all-point',
          type: 'symbol',
          source: 'all-area',
          layout: {
            'text-size': 12,
            'text-field': ['get', 'lineName'],
            // 'text-offset': [0, 1.25],
            'text-anchor': 'center',
          },
          filter: ['==', '$type', 'Point'],
        })
      },
      activeArea() {
        if (!this.mapIns) return
        if (!this.rowInfo) {
          mapBoundGeo(this.allGeoJson, this.mapIns)
          if (this.mapIns.getSource('active-area')) {
            clearSourceAndLayer(this.mapIns, ['active-area'], ['active-area'])
          }
          return
        }
        if (this.mapIns.getSource('active-area')) {
          clearSourceAndLayer(this.mapIns, ['active-area'], ['active-area'])
        }
        this.mapIns.addSource('active-area', { type: 'geojson', data: this.rowInfo.lineRange })
        this.mapIns.addLayer({
          id: 'active-area',
          type: 'line',
          source: 'active-area',
          paint: {
            'line-color': '#00C911',
            'line-width': 2,
          },
        })

        mapBoundGeo(this.rowInfo.lineRange, this.mapIns, { top: 150, bottom: 150, left: 150, right: 150 })
      },

      drawMarks(list) {
        if (!this.mapIns) return
        if (!list?.length) return

        let allGeoJson = { type: 'FeatureCollection', features: [] }

        list.forEach(ele => {
          if (ele.latitude && ele.longitude) {
            allGeoJson.features.push({
              type: 'Feature',
              properties: ele,
              geometry: {
                type: 'Point',
                coordinates: [+ele.longitude, +ele.latitude],
              },
            })
          }
        })

        this.mapIns.addSource('all-makers', { type: 'geojson', data: allGeoJson })

        this.mapIns.addLayer({
          id: 'all-makers-circle',
          type: 'circle',
          source: 'all-makers',
          paint: {
            'circle-radius': 6,
            'circle-color': [
              'match',
              ['get', 'objectType'],
              '1',
              this.legendColors[0],
              '2',
              this.legendColors[1],
              '3',
              this.legendColors[2],
              '9',
              this.legendColors[3],

              '#000000',
            ],
          },
        })

        const popup = new mapboxgl.Popup({
          closeButton: false,
          closeOnClick: false,
        })

        this.mapIns.on('mouseenter', 'all-makers-circle', e => {
          const html = `<div>${e.features[0].properties.objectName}</div>`
          const coordinates = e.features[0].geometry.coordinates.slice()
          while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
            coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360
          }

          popup.setLngLat(coordinates).setHTML(html).addTo(this.mapIns)
        })

        this.mapIns.on('mouseleave', 'all-makers-circle', () => {
          popup.remove()
        })
      },

      onSearchChange: debounce(function (val) {
        this.getObjectList(val)
      }, 500),

      handleChange(val) {
        if (val === undefined) {
          this.getObjectList(val)
          return
        }
        this.activeObject = this.objectList.find(el => el.objectId == val)
        const { longitude, latitude } = this.activeObject
        if (latitude && longitude) {
          this.mapIns.flyTo({
            center: [+longitude, +latitude],
          })
        } else {
          this.$message.info('无坐标信息')
        }
      },

      getObjectList(objectName) {
        getPatrolObjectList({
          objectName,
          patrolType: this.$route.meta.query.patrolType,
        }).then(res => {
          if (!this.allObjectList?.length) {
            this.allObjectList = (res?.data || []).map(el => ({ ...el, objectType: `${el.objectType}` }))

            let allObject = [
              { name: '水利工程', data: [] },
              { name: '监测站点', data: [] },
              { name: '江河湖泊', data: [] },
              { name: '工程设备', data: [] },
            ]
            ;(res.data || []).forEach(el => {
              if (el.objectType == 1) {
                allObject[0].data.push(el)
              }
              if (el.objectType == 2) {
                allObject[1].data.push(el)
              }
              if (el.objectType == 3) {
                allObject[2].data.push(el)
              }
              if (el.objectType == 9) {
                allObject[3].data.push(el)
              }
            })

            this.allObject = allObject
          }

          this.objectList = res.data || []
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');
  .map {
    width: 100%;
    height: 100%;
    position: relative;
    .search {
      padding: 8px;
      border-radius: 8px;
      background-color: #fff;
      position: absolute;
      left: 10px;
      top: 10px;
      z-index: 1;
      box-shadow: 0px 1px 8px rgba(0, 0, 0, 0.2);
    }
    .tip {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1;
      height: 46px;
      line-height: 46px;
      padding: 0 16px;
      border-radius: 8px;
      background-color: #fff;
      color: #4e5969;
      box-shadow: 0px 1px 8px rgba(0, 0, 0, 0.2);
    }

    .form-panel {
      position: absolute;
      top: 80px;
      right: 10px;
      width: 320px;
      z-index: 1;
      background-color: #fff;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0px 1px 8px rgba(0, 0, 0, 0.2);
      .form-panel-title {
        color: #3d3d3d;
        font-size: 18px;
        font-weight: 600;
        line-height: 22px;
        padding: 0 0 8px 0px;
      }
    }

    .legend {
      background: linear-gradient(322deg, rgba(253, 254, 255, 0.8) 0%, rgba(244, 247, 252, 0.8) 100%);
      position: absolute;
      left: 10px;
      bottom: 10px;
      padding: 8px 12px;
      border-radius: 8px 8px 8px 8px;
      overflow: hidden;
      .legend-item:nth-child(n):not(:last-child) {
        margin-bottom: 4px;
      }
      .legend-item {
        display: flex;
        align-items: center;
        color: #4e5969;
        font-size: 14px;
        line-height: 32px;
        padding: 0 8px;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 6px 0px 20px 0px rgba(34, 87, 188, 0.1);
        border-radius: 4px 4px 4px 4px;

        .legend-icon {
          width: 10px;
          height: 10px;
          border-radius: 1px 1px 1px 1px;
          margin-right: 8px;
          background: pink;
        }
      }
    }
  }

  ::v-deep .mapboxgl-ctrl-top-left {
    display: none;
  }
  ::v-deep .mapboxgl-ctrl-top-right {
    right: 198px;
  }

  ::v-deep .ant-form-item {
    margin-bottom: 18px;
  }
  ::v-deep .ant-form-item-with-help {
    margin-bottom: 5px;
  }
</style>
