<template>
  <div style="display: flex; flex: 1; gap: 10px; margin-top: 16px">
    <div style="flex: 1; display: flex; flex-direction: column">
      <a-form-model labelAlign="right" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
        <a-row :gutter="16">
          <a-col :span="12" v-for="(item, index) in inOutWaters" :key="index">
            <a-form-model-item :label="item.name">
              <a-input-number size="small" v-model="inOutWaters[index].wlv" :min="0" :step="0.1" />
              m
            </a-form-model-item>
          </a-col>
          <br />
          <a-col :span="12">
            <a-form-model-item label="灌溉面积">
              <a-input-number size="small" v-model="irrBase.irrArea" :min="0" :step="0.1" />
              平方千米
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="灌溉深度">
              <a-input-number size="small" v-model="irrBase.irrHeight" :min="0" :step="0.1" />
              mm
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="灌溉开始时间">
              <a-date-picker
                size="small"
                format="YYYY-MM-DD HH:mm:ss"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                v-model="irrBase.startTime"
                showTime
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="灌溉时长">
              <a-input-number size="small" v-model="irrBase.hour" :min="0" :step="0.1" />
              h
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>

      <div style="flex: 1">
        <VxeTable
          size="small"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isDrop="false"
          :tablePage="false"
          :isShowTableHeader="false"
        />
      </div>
    </div>
    <div style="flex: 1; display: flex; flex-direction: column">
      <div style="display: flex; align-items: center; justify-content: end; margin-bottom: 16px">
        <a-select
          style="width: 240px"
          :options="selectOptions"
          placeholder="请选择"
          allowClear
          v-model="chartProjectSelect"
          @change="changeChartProjectSelect"
        />
      </div>
      <BaseEchart :height="'100%'" style="height: 100%" :option="chartOptions" @getEchartsIns="getEchartsIns" />
    </div>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'
  import { getProjectInOutWater, getDispathProjectList } from '../../../services.js'

  export default {
    name: 'Type1',
    components: { VxeTable, BaseEchart },
    props: ['baseInfo'],
    data() {
      return {
        inOutWaters: [],

        irrBase: {
          irrArea: undefined,
          irrHeight: undefined,
          startTime: undefined,
          hour: undefined,
        },

        loading: false,
        columns: [],
        list: [],

        selectOptions: [],
        chartProjectSelect: undefined,
        chartDataSource: [],
      }
    },
    computed: {
      chartOptions() {
        return {
          grid: {
            left: '4%',
            right: '4%',
            bottom: '5%',
            top: '8%',
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            textStyle: {
              color: '#000',
            },
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
          },
          xAxis: {
            type: 'category',
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: '#000',
              },
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#000',
              },
            },
          },
          yAxis: {
            type: 'value',
            name: this.selectOptions.find(el => el.value === this.chartProjectSelect)?.valueType,
            axisPointer: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#BBB',
              },
            },
            axisLabel: {
              textStyle: {
                color: '#000',
              },
            },
          },
          dataZoom: [
            {
              type: 'inside',
            },
          ],
          legend: { show: false },
          series: this.chartDataSource?.map((item, index) => {
            return {
              type: 'line',
              showSymbol: true,
              symbolSize: 2,
              symbol: 'circle',
              name: item.name,
              itemStyle: {
                borderRadius: [2, 2, 0, 0], //（顺时针左上，右上，右下，左下）
                color: item.color || '#1978E5',
              },
              data: item.data,
            }
          }),
        }
      },
    },
    watch: {
      list: {
        handler(val) {
          this.chartDataSource = val[0].projects
            .filter(el => el.projectCode === this.chartProjectSelect)
            .map(el => {
              return {
                name: el.projectName,
                data: val.map(item => [item.tm, item.projectsObj[el.projectCode].value]),
              }
            })
        },
        deep: true,
      },
    },
    activated() {
      if (this.inOutWaters.length > 0) return

      getProjectInOutWater({
        fcstRange: this.baseInfo.fcstRange,
      }).then(res => {
        this.inOutWaters = res.data
      })

      getDispathProjectList({
        dispathType: this.baseInfo.dispathType,
        fcstRange: this.baseInfo.fcstRange,
        startTime: this.baseInfo.startTime,
        endTime: this.baseInfo.endTime,
      }).then(res => {
        this.selectOptions = res.data[0].projects.map(el => ({ ...el, value: el.projectCode, label: el.projectName }))
        this.chartProjectSelect = this.selectOptions[0].value

        this.list = JSON.parse(JSON.stringify(res.data)).map(el => {
          const obj = {}
          el.projects.forEach(ele => (obj[ele.projectCode] = { ...ele }))
          return { ...el, projectsObj: obj }
        })

        this.columns = [
          {
            title: '时间',
            minWidth: 150,
            field: 'tm',
          },
          ...Object.keys(this.list[0].map).map(element => {
            return {
              title: element,
              headerAlign: 'center',
              children: this.list[0].map[element].map(el => ({
                title: el.projectName,
                minWidth: 180,
                headerAlign: 'center',
                slots: {
                  default: ({ row, rowIndex }) => (
                    <div class='cell-box'>
                      <a-input-number
                        value={this.list[rowIndex].projectsObj[el.projectCode].value}
                        size='small'
                        step={0.1}
                        min={0}
                        onChange={value => {
                          this.list[rowIndex].projectsObj[el.projectCode].value = value
                        }}
                      />
                      <a
                        style='margin-left: 5px'
                        onClick={() =>
                          this.list.forEach((item, index) => {
                            if (index > rowIndex) {
                              item.projectsObj[el.projectCode].value =
                                this.list[rowIndex].projectsObj[el.projectCode].value
                            }
                          })
                        }
                      >
                        向下填充
                      </a>
                    </div>
                  ),
                },
              })),
            }
          }),
        ]
      })
    },
    methods: {
      changeChartProjectSelect() {
        this.$nextTick(() => {
          this.chartDataSource = this.list[0].projects
            .filter(el => el.projectCode === this.chartProjectSelect)
            .map(el => {
              return {
                name: el.projectName,
                data: this.list.map(item => [item.tm, item.projectsObj[el.projectCode].value]),
              }
            })
        })
      },
      getEchartsIns(echartsIns) {
        this.echartsIns = echartsIns
        this.echartsIns.on('click', params => {})
      },

      save() {
        return {
          inOutWaters: this.inOutWaters,
          irrBase: this.irrBase,
          projectFlows: this.list,
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .cell-box {
    a {
      display: none;
    }
    &:hover {
      a {
        display: inline;
      }
    }
  }
</style>
