import request from '@/utils/request'

// 工程管理_维修养护_应急抢修-列表分页查询
export function getRepairPage(data) {
  return request({
    url: '/prjstd/repair/page',
    method: 'post',
    data,
  })
}
// 增加
export function addRepair(data) {
  return request({
    url: '/prjstd/repair/add',
    method: 'post',
    data,
  })
}
// 详情
export function getRepairById(params) {
  return request({
    url: '/prjstd/repair/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editRepair(data) {
  return request({
    url: '/prjstd/repair/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteRepair(params) {
  return request({
    url: '/prjstd/repair/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 验收
// export function checkRepair(params) {
//   return request({
//     url: '/prjstd/repair/check',
//     method: 'post',
//     headers: {
//       'Content-Type': 'application/x-www-form-urlencoded',
//     },
//     params,
//   })
// }

export function getProjectListByPropertyId(params) {
  return request({
    url: '/prjstd/maintenance/getProjectList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
