<template>
    <ant-modal :visible="open" :modal-title="modalTitle" :loading="modalLoading" modalWidth="1400" @cancel="cancel"
        modalHeight="900" :footer="null">
        <div slot="content" style="height: 100%; display: flex; flex-direction: column;">
            <!-- 第一行：模型选择 -->
            <div style="display: flex; flex-direction: row; align-items: center; justify-content: flex-end; margin-bottom: 16px;">
                <span>模型: </span>&nbsp;&nbsp;
                <a-select v-model="modelName" allowClear style="width: 40%;" placeholder="请选择" 
                    :options="modelOptions" show-search @change="handleModelChange"></a-select>
            </div>

            <!-- 第二行：信息组 -->
            <div style="display: flex; flex-direction: row; justify-content: space-between; margin-bottom: 16px;">
                <div class="info-card" v-for="(item, index) in infoCards" :key="index">
                    <div class="info-icon">
                        <a-icon :type="item.icon" style="font-size: 24px; color: #1890ff;" />
                    </div>
                    <div class="info-content">
                        <div class="info-title">{{ item.title }}</div>
                        <div class="info-value">{{ item.value }}</div>
                    </div>
                </div>
            </div>

            <!-- 第三行：地图和表格 -->
            <div style="flex: 1; display: flex; flex-direction: row; height: 500px;">
                <!-- 左侧地图 -->
                <div style="flex: 1; position: relative; border: 1px solid #e8e8e8; margin-right: 16px;">
                    <div v-if="mapLoading" style="
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 9999;">
                        <a-spin />
                    </div>
                    <div style="width: 100%; height: 100%; background-color: #f5f5f5; display: flex; justify-content: center; align-items: center;">
                        <span>地图区域</span>
                    </div>
                </div>

                <!-- 右侧表格 -->
                <div style="flex: 1;">
                    <a-tabs v-model="activeTab" @change="handleTabChange" style="height: 100%;">
                        <a-tab-pane key="area" tab="片区灾害评价">
                            <div style="height: 100%;">
                                <VxeTable 
                                    ref="areaTableRef" 
                                    size="small" 
                                    tableTitle="片区灾害评价"
                                    :columns="areaColumns" 
                                    :tableData="areaTableData" 
                                    :tablePage="false"
                                    :isShowSetBtn="false"
                                    height="400px"
                                ></VxeTable>
                            </div>
                        </a-tab-pane>
                        <a-tab-pane key="river" tab="河道灾害评价">
                            <div style="height: 100%;">
                                <VxeTable 
                                    ref="riverTableRef" 
                                    size="small" 
                                    tableTitle="河道灾害评价"
                                    :columns="riverColumns" 
                                    :tableData="riverTableData" 
                                    :tablePage="false"
                                    :isShowSetBtn="false"
                                    height="400px"
                                ></VxeTable>
                            </div>
                        </a-tab-pane>
                    </a-tabs>
                </div>
            </div>
        </div>
    </ant-modal>
</template>

<script>
import AntModal from '@/components/pt/dialog/AntModal'
import VxeTable from '@/components/VxeTable/index.vue'
import moment from 'moment'

export default {
    name: 'Flood',
    components: { AntModal, VxeTable },
    data() {
        return {
            open: false,
            modalLoading: false,
            modalTitle: '洪涝灾害评价',
            mapLoading: false,
            
            // 模型选择
            modelName: '1',
            modelOptions: [
                { label: '模型一', value: '1' },
                { label: '模型二', value: '2' }
            ],
            
            // 当前记录
            rowData: {},
            
            // 信息卡片数据
            infoCards: [],
            
            // 表格相关
            activeTab: 'area',
            areaColumns: [
                { type: 'seq', title: '序号', width: 60 },
                { title: '片区名称', field: 'areaName', minWidth: 120 },
                { title: '受涝面积(㎡)', field: 'floodArea', minWidth: 120 },
                { title: '持续时长(h)', field: 'duration', minWidth: 120 },
                { title: '涝灾减产率', field: 'reductionRate', minWidth: 120 }
            ],
            riverColumns: [
                { type: 'seq', title: '序号', width: 60 },
                { title: '河道名称', field: 'riverName', minWidth: 120 },
                { title: '最高水位(m)', field: 'maxWaterLevel', minWidth: 120 },
                { title: '超警戒水深', field: 'overWarningDepth', minWidth: 120 },
                { title: '淹没长度(km)', field: 'floodLength', minWidth: 120 }
            ],
            areaTableData: [],
            riverTableData: [],
            
            // 测试数据
            testData: {
                '1': {
                    infoCards: [
                        { icon: 'cloud', title: '预计累计降雨量', value: '125.6 mm' },
                        { icon: 'arrow-up', title: '断面最大超警戒水深', value: '0.8 m' },
                        { icon: 'fall', title: '涝灾减产率', value: '15.2%' },
                        { icon: 'area-chart', title: '涝灾面积', value: '256.8 ha' },
                        { icon: 'rise', title: '河道最高水位', value: '3.6 m' },
                        { icon: 'clock-circle', title: '涝灾延续时长', value: '48 h' }
                    ],
                    areaData: [
                        { areaName: '东部片区', floodArea: '125000', duration: '36', reductionRate: '12.5%' },
                        { areaName: '西部片区', floodArea: '98000', duration: '24', reductionRate: '8.3%' },
                        { areaName: '南部片区', floodArea: '156000', duration: '48', reductionRate: '18.7%' },
                        { areaName: '北部片区', floodArea: '78000', duration: '18', reductionRate: '5.2%' },
                        { areaName: '北部片区', floodArea: '78000', duration: '18', reductionRate: '5.2%' },
                        { areaName: '北部片区', floodArea: '78000', duration: '18', reductionRate: '5.2%' },
                        { areaName: '北部片区', floodArea: '78000', duration: '18', reductionRate: '5.2%' },
                        { areaName: '北部片区', floodArea: '78000', duration: '18', reductionRate: '5.2%' },
                        { areaName: '北部片区', floodArea: '78000', duration: '18', reductionRate: '5.2%' },
                        { areaName: '北部片区', floodArea: '78000', duration: '18', reductionRate: '5.2%' },
                        { areaName: '北部片区', floodArea: '78000', duration: '18', reductionRate: '5.2%' },
                        { areaName: '北部片区', floodArea: '78000', duration: '18', reductionRate: '5.2%' }
                    ],
                    riverData: [
                        { riverName: '东河', maxWaterLevel: '3.2', overWarningDepth: '0.5', floodLength: '2.8' },
                        { riverName: '西河', maxWaterLevel: '2.8', overWarningDepth: '0.3', floodLength: '1.5' },
                        { riverName: '南河', maxWaterLevel: '3.6', overWarningDepth: '0.8', floodLength: '3.2' },
                        { riverName: '北河', maxWaterLevel: '2.5', overWarningDepth: '0.2', floodLength: '1.2' },
                        { riverName: '北河', maxWaterLevel: '2.5', overWarningDepth: '0.2', floodLength: '1.2' },
                        { riverName: '北河', maxWaterLevel: '2.5', overWarningDepth: '0.2', floodLength: '1.2' },
                        { riverName: '北河', maxWaterLevel: '2.5', overWarningDepth: '0.2', floodLength: '1.2' },
                        { riverName: '北河', maxWaterLevel: '2.5', overWarningDepth: '0.2', floodLength: '1.2' }
                    ]
                },
                '2': {
                    infoCards: [
                        { icon: 'cloud', title: '预计累计降雨量', value: '178.3 mm' },
                        { icon: 'arrow-up', title: '断面最大超警戒水深', value: '1.2 m' },
                        { icon: 'fall', title: '涝灾减产率', value: '23.7%' },
                        { icon: 'area-chart', title: '涝灾面积', value: '342.5 ha' },
                        { icon: 'rise', title: '河道最高水位', value: '4.2 m' },
                        { icon: 'clock-circle', title: '涝灾延续时长', value: '72 h' }
                    ],
                    areaData: [
                        { areaName: '东部片区', floodArea: '185000', duration: '48', reductionRate: '18.5%' },
                        { areaName: '西部片区', floodArea: '142000', duration: '36', reductionRate: '15.2%' },
                        { areaName: '南部片区', floodArea: '210000', duration: '72', reductionRate: '25.6%' },
                        { areaName: '北部片区', floodArea: '125000', duration: '24', reductionRate: '10.8%' },
                        { areaName: '中部片区', floodArea: '168000', duration: '60', reductionRate: '20.3%' }
                    ],
                    riverData: [
                        { riverName: '东河', maxWaterLevel: '4.0', overWarningDepth: '0.8', floodLength: '3.5' },
                        { riverName: '西河', maxWaterLevel: '3.6', overWarningDepth: '0.6', floodLength: '2.8' },
                        { riverName: '南河', maxWaterLevel: '4.2', overWarningDepth: '1.2', floodLength: '4.5' },
                        { riverName: '北河', maxWaterLevel: '3.2', overWarningDepth: '0.4', floodLength: '2.2' },
                        { riverName: '中河', maxWaterLevel: '3.8', overWarningDepth: '0.7', floodLength: '3.0' }
                    ]
                }
            }
        }
    },
    methods: {
        // 取消按钮
        cancel() {
            this.open = false
            this.$emit('close')
        },
        
        // 显示模态框
        handleShow(row) {
            this.open = true
            this.modalLoading = true
            this.rowData = row
            
            // 默认加载模型一的数据
            this.loadModelData(this.modelName)
            
            setTimeout(() => {
                this.modalLoading = false
            }, 500)
        },
        
        // 切换模型
        handleModelChange(value) {
            this.loadModelData(value)
        },
        
        // 加载模型数据
        loadModelData(modelId) {
            const modelData = this.testData[modelId]
            if (modelData) {
                this.infoCards = modelData.infoCards
                this.areaTableData = modelData.areaData
                this.riverTableData = modelData.riverData
            }
        },
        
        // 切换Tab
        handleTabChange(activeKey) {
            this.activeTab = activeKey
        }
    },
    created() {
        // 初始化加载模型一的数据
        this.loadModelData(this.modelName)
    }
}
</script>

<style lang="less" scoped>
.info-card {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
    width: 15%;
    
    .info-icon {
        margin-right: 12px;
    }
    
    .info-content {
        display: flex;
        flex-direction: column;
        
        .info-title {
            font-size: 12px;
            color: #888;
        }
        
        .info-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
    }
}

::v-deep .modal-content {
    height: 100%;
}
</style>
