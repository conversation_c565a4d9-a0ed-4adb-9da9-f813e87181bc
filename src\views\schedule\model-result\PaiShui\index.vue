<template>
    <div style="position: relative; display: flex; flex-direction: column; height: 7.7rem; width: 100%; ">
        <!-- 地图 -->
        <div
            style="flex: 6;  height: 100%; width: 100%; position: relative; display: flex; flex-direction: row; justify-content: center; align-items: center;">

            <div style="position: relative;flex: 5; height: 100%; width: 100%;">
                <div :id="paiShuiMap" style="height: 100%; width: 100%;"></div>
                <!-- 颜色图例 -->
                <div
                    style="position: absolute;width: 250px;  top: 10px; right: 10px; background: rgba(255, 255, 255, 0.7); padding: 5px; border-radius: 3px; z-index: 1000;">
                    <div style="font-size: 10px; font-weight: bold; margin-bottom: 5px;">排水量</div>
                    <div style="display: flex; flex-direction: column; width: 100%;">
                        <div style="display: flex; height: 15px; width: 100%;">
                            <div v-for="(color, index) in colors" :key="index" :style="{
                                flex: 1,
                                height: '100%',
                                backgroundColor: color
                            }">
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-top: 3px;">
                            <span style="font-size: 10px;">{{ 0 }}</span>
                            <span style="font-size: 10px;">{{ maxFlow.toFixed(0) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div style="flex: 5;width: 100%; height: 100%; padding-left: 10px; ">
                <VxeTable ref="vxeDrainageTableRef" size="small" :isShowTableHeader="false"
                    :header-cell-class-name="headerCellClassName" :row-class-name="rowClassName"
                    :columns="columnsDrainage" :tableData="listDrainage" :tablePage="false">
                </VxeTable>
            </div>
        </div>
        <div style="flex: 4; height: 100%; width: 100%;position: relative; ">
            <!-- 折线图 -->
            <div style="position: absolute; top: 10px; left: 120px; width: 200px; height: 30px;z-index: 1000;"
                class="y_select">
                <a-select v-model="lineNames" allowClear
                    style="width: 100%; height: 25px; font-weight: 400; font-size: 12px;" placeholder="请选择" mode="tags"
                    :options="lineOptions" show-search></a-select>
            </div>
            <LineEchart :height="'245px'" style="margin-top: 10px;" :dataSource="lineChartData"
                :custom="lineChartCustom">
            </LineEchart>
        </div>
    </div>
</template>

<script lang="jsx">
import { getValueByKey, getOptions } from '@/api/common'
import { getSiteRainRes } from '../services'
import VxeTable from '@/components/VxeTable/index.vue'
import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
import axios from 'axios'
import LineEchart from '../Linechart/index.vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import * as turf from '@turf/turf'
import { mapBoundGeo } from '@/utils/mapBounds.js'
import { Icon } from 'vxe-pc-ui'
const colors = [
    "#E3F2FD", "#DEEDFC", "#D9E8FB", "#D4E3FA", "#CFDEF9", "#CAD9F8", "#C5D4F7", "#C0CFF6", "#BBCAF5", "#B6C5F4",
    "#B1C0F3", "#ACC0F2", "#A7C0F1", "#A2C0F0", "#9DC0EF", "#98C0EE", "#93C0ED", "#8EC0EC", "#89C0EB", "#84C0EA",
    "#7FC0E9", "#7AC0E8", "#75C0E7", "#70C0E6", "#6BC0E5", "#66C0E4", "#61C0E3", "#5CC0E2", "#57C0E1", "#52C0E0",
    "#4DC0DF", "#48C0DE", "#43C0DD", "#3EC0DC", "#39C0DB", "#34C0DA", "#2FC0D9", "#2AC0D8", "#25C0D7", "#20C0D6",
    "#1BC0D5", "#16C0D4", "#11C0D3", "#0CC0D2", "#07C0D1", "#02C0D0", "#00BDCF", "#00BBCE", "#00B8CD", "#00B6CC",
    "#00B3CB", "#00B1CA", "#00AEC9", "#00ACC8", "#00A9C7", "#00A7C6", "#00A4C5", "#00A2C4", "#009FC3", "#009DC2",
    "#009AC1", "#0098C0", "#0095BF", "#0093BE", "#0090BD", "#008EBC", "#008BBA", "#0089B9", "#0086B8", "#0084B7",
    "#0081B6", "#007FB5", "#007CB4", "#007AB3", "#0077B2", "#0075B1", "#0072B0", "#0070AF", "#006DAE", "#006BAD",
    "#0068AC", "#0066AB", "#0063AA", "#0061A9", "#005EA8", "#005CA7", "#0059A6", "#0057A5", "#0054A4", "#0052A3",
    "#004FA2", "#004DA1", "#004AA0", "#00489F", "#00459E", "#00439D", "#00409C", "#003E9B", "#003B9A", "#003999"
]

const lineColors = [
    '#507EF7', '#B5E241', "#FFD700", "#666666", "#FF0000", "#00FF00", "#0000FF", "#FF00FF", "#00FFFF", "#FFFF00",
]

export default {
    name: 'ShuiDongLi',
    mapIns: null, // 地图实例
    paiShuiData: [],
    components: { VxeTable, LineEchart, TimePlaySlider },
    data() {
        return {
            paiShuiMap: 'paiShuiMap', // 地图id
            colors,
            loading: false,
            lineNames: [],
            lineOptions: [],
            lineChartData: [],
            lineChartCustom: {
                shortValue: true, // 缩写坐标值
                xLabel: '', // x轴名称
                yLabel: '流量(m³/s)', //y轴名称
                yUnit: '', //y轴单位
                legend: true, // 图例
                showAreaStyle: true, // 颜色区域
                rYUnit: '', // 右侧y轴单位
                rYLabel: '降雨量(mm)', // 右侧y轴名称
                rYInverse: true,
                dataZoom: false,
                color: null,
                grid: {
                    left: '1%',
                    right: '1%',
                    bottom: '5%',
                    top: '15%',
                    containLabel: true,
                },
                legendOptions: {},
                legendTop: '1%',
                legendLeft: '35%',
                xAxisData: null // x轴数据
            },
            maxFlow: 0, // 最大流量
            columnsDrainage: [],
            listDrainage: [],
            times: [],
        }
    },
    props: {
        modelCode: { // 模型名称
            type: String,
            default: "",
            required: true,
        },
        modelScene: { // 模型名称
            type: Number,
            default: 1,
            required: true,
        }
    },
    created() {
        this.paiShuiMap = 'paiShuiMap' + new Date().getMilliseconds()
    },
    watch: {
        async modelCode(newVal) {
            await this.refreshModelRes()
            this.paiShuiSource.features.forEach(feature => {
                feature.properties["color"] = this.getColorByValue(0)
                feature.properties["flow"] = 0
            })
            for (const key in this.sumFlow) { // 最大流量
                this.paiShuiSource.features.forEach(feature => {
                    if (feature.properties.name && key.startsWith(feature.properties.name)) {
                        feature.properties["color"] = this.getColorByValue(this.sumFlow[key])
                        feature.properties["flow"] = this.sumFlow[key]
                    }
                })
            }
            let temp = JSON.parse(JSON.stringify(this.paiShuiSource))
            temp.features = temp.features.filter(feature => feature.properties.flow)
            mapBoundGeo(temp, this.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })  // 计算边界
            this.mapIns.getSource('paiShuiSource').setData(temp)
        },
        // echarts 左上方下拉表中的片区名称改变时，重新获取数据
        lineNames(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.lineChartData = this.getAreaDataByName(newVal)
            }
        }
    },
    async mounted() {
        this.$emit('changeLoading', true)
        getValueByKey('patrolCentralPoint').then(res => {
            this.center = res.data.split(',').map(el => +el)
            this.$nextTick(async () => {
                await this.refreshModelRes()
                await this.initMap()
            })
        })
    },

    methods: {
        getColorByValue(value) { // 根据值获取颜色
            const maxValue = this.maxFlow // 假设最大值为100
            const index = Math.floor((value / maxValue) * (colors.length - 1)) // 计算索引
            return colors[index] // 返回对应的颜色
        },
        async refreshModelRes() {
            this.paiShuiData = []
            this.lineOptions = []
            this.lineChartData = []
            this.listDrainage = []
            this.times = []
            this.maxFlow = 0
            if (!this.modelCode) {
                return
            }
            await getSiteRainRes({
                modelId: this.modelCode
            }).then(res => {
                if (res.data && res.data.length > 0) {

                    this.paiShuiData = res.data
                    let tempColumns = [
                        { type: 'seq', title: '序号', width: 50 },
                        {
                            title: '时间',
                            field: 'time',
                            fixed: 'left',
                            minWidth: 180,
                            showOverflow: 'tooltip',
                        }
                    ]
                    this.sumFlow = {} // 各个排水片区的累计水量
                    let lineOptionsTemp = [] // 折线图的下拉列表选项
                    if (this.paiShuiData[0].sites && this.paiShuiData[0].sites.length > 0) {
                        // 获取表头 与 折线图的下拉列表选项
                        this.paiShuiData[0].sites.forEach(element => {
                            tempColumns.push({
                                title: element.areaName.slice(0, element.areaName.length - 4) + "(m³/s)", // 去除后缀 集水区
                                field: element.areaName,
                                minWidth: 150,
                                showOverflow: 'tooltip',
                            })
                            lineOptionsTemp.push({
                                label: element.areaName.slice(0, element.areaName.length - 4),
                                value: element.areaName,
                            })
                            this.sumFlow[element.areaName] = 0
                        });
                        this.columnsDrainage = tempColumns
                        this.lineOptions = lineOptionsTemp
                        // 初始化折线图数据
                        this.lineNames = [this.lineOptions[0].value]
                        this.lineChartData = this.getAreaDataByName(this.lineNames)

                        // 排水片 VUEXTABLE 表格数据
                        let tempData = []
                        this.paiShuiData.forEach(element => {
                            let tempObj = {}
                            tempObj['time'] = element['tm']
                            element.sites.forEach(item => {
                                tempObj[item.areaName] = (+item.flow).toFixed(3)
                                this.sumFlow[item.areaName] += (+item.flow) * 0.36
                            })
                            tempData.push(tempObj)
                        });
                        for (const key in this.sumFlow) {
                            if (this.sumFlow[key] > this.maxFlow) {  // 最大流量
                                this.maxFlow = Math.ceil(this.sumFlow[key])
                            }
                        }
                        this.listDrainage = tempData
                    }
                    // 初始化时间轴
                    this.times = this.paiShuiData.map(el => el.tm)
                }
            })
        },
        /**
         * 获取 this.paiShuiData 中指定 areaName 的数据
         * @param {*} name 片区名称
         * @returns 数据
         */
        getAreaDataByName(names) {
            let times = []
            let tempNames = {}
            names.forEach(element => {
                tempNames[element] = {}
                tempNames[element]['rain'] = []
                tempNames[element]['wlevel'] = []
            });
            this.paiShuiData.forEach(element => {
                times.push(element.tm)
                element.sites.forEach(item => {
                    if (item.areaName in tempNames) {
                        tempNames[item.areaName].wlevel.push(item.flow)
                        tempNames[item.areaName].rain.push(item.rain)
                    }
                })
            });
            const dataArr = []
            let maxYmax1 = 0 // 最大流量
            let legendData = []
            console.log("this.$route.query = ",this.$route.query,"this.modelScene = ",this.modelScene)
            if (this.$route.query.modelScene && this.$route.query.modelScene == 1 || this.modelScene && this.modelScene == 1) { // 历史复演情景
                Object.keys(tempNames).forEach((key, index) => { // 最大降雨量
                    dataArr.push({
                        name: (key ? key.slice(0, key.length - 5) : "") + "流量",
                        data: tempNames[key].wlevel,
                        type: 'line',
                        color: lineColors[index % lineColors.length],
                        yAxisIndex: 0,
                        smooth: true,
                    })
                    legendData.push({
                        name: (key ? key.slice(0, key.length - 5) : "") + "流量",
                        color: lineColors[index % lineColors.length],
                        icon: 'circle'
                    })
                    dataArr.push({
                        name: (key ? key.slice(0, key.length - 5) : "") + "降雨量",
                        color: lineColors[index % lineColors.length],
                        data: tempNames[key].rain,
                        yAxisIndex: 1,
                        type: 'bar',
                        smooth: true,
                    })
                    legendData.push({
                        name: (key ? key.slice(0, key.length - 5) : "") + "降雨量",
                        color: lineColors[index % lineColors.length],
                        icon: 'rect',
                    })
                    maxYmax1 = Math.max(...tempNames[key].rain) > maxYmax1 ? Math.max(...tempNames[key].rain) : maxYmax1
                })
            } else {
                Object.keys(tempNames).forEach((key, index) => { // 最大降雨量
                    dataArr.push({
                        name: (key ? key.slice(0, key.length - 5) : "") + "排水量",
                        data: tempNames[key].wlevel,
                        type: 'line',
                        color: lineColors[index % lineColors.length],
                        yAxisIndex: 0,
                        smooth: true,
                    })
                    legendData.push({
                        name: (key ? key.slice(0, key.length - 5) : "") + "排水量",
                        color: lineColors[index % lineColors.length],
                        icon: 'circle'
                    })
                })
                dataArr.push({
                    name: "时段降雨量",
                    color: lineColors[0],
                    data: tempNames[Object.keys(tempNames)[0]].rain,
                    yAxisIndex: 1,
                    type: 'bar',
                    smooth: true,
                })
                legendData.push({
                    name: "时段降雨量",
                    color: lineColors[0],
                    icon: 'rect',
                })
                maxYmax1 = Math.max(...tempNames[Object.keys(tempNames)[0]].rain) > maxYmax1 ? Math.max(...tempNames[Object.keys(tempNames)[0]].rain) : maxYmax1
            }

            this.lineChartCustom.yMax1 = 2 * maxYmax1
            this.lineChartCustom.xAxisData = times
            this.lineChartCustom.legendOptions['data'] = legendData
            this.lineChartCustom.legendOptions.itemWidth = 4
            this.lineChartCustom.legendOptions.itemHeight = 10
            return dataArr
        },
        // 表头样式
        headerCellClassName({ column }) {
            return 'col-blue'
        },
        // 行样式
        rowClassName() {
            return 'row-green'
        },
        async initMap() {
            mapboxgl.accessToken = 'pk.eyJ1IjoiaGhjY2RldiIsImEiOiJjbTBxaDBhdmswYzZjMmpwdzE4eWU2d2NvIn0._XkHfjxUcOLIZ7bIJUcbWw'
            this.mapIns = new mapboxgl.Map({
                container: this.paiShuiMap,
                style: {
                    version: 8,
                    sources: {
                        "wmts-tianditu-img-source": {
                            type: 'raster',
                            tiles: [
                                `${process.env.VUE_APP_TIANDI_BASE}/img_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
                            ],
                            tileSize: 256,
                        },
                        "wmts-tianditu-cva-source": {
                            type: 'raster',
                            tiles: [
                                `${process.env.VUE_APP_TIANDI_BASE}/cia_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
                            ],
                            tileSize: 256,
                        }
                    },
                    layers: [
                        {
                            id: 'wmts-tianditu-img-layer',
                            type: 'raster',
                            source: "wmts-tianditu-img-source"
                        },
                        {
                            id: 'wmts-tianditu-cva-layer',
                            type: 'raster',
                            source: "wmts-tianditu-cva-source"
                        },
                    ]
                },
                center: this.center,
                zoom: 10,
                maxZoom: 17.49, // 天地图大于这个值时，图层会消失
                // minZoom: 4,
                pitch: 0, // 相对于地面3D视角的角度
                antialias: false, //抗锯齿，通过false关闭提升性能
            })

            // ### 标签汉化 //style已经设置中文
            // this.mapIns.addControl(new MapboxLanguage({ defaultLanguage: 'zh-Hans' }))
            // ### 添加导航控制条
            this.mapIns.addControl(new mapboxgl.NavigationControl({ showCompass: false }), 'top-left')
            // 鼠标样式
            this.mapIns.getCanvas().style.cursor = 'auto'
            this.mapIns.on('style.load', async () => {
                const self = this
                let paiShui = "https://www.sthzxgq.cn:11120/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=sthgq_vector%3Asthgq_paishuiqu&maxFeatures=5000&outputFormat=application%2Fjson"
                let hoveredPolygonId = null;
                await this.drawIrr(this.mapIns)

                // 模型名称改变时重新获取数据
                await axios(paiShui).then(resp => {
                    self.paiShuiSource = resp.data
                    for (const key in self.sumFlow) { // 最大流量
                        resp.data.features.forEach(feature => {
                            if (feature.properties.name && key.startsWith(feature.properties.name)) {
                                feature.properties["color"] = self.getColorByValue(self.sumFlow[key])
                                feature.properties["flow"] = self.sumFlow[key]
                            }
                        })
                    }
                    let temp = JSON.parse(JSON.stringify(resp.data))
                    temp.features = temp.features.filter(ele => ele.properties.flow)

                    if (self.paiShuiSource.features.length > 0) {
                        mapBoundGeo(temp, self.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })  // 计算边界
                    } // 没有数据时，不显示
                    self.mapIns.addSource('paiShuiSource', {
                        type: 'geojson',
                        data: temp, //区划的面数据
                        'generateId': true // 确保所有特征都有唯一的ID
                    })
                    self.mapIns.addLayer({
                        id: 'gq-area-paiShui',
                        type: 'fill',
                        source: "paiShuiSource",
                        paint: {
                            'fill-color': ['get', "color"], // blue color fill
                            'fill-opacity': [
                                'case',
                                ['boolean', ['feature-state', 'hover'], false],
                                1,
                                0.4
                            ]
                        }
                    })
                    self.mapIns.addLayer({
                        'id': 'gq-area-paiShui-borders',
                        'type': 'line',
                        'source': 'paiShuiSource',
                        'layout': {},
                        'paint': {
                            'line-color': '#FFF',
                            'line-opacity': 1,
                            'line-width': 2
                        }
                    });
                    self.mapIns.addInteraction('gq-area-paiShui-click-interaction', {
                        type: 'click',
                        target: { layerId: 'gq-area-paiShui' },
                        handler: (e) => {
                            // Copy coordinates array.
                            const coordinates = turf.centroid(turf.polygon(e.feature.geometry.coordinates)).geometry.coordinates;
                            const description = e.feature.properties.name;
                            let content = `<div class='outerPaiShui'>
                                                    <div class='innerTop'>${e.feature.properties.name}</div>
                                                    <div class='innerBottom'>${"总排水量： " + e.feature.properties.flow.toFixed(2) + "万m³"}</div>
                                                </div>`
                            self.lineNames = [description + "集水区"]
                            new mapboxgl.Popup()
                                .setLngLat(coordinates)
                                .setHTML(content)
                                .addTo(self.mapIns);
                        }
                    });
                    self.mapIns.on('mousemove', 'gq-area-paiShui', (e) => {
                        if (e.features.length > 0) {
                            if (hoveredPolygonId !== null) {
                                self.mapIns.setFeatureState(
                                    { source: 'paiShuiSource', id: hoveredPolygonId },
                                    { hover: false }
                                );
                            }
                            hoveredPolygonId = e.features[0].id;
                            self.mapIns.setFeatureState(
                                { source: 'paiShuiSource', id: hoveredPolygonId },
                                { hover: true }
                            );
                        }
                    });
                    self.mapIns.on('mouseleave', 'gq-area-paiShui', () => {
                        if (hoveredPolygonId !== null) {
                            self.mapIns.setFeatureState(
                                { source: 'paiShuiSource', id: hoveredPolygonId },
                                { hover: false }
                            );
                        }
                        hoveredPolygonId = null;
                    });
                })
                this.mapIns.resize();
                this.$emit('changeLoading', false)
            })
        },
        async drawIrr(mapIns) {
            //蒙版
            await axios(
                "https://www.sthzxgq.cn:11120/geoserver/district/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=district:zhejiang&filter=<PropertyIsEqualTo><PropertyName>code</PropertyName><Literal>330481</Literal></PropertyIsEqualTo>"
            ).then(async res => {
                //蒙版边界
                this.mapIns.addLayer({
                    id: 'mb-line',
                    type: 'line',
                    source: {
                        type: 'geojson',
                        data: res.data, //区划的面数据
                    },
                    paint: {
                        'line-color': '#458fad', //'rgba(9, 236, 255, 1)',
                        'line-width': 4,
                        'line-dasharray': [0.2, 0.2],
                    },
                    layout: {
                        visibility: 'visible',
                    },
                })

                // 蒙版图层   //通过边界数据反选 达到挖洞效果
                this.mapIns.addLayer({
                    id: 'mb-tag',
                    type: 'fill',
                    source: {
                        type: 'geojson',
                        data: {
                            type: 'Feature',
                            geometry: {
                                type: 'Polygon',
                                coordinates: [
                                    [
                                        [-180, 90],
                                        [180, 90],
                                        [180, -90],
                                        [-180, -90],
                                    ],
                                    res.data.features[0].geometry.coordinates[0][0],
                                ],
                            },
                        },
                    },
                    paint: { 'fill-color': 'rgba(0,0,0,0.4)' },
                    layout: { visibility: 'visible' },
                })
            })
            // 灌区边界
            await getValueByKey('gis.irr.boundary').then(res => {
                axios(res.data).then(resp => {
                    mapIns.addLayer({
                        id: 'gq-area-line',
                        type: 'line',
                        source: {
                            type: 'geojson',
                            data: resp.data, //区划的面数据
                        },
                        paint: {
                            'line-color': '#bc7c2b',
                            'line-width': 2,
                        },
                    })
                })
            })

            //灌区面
            await getValueByKey('gis.irr.area').then(res => {
                axios(res.data).then(resp => {
                    mapIns.addLayer({
                        id: 'gq-area',
                        type: 'fill',
                        source: {
                            type: 'geojson',
                            data: resp.data,
                        },
                        paint: {
                            'fill-color': 'rgba(2, 237, 164, 0.1)',
                            // 'fill-outline-color': '#03FFCD', // 边框颜色（必填）边框宽度固定为1像素，无法调整
                        },
                        layout: { visibility: 'visible' },
                    })
                })
            })

            // 水系
            await getOptions('main_canal').then(res => {
                axios(
                    // `${import.meta.env.VITE_GEOSERVER_BASE}/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=sthgq_vector:HP005`,
                    `https://www.sthzxgq.cn:11120/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=sthgq_vector:HP005`,
                ).then(resp => {
                    const arr = []
                    res.data.forEach(el => {
                        arr.push(...[+el.key, el.option1])
                    })

                    mapIns.addLayer({
                        id: 'main_canal',
                        type: 'line',
                        source: {
                            type: 'geojson',
                            data: resp.data,
                        },
                        paint: {
                            'line-width': [
                                'case',
                                ['==', ['get', 'id'], 10004],
                                3,
                                ['==', ['get', 'id'], 10005],
                                3,
                                ['==', ['get', 'id'], 20005],
                                3,
                                1,
                            ],
                            'line-color': [
                                'match',
                                ['get', 'id'], // 读取属性名

                                ...arr,

                                '#01FE1B', // 默认颜色
                            ],
                        },
                    })
                })
            })
        }
    },
}

</script>
<style lang="less" scoped>
::v-deep .mapboxgl-popup-content {
    padding: 0px;

    .mapboxgl-popup-close-button {
        color: #fff;
    }
}

.y_select {
    width: 100%;

    ::v-deep .ant-select-selection--multiple {
        .ant-select-selection__rendered {
            display: flex;
            flex-wrap: nowrap;
            /* 防止换行 */
            overflow: hidden;
            /* 隐藏超出部分 */
        }

        .ant-select-selection__rendered ul {
            display: flex;
            width: 100%;

            &>li {
                flex-shrink: 0;
                /* 防止子元素缩小 */
            }

            .ant-select-search {
                flex: 1;
            }

            .ant-select-search__field {
                width: 100% !important;
            }
        }

        .ant-select-selection-item {
            white-space: nowrap;
            /* 防止文本换行 */
            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            /* 显示省略号 */
        }
    }
}


::v-deep .outerPaiShui {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .innerTop {
        width: 200px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        font-weight: 500;
        color: #fff;
        background-color: rgb(83, 132, 254);
    }

    .innerBottom {
        // height: 40px;
        // line-height: 40px;
        // background-color: red;
        text-align: center;
        font-size: 11px;
        font-weight: 350;
        color: #000000;
        margin-top: 5px;
        // margin-bottom: 5px;
    }

    .innerBottom:last-child {
        margin-bottom: 5px;
    }

}
</style>
