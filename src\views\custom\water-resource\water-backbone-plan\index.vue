<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="年份">
        <a-input v-model="queryParam.year" placeholder="年份" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <FormDrawer v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getPlanList, deleteWaterBackbone } from './services'
  import FormDrawer from './modules/FormDrawer.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import { filter } from 'lodash'

  export default {
    name: 'WaterBackbonePlan',
    components: {
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    data() {
      return {
        isChecked: false,

        showForm: false,
        showFormDetails: false,

        list: [],
        tableTitle: '骨干工程计划用水量',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          year: '',
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
          },
          {
            title: '年份',
            field: 'year',
            minWidth: 100,
          },
          {
            title: '骨干工程计划用水量(万m³)',
            field: 'planWaterRate',
            minWidth: 100,
          },
          {
            title: '创建人',
            field: 'createdUserName',
            width: 120,
          },

          {
            title: '创建时间',
            field: 'createdTime',
            width: 180,
          },

          {
            title: '操作',
            field: 'operate',
            width: 100,
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleEdit(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {},
    methods: {
      filterCounty(nodes) {
        if (!nodes) {
          return []
        }
        let countys = []
        for (let i = 0; i < nodes.length; i++) {
          let e = nodes[i]
          if (e.icon == '4') {
            countys.push(e)
          }
          let tmp = this.filterCounty(e.children)
          if (tmp.length > 0) {
            countys = countys.concat(tmp)
          }
        }
        return countys
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getPlanList(this.queryParam).then(response => {
          this.list = response?.data
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.id)
        // this.names = valObj.records.map(item => item.planName)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          pageNum: 1,
          sort: [],
        }
        this.planTimes = []
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle())
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.id ? [row?.id] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteWaterBackbone({ ids: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
