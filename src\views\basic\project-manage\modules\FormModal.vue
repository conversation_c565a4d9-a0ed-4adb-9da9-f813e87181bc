<template>
  <!-- 代表站配置 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="550"
  >
    <div slot="content">
      <div class="table-panel" layout="vertical">
        <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
          <a-row class="form-row" :gutter="32">
            <!-- 1.圩内水位站 2.圩外水位站 3水位站 4流量站 5雨量站 -->
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="上河水位">
                <a-tree-select
                  v-model="form.WNWaterSite"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="siteOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'name',
                    key: 'id',
                    value: 'key',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="下河水位">
                <a-tree-select
                  v-model="form.WWWaterSite"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="siteOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'name',
                    key: 'id',
                    value: 'key',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="水位站">
                <a-tree-select
                  v-model="form.WaterSite"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="siteOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'name',
                    key: 'id',
                    value: 'key',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="流量站">
                <a-tree-select
                  v-model="form.FlowSite"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="siteOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'name',
                    key: 'id',
                    value: 'key',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="雨量站">
                <a-tree-select
                  v-model="form.RainSite"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="siteOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'name',
                    key: 'id',
                    value: 'key',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="水质站">
                <a-tree-select
                  v-model="form.WaterQualitySite"
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="siteOptions"
                  show-search
                  treeNodeFilterProp="title"
                  allowClear
                  placeholder="请选择"
                  :replaceFields="{
                    children: 'children',
                    title: 'name',
                    key: 'id',
                    value: 'key',
                  }"
                  tree-default-expand-all
                ></a-tree-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import Initializer from '@/core/bootstrap'
  import {
    getNextSort,
    getParentTree,
    addProject,
    updateProject,
    getProject,
    getNewCategoryTree,
    getProjectSite,
    addProjectSite,
  } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormModal',
    props: ['siteTypeList'],
    components: { AntModal },
    data() {
      return {
        siteOptions: [],
        newList: [],
        projectId: null,
        loading: false,
        modalLoading: false,
        formTitle: '',
        open: false,
        rowInfo: null,

        // 表单参数
        form: {
          WNWaterSite: null,
          WWWaterSite: null,
          WaterSite: null,
          FlowSite: null,
          RainSite: null,
          WaterQualitySite: undefined,
        },
        rules: {
          siteId: [{ required: true, message: '站点名称不能为空', trigger: 'change' }],
          siteType: [{ required: true, message: '站点类别不能为空', trigger: 'change' }],
        },
      }
    },
    filters: {},
    created() {
      // 字典获取下拉
      this.init()
    },
    computed: {},
    watch: {},
    methods: {
      init() {
        let params = { siteIds: '' }
        getNewCategoryTree(params).then(res => {
          let tmpData = res?.data || []
          this.siteOptions = this.getChildren(tmpData, 'data')
        })
      },
      getChildren(tree, type) {
        let newTree = []
        for (let i = 0; i < tree.length; i++) {
          if (tree[i].type === type) {
            newTree.push(tree[i])
          } else if (tree[i].children && tree[i].children.length > 0) {
            let children = this.getChildren(tree[i].children, type)
            if (children.length > 0) {
              tree[i].children = children
              newTree.push(tree[i])
            }
          }
        }
        return newTree
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== null) {
          this.$refs.form.resetFields()
        }
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '配置代表站'
        this.rowInfo = record
        this.projectId = record.projectId
        this.modalLoading = true
        getProjectSite({ projectId: record.projectId }).then(res => {
          this.modalLoading = false
          if (res?.data) {
            let newList = Object.keys(this.form).map(type => ({
              [type]:
                res?.data.find(
                  site =>
                    site.siteType ==
                    (type == 'WNWaterSite'
                      ? 1
                      : type == 'WWWaterSite'
                        ? 2
                        : type == 'WaterSite'
                          ? 3
                          : type == 'FlowSite'
                            ? 4
                            : type == 'RainSite'
                              ? 5
                              : type == 'WaterQualitySite'
                                ? 6
                                : 0),
                )?.siteId || null,
            }))
            this.form = newList.reduce((acc, cur) => ({ ...acc, ...cur }), {})
          } else {
          }
        })
      },
      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            let sites = Object.keys(this.form).map((key, index) => {
              return {
                projectId: this.projectId,
                siteId: this.form[key],
                // WNWaterSite、WWWaterSite、WaterSite、FlowSite、RainSite
                siteType:
                  key == 'WNWaterSite'
                    ? 1
                    : key == 'WWWaterSite'
                      ? 2
                      : key == 'WaterSite'
                        ? 3
                        : key == 'FlowSite'
                          ? 4
                          : key == 'RainSite'
                            ? 5
                            : key == 'WaterQualitySite'
                              ? 6
                              : 0,
              }
            })
            let params = []
            params = sites.filter(item => item.siteId && item.siteId !== null)
            if (params.length == 0) {
              this.$message.info('至少配置一个站！', 3)
              this.loading = false
              return
            }

            addProjectSite(params)
              .then(response => {
                this.$message.success('配置成功', 3)
                this.open = false
                this.loading = false
                this.$emit('ok')
              })
              .catch(err => (this.loading = false))
          } else {
            return false
          }
        })
      },
    },
  }
</script>
