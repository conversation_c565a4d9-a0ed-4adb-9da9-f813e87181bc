<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="培训名称">
        <a-input v-model="queryParam.trainingName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>

      <a-form-item label="培训时间">
        <a-range-picker
          allow-clear
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          v-model="queryParam.trainingTime"
          :disabled-date="disabledDate"
          @openChange="() => (disabledDealDate = null)"
          @change="onRangeChange"
          @calendarChange="calendarChange"
        />
      </a-form-item>

      <a-form-item label="执行情况">
        <a-select
          allowClear
          v-model="queryParam.statusImplementation"
          placeholder="请选择"
          :options="statusImplementationOptions"
        ></a-select>
      </a-form-item>

      <a-form-model-item label="所属工程">
        <a-tree-select
          v-model="queryParam.projectId"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="projectOptions"
          show-search
          treeNodeFilterProp="title"
          allowClear
          placeholder="请选择"
          :replaceFields="{
            children: 'children',
            title: 'projectName',
            key: 'projectId',
            value: 'projectId',
          }"
          tree-default-expand-all
        ></a-tree-select>
      </a-form-model-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>

            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormModal
          v-if="showFormModal"
          :statusImplementationOptions="statusImplementationOptions"
          :projectOptions="projectOptions"
          ref="formModalRef"
          :unitOptions="unitOptions"
          @ok="onOperationComplete"
          @close="showFormModal = false"
        />
        <DetailModal
          v-if="showDetailModal"
          :statusImplementationOptions="statusImplementationOptions"
          ref="detailModalRef"
          @close="showDetailModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'
  import { getTrainingPage, deleteTraining } from './services'
  import { getUnitPage } from '@/views/standardized/unit-manage/unit-info/services'
  import { getOptions, getProjectTree, getDeptTree } from '@/api/common'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormModal from './modules/FormModal.vue'
  import DetailModal from './modules/DetailModal.vue'
  import getEquipmentTree from '@/utils/getEquipmentTree'

  export default {
    name: 'TrainingMaterials',
    components: {
      VxeTable,
      VxeTableForm,
      FormModal,
      DetailModal,
    },
    data() {
      return {
        showFormModal: false,
        showDetailModal: false,
        statusImplementationOptions: [
          { label: '完成', value: 1 },
          { label: '未完成', value: 2 },
        ],
        projectOptions: [],
        unitOptions: [],
        list: [],
        tableTitle: '培训资料',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        disabledDealDate: null,
        queryParam: {
          trainingName: undefined,
          trainingTime: [],
          startTime: undefined,
          endTime: undefined,
          statusImplementation: undefined,
          projectId: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '培训名称',
            field: 'trainingName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
          {
            title: '培训计划',
            field: 'trainingPlan',
            minWidth: 140,
          },
          {
            title: '培训时间',
            field: 'trainingTime',
            minWidth: 140,
            sortable: true,
          },
          {
            title: '执行情况',
            field: 'statusImplementation',
            minWidth: 120,
            slots: {
              default: ({ row, rowIndex }) => {
                return this.statusImplementationOptions.find(el => el.value == row.statusImplementation).label
              },
            },
          },
          {
            title: '管理单位',
            field: 'unitName',
            minWidth: 100,
          },
          {
            title: '操作',
            field: 'operate',
            width: 140,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      // 获取工程树
      getProjectTree({ objectCategoryId: this.queryParam.treeNodeId }).then(res => {
        this.projectOptions = res.data
      })

      // getUnitPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
      //   this.unitOptions = (res.data?.data || []).map(el => ({ label: el.unitName, value: el.unitId }))
      // })
      getDeptTree({ deptName: '', type: 2 }).then(res => {
        this.unitOptions = res.data
        // this.unitOptions = getEquipmentTree(res.data, 'deptId')
        console.log('222 unitOptions ', this.unitOptions)
      })

      this.getList()
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      disabledDate(current) {
        let start = moment(this.disabledDealDate).subtract(7, 'd')
        let end = moment(this.disabledDealDate).add(7, 'd')
        return current > end || current < start
      },
      calendarChange(dates) {
        if (dates?.length == 1) {
          this.disabledDealDate = dates[0]
        }
      },
      onRangeChange(dates) {
        this.queryParam.startTime = dates[0]
        this.queryParam.endTime = dates[1]
      },

      /** 查询列表 */
      getList() {
        this.showFormModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getTrainingPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          trainingName: undefined,
          positionType: undefined,
          projectId: undefined,
          trainingTime: [],
          startTime: undefined,
          endTime: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.trainingId)
        this.names = valObj.records.map(item => item.trainingName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleAdd(this.queryParam.parentId))
      },
      /* 修改 */
      handleUpdate(row) {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleUpdate(row))
      },
      // 详情
      handleDetail(row) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleDetail(row))
      },

      // 导出
      handleExport() {},

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const trainingIds = row.trainingId ? [row.trainingId] : this.ids
        const names = row.trainingName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteTraining({ trainingIds: trainingIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped></style>
