export const terminals = [
  { label: '雨量', value: 'rainfall' },
  { label: '水位', value: 'waterLevel' },
  { label: '流量', value: 'flow' },
  { label: 'pH', value: 'ph' },
  { label: '温度', value: 'temperature' },
  { label: '电导率', value: 'electricConductivity' },
  { label: '溶解氧', value: 'dissolvedOxygen' },
]

// 类型(1逐时 2逐日 3逐月 4瞬时)
export const timeRangesOptions = [
  { label: '瞬时', value: '4' },
  { label: '逐时', value: '1' },
  { label: '逐日', value: '2' },
]
export const chartColors = [
  '#549DFA',
  '#AC3B2A',
  '#567EFE',
  '#20E5CB',
  '#BC20E5',
  '#FFD15C',
  '#7F43F8',
  '#DE7C45',
  '#34B548',
  '#A7D2F4',
]

export const multiSourceOptions = {
  ph: { label: 'pH', value: 'ph', unit: '', color: '#009688' },
  temperature: { label: '温度', value: 'temperature', unit: '(℃)', color: '#EF8432' },
  electricConductivity: { label: '电导率', value: 'electricConductivity', unit: '(μS/cm)', color: '#16FFD8' },
  dissolvedOxygen: { label: '溶解氧', value: 'dissolvedOxygen', unit: '(mg/L)', color: '#1890ff' },
  rainfall: { label: '雨量', value: 'rainfall', unit: '(mm)', color: '#FF0000' },
  waterLevel: { label: '水位', value: 'waterLevel', unit: '(m)', color: '#009688' },
  flow: { label: '流量', value: 'flow', unit: '(m³/s)', color: '#EF8432' },
  waterQuality: { label: '水质', value: 'waterQuality', unit: '', color: '#16FFD8' },
  waterQuality1: { label: '流速', value: 'waterQuality1', unit: '(m/s)', color: '#1890ff' },
}
export const hoursOptions = [
  { label: '0', value: '00:00:00' },
  { label: '1', value: '01:00:00' },
  { label: '2', value: '02:00:00' },
  { label: '3', value: '03:00:00' },
  { label: '4', value: '04:00:00' },
  { label: '5', value: '05:00:00' },
  { label: '6', value: '06:00:00' },
  { label: '7', value: '07:00:00' },
  { label: '8', value: '08:00:00' },
  { label: '9', value: '09:00:00' },
  { label: '10', value: '10:00:00' },
  { label: '11', value: '11:00:00' },
  { label: '12', value: '12:00:00' },
  { label: '13', value: '13:00:00' },
  { label: '14', value: '14:00:00' },
  { label: '15', value: '15:00:00' },
  { label: '16', value: '16:00:00' },
  { label: '17', value: '17:00:00' },
  { label: '18', value: '18:00:00' },
  { label: '19', value: '19:00:00' },
  { label: '20', value: '20:00:00' },
  { label: '21', value: '21:00:00' },
  { label: '22', value: '22:00:00' },
  { label: '23', value: '23:00:00' },
]
