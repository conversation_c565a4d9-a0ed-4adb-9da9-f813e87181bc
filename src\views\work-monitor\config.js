export const contentConfig = {
  // 直升式卷扬闸门
  G: [
    { label: '开启状态', field: 'isOpen' },
    { label: '开启时长', field: 'runTime' },
    { label: '闸门高度', field: 'gateValue' },
    { label: '更新时间', field: 'dateTime' },
  ],
  // 卧倒式液压闸门（单油缸）
  G_1: [
    { label: '开启状态', field: 'isOpen' },
    { label: '开启时长', field: 'runTime' },
    { label: '闸门角度', field: 'gateValue' },
    { label: '更新时间', field: 'dateTime' },
  ],
  // 卧倒式液压闸门（双油缸）
  G_2: [
    { label: '开启状态', field: 'isOpen' },
    { label: '开启时长', field: 'runTime' },
    { label: '闸门角度', field: 'gateValue' },
    { label: '更新时间', field: 'dateTime' },
  ],
  // 轴流泵
  P: [
    { label: '开启状态', field: 'isOpen' },
    { label: '开启时长', field: 'runTime' },
    { label: '翻水量', field: 'flowValue', unit: '万m³' },
    { label: '更新时间', field: 'dateTime' },
  ],
  // 潜水泵
  P_2: [
    { label: '开启状态', field: 'isOpen' },
    { label: '开启时长', field: 'runTime' },
    { label: '翻水量', field: 'flowValue', unit: '万m³' },
    { label: '更新时间', field: 'dateTime' },
  ],
  // 蝶阀
  BUTTERFLY_VALVE: [
    { label: '开启状态', field: 'isOpen' },
    { label: '更新时间', field: 'dateTime' },
  ],
  // 配电柜
  GGD: [
    { label: '开启状态', field: 'isOpen' },
    { label: '更新时间', field: 'dateTime' },
  ],
  // 阀门
  VALVE: [
    { label: '开启状态', field: 'isOpen' },
    { label: '更新时间', field: 'dateTime' },
  ],
  // 电磁阀
  SOLENOID_VALVE: [
    { label: '开启状态', field: 'isOpen' },
    { label: '流量', field: 'rated', unit: 'm³/s' },
    { label: '更新时间', field: 'dateTime' },
  ],
  // 油泵
  OIL_PUMP: [
    { label: '开启状态', field: 'isOpen' },
    { label: '更新时间', field: 'dateTime' },
  ],
}
