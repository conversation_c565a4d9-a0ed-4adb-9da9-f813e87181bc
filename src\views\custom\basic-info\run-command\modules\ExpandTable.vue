<template>
  <div style="height: 100%">
    <VxeTable
      ref="vxeTableRef"
      :isShowTableHeader="false"
      :tableKey="tableKey"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="true"
      :rowConfig="{ isHover: false }"
      min-height="82px"
      max-height="280px"
      size="small"
      @refresh="getList"
    ></VxeTable>
    <OptCmdDetail v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import OptCmdDetail from '../../opt-command/modules/OptCmdDetail.vue'
  import { getRunmdById } from '../services'

  export default {
    name: 'ExpandTable',
    components: { VxeTable, OptCmdDetail },
    props: ['row'],
    data() {
      return {
        showForm: false,
        tableKey: 1,
        loading: true,
        list: [],
        selectIds: [],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          { title: '日期', field: 'operateDate', minWidth: 120, showOverflow: 'tooltip' },
          { title: '工程名称', field: 'projectName', minWidth: 120, showOverflow: 'tooltip' },
          { title: '操作人', field: 'operateName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '监护人', field: 'guardianName', minWidth: 100, showOverflow: 'tooltip' },
          {
            title: '接收状态',
            field: 'recStatusCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                return row.recStatusCode === 1 ? '已接收' : '未接收'
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>详情</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {},
    methods: {
      getList() {
        this.loading = true
        getRunmdById({ runCmdId: this.row.runCmdId }).then(
          response => {
            if (response.code === 200 && response.data && response.data.projectOpInfoList) {
              this.list = response.data.projectOpInfoList
            } else {
              this.list = []
            }
            this.loading = false
          },
        ).catch(() => {
          this.list = []
          this.loading = false
        })
      },

      handleDetail(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      onOperationComplete() {
        this.isShowModal = false
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 10px;
    }
  }

  ::v-deep .vxe-table--render-default .vxe-table--body-wrapper {
    height: auto !important;
  }
</style>
