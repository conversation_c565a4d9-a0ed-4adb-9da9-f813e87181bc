<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <!-- <a-form-item label="计划名称">
        <a-input v-model="queryParam.logTitle" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item> -->

      <a-form-item label="所在班组">
        <a-select v-model="queryParam.groupId" allowClear :options="groupOptions" placeholder="请选择"></a-select>
      </a-form-item>
      <a-form-item label="日志日期">
        <a-range-picker format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" v-model:value="logDate" @change="changeLogDate" />
      </a-form-item>

      <template #table>
        <div class="page-right-panel">
          <div class="right-panel-header">
            <div class="title">{{ userLogName }}的排班日志</div>
            <div>
              <a-radio-group
                v-model="queryParam.logType"
                button-style="solid"
                style="margin-right: 10px"
                @change="onLogTypeChange"
              >
                <a-radio-button v-for="(el, i) in logTypeOptions" :key="i" :value="el.value">
                  {{ el.label }}
                </a-radio-button>
              </a-radio-group>
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
            </div>
          </div>

          <div v-if="loading" class="loading">
            <a-spin />
          </div>
          <div v-if="list.length === 0" style="text-align: center; margin-top: 100px"><a-empty /></div>

          <div v-else class="content" :key="listKey">
            <div v-for="(el, idx) in list" :key="idx" style="height: 100%">
              <div class="item" v-if="el.logId" @click="() => {}">
                <div class="item-header">
                  <div class="left">
                    <div class="tag-width" :class="el.logType == 1 ? 'zhi-log' : 'diao-log'">
                      {{ logTypeOptions.find(ele => ele.value == el.logType)?.label }}
                    </div>

                    <div class="tag-width tag-shift text-overflow">{{ el.shiftName }}</div>
                    <div class="tag-width time">{{ el.logDate }}</div>
                    <div class="tag-width tag-group text-overflow">{{ el.groupName }}</div>
                  </div>

                  <div class="right">
                    <a-icon type="edit" style="margin-right: 10px" @click="handleUpdate(el)" />
                    <a-icon type="delete" @click="handleDelete(el)" />
                  </div>
                </div>
                <div class="item-content">
                  <div class="log">{{ el.logContent }}</div>

                  <div class="attach">
                    <img src="~@/assets/images/pin-icon.png" width="15" height="15" alt="" />
                    <div style="margin: 0 2px; color: #86909c">附件:</div>

                    <UploadFile
                      style="margin-top: 3px; height: 46px"
                      :fileUrl="el.attachVOS?.map(el => el.attachUrl)"
                      :multiple="true"
                      listType="picture-card"
                      :onlyView="true"
                      disabled
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <a-pagination
            v-if="list.length !== 0"
            style="text-align: right; padding: 14px 30px"
            size="small"
            :total="total"
            :defaultPageSize="9"
            @change="handlePageChange"
            :show-total="total => `共 ${total} 条`"
          />
        </div>

        <FormDrawer
          v-if="showFormDrawer"
          :logTypeOptions="logTypeOptions"
          ref="formDrawerRef"
          @ok="getList"
          @close="showFormDrawer = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getWorkLogPage, workLogDelete } from './services'
  import { getWorkGroupPage } from '@/views/work/work-group/services'
  import FormDrawer from './modules/FormDrawer'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'ScheduleLog',
    components: {
      VxeTableForm,
      VxeTable,
      FormDrawer,
      UploadFile,
    },
    data() {
      return {
        showFormDrawer: false,
        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,
        userLogName: '',

        listKey: 1,
        logDate: [],
        queryParam: {
          groupId: undefined,
          logType: undefined,
          startTime: '',
          endTime: '',
          pageNum: 1,
          pageSize: 9,
          sort: [],
        },
        logTypeOptions: [
          { label: '全部', value: undefined },
          { label: '值班日志', value: 1 },
          { label: '调度日志', value: 2 },
        ],
        groupOptions: [],
      }
    },
    created() {
      // console.log(this.$store.state.user.name)
      this.userLogName = this.$store.state?.user?.name
      getWorkGroupPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        this.groupOptions = (res.data?.data || []).map(el => ({ ...el, label: el.groupName, value: el.groupId }))

        this.getList()
      })
    },
    methods: {
      onLogTypeChange(val) {
        this.$nextTick(() => {
          this.handleQuery()
        })
      },

      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getWorkLogPage(this.queryParam).then(response => {
          const length = (response.data.data || []).length
          if (length == 0 || length == 9) {
            this.list = response.data.data
          } else {
            this.list = [...response.data.data, ...[...new Array(9 - length).keys()]]
          }
          this.total = response.data.total
          this.loading = false
          this.listKey += 1
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          startTime: '',
          endTime: '',
          groupId: undefined,
          logType: undefined,
          pageNum: 1,
          sort: [],
        }
        this.logDate = []
        this.handleQuery()
      },

      // 分页
      handlePageChange(pageNumber) {
        this.queryParam.pageNum = pageNumber
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.logId)
        this.names = valObj.records.map(item => item.logTitle)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      changeLogDate(val) {
        this.queryParam.startTime = val[0]
        this.queryParam.endTime = val[1]
      },
      /* 新增 */
      handleAdd() {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleAdd())
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const logIds = row.logId ? [row.logId] : this.ids
        const names = row.logTitle || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return workLogDelete({ logIds: logIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.getList()
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .page-right-panel {
    flex: 1;
    background-color: #fff;
    position: relative;
    overflow: auto;
    .loading {
      position: absolute;
      width: 100%;
      height: 100%;
      text-align: center;
      top: 200px;
    }
  }

  .right-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    user-select: none;
    padding: 14px;
    height: 60px;

    .title {
      font-size: 16px;
      font-weight: 700;
      transition: all 0.3s;
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .content {
    height: calc(100% - 110px);
    flex: 1;
    width: 100%;
    padding: 0 14px;

    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    grid-gap: 24px;

    .item {
      height: 100%;
      border-radius: 4px;
      border: 1px solid #e5e6eb;
      .item-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f7f8fa;
        padding: 13px 10px 13px 10px;
        .left {
          display: flex;
          align-items: center;
          width: calc(100% - 38px);
        }
        .zhi-log {
          color: #165dff;
          background: rgba(22, 93, 255, 0.1);
        }
        .diao-log {
          color: #0fc6c2;
          background: rgba(15, 198, 194, 0.1);
        }
        .tag-width {
          width: 63px;
          height: 20px;
          margin-right: 6px;
          text-align: center;
          line-height: 20px;
          border-radius: 4px;
        }
        .tag-shift {
          width: 88px;
          padding: 0 5px;
          color: #23c343;
          background: #e8ffea;
        }
        .tag-group {
          width: 88px;
          padding: 0 3px;
          color: #4e5969;
          margin-right: 0;
          background: #e5e6eb;
        }
        .time {
          width: 90px;
          color: #4e5969;
          background: #e5e6eb;
        }
        .text-overflow {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .right {
          opacity: 0;
        }
        .right:hover {
          opacity: 1;
        }
      }

      .item-content {
        padding: 16px 10px 10px;
        .log {
          height: 66px;
          font-size: 14px;
          color: #4e5969;
          line-height: 22px;

          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }

        .attach {
          display: flex;
          align-items: center;
          position: relative;
          bottom: -5px;

          ::v-deep .ant-upload-list-picture-card .ant-upload-list-item {
            width: 44px;
            height: 44px;
            padding: 0px;
            margin: 0 4px 0 0;
          }
          ::v-deep .ant-upload-list-picture-card-container {
            width: 42px;
            height: 42px;
            margin: 0 4px 0 0;
            &:last-child {
              margin: 0;
            }
          }
        }
      }
    }
  }
</style>
