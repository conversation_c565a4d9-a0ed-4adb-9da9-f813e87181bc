<template>
  <!-- 增加修改 -->
  <div>
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      modalWidth="800"
      @cancel="cancel"
      modalHeight="750"
    >
      <div slot="content">
        <div class="table-panel" layout="vertical">
          <a-form-model ref="form" :model="form" :rules="rules" layout="vertical">
            <a-row></a-row>
            <a-row class="form-row" :gutter="32">
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="视频编码" prop="cameraCode">
                  <a-input allowClear v-model="form.cameraCode" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="视频名称" prop="cameraName">
                  <a-input allowClear v-model="form.cameraName" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="设备号" prop="deviceCode">
                  <a-input allowClear v-model="form.deviceCode" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="通道号" prop="channelCode">
                  <a-input allowClear v-model="form.channelCode" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <div style="padding: 0 2px; display: flex">
                <a-col :lg="12" :md="12" :sm="24">
                  <a-form-model-item label="封面" prop="snapUrl">
                    <UploadFile
                      :fileUrl.sync="form.snapUrl"
                      :multiple="false"
                      listType="picture-card"
                      folderName="projectCover"
                      accept=".png,.jpg,.jpeg"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :lg="12" :md="12" :sm="24" style="padding-left: 16px">
                  <a-form-model-item label="视频类型" prop="cameraType">
                    <a-radio-group v-model="form.cameraType">
                      <a-radio value="1">枪机</a-radio>
                      <a-radio value="2">球机</a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                  <a-form-model-item label="所属工程" prop="projectId">
                    <a-tree-select
                      v-model="form.projectId"
                      style="width: 100%"
                      :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                      :tree-data="projectOptions"
                      show-search
                      :filter-option="filterOption"
                      treeNodeFilterProp="title"
                      allowClear
                      placeholder="请选择"
                      :replaceFields="{
                        children: 'children',
                        title: 'projectName',
                        key: 'projectId',
                        value: 'projectId',
                      }"
                      tree-default-expand-all
                    ></a-tree-select>
                  </a-form-model-item>
                </a-col>
              </div>

              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="所属站点">
                  <a-select
                    show-search
                    :allowClear="true"
                    placeholder="请输入"
                    option-filter-prop="children"
                    :filter-option="filterOption"
                    v-model="form.siteId"
                    :options="siteOptions"
                  ></a-select>
                  <!-- <a-tree-select
                    v-model="form.siteId"
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :tree-data="siteOptions"
                    show-search
                    :filter-option="filterOption"
                    treeNodeFilterProp="title"
                    allowClear
                    placeholder="请选择"
                    :replaceFields="{
                      children: 'children',
                      title: 'name',
                      key: 'id',
                      value: 'key',
                    }"
                    tree-default-expand-all
                  ></a-tree-select> -->
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="所属水系">
                  <a-select
                    show-search
                    :allowClear="true"
                    placeholder="请输入"
                    option-filter-prop="children"
                    :filter-option="filterOption"
                    v-model="form.riverSystemId"
                    :options="riverSystemOptions"
                  ></a-select>
                  <!-- <a-tree-select
                    v-model="form.riverSystemId"
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :tree-data="riverSystemOptions"
                    show-search
                    :filter-option="filterOption"
                    treeNodeFilterProp="title"
                    allowClear
                    placeholder="请选择"
                    :replaceFields="{
                      children: 'children',
                      title: 'name',
                      key: 'id',
                      value: 'key',
                    }"
                    tree-default-expand-all
                  ></a-tree-select> -->
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="所属其他管理对象">
                  <a-select
                    show-search
                    :allowClear="true"
                    placeholder="请输入"
                    option-filter-prop="children"
                    :filter-option="filterOption"
                    v-model="form.otherObjectId"
                    :options="otherObjectOptions"
                  ></a-select>
                </a-form-model-item>
              </a-col>

              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="行政区划">
                  <a-tree-select
                    v-model="form.districtCode"
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :tree-data="districtOptions"
                    show-search
                    :filter-option="filterOption"
                    treeNodeFilterProp="title"
                    allowClear
                    placeholder="请选择"
                    :replaceFields="{
                      children: 'children',
                      title: 'districtName',
                      key: 'districtCode',
                      value: 'districtCode',
                    }"
                    tree-default-expand-all
                  ></a-tree-select>
                </a-form-model-item>
              </a-col>

              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="经度" prop="longitude">
                  <a-input-number
                    :min="73.66"
                    :max="135.05"
                    style="width: 100%"
                    v-model="form.longitude"
                    placeholder="请输入"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="纬度" prop="latitude">
                  <a-input-number
                    :min="3.86"
                    :max="53.55"
                    style="width: 100%"
                    v-model="form.latitude"
                    placeholder="请输入"
                  />
                </a-form-model-item>
              </a-col>

              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="所在位置" prop="location">
                  <a-input allowClear v-model="form.location" placeholder="请输入">
                    <a-icon
                      style="font-size: 20px; cursor: pointer"
                      @click="onMapOpen"
                      slot="addonAfter"
                      type="environment"
                      theme="twoTone"
                    />
                  </a-input>
                </a-form-model-item>
              </a-col>

              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="型号">
                  <a-input allowClear v-model="form.modelType" placeholder="请输入"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="规格">
                  <a-input allowClear v-model="form.specification" placeholder="请输入"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="生产日期">
                  <a-date-picker
                    v-model="form.productionDate"
                    format="YYYY-MM-DD"
                    formatValue="YYYY-MM-DD"
                    placeholder="请选择"
                    style="width: 100%"
                  />

                  <!-- <a-date-picker
                    v-model="form.productionDate"
                    format="YYYY-MM-DD"
                    placeholder="请选择"
                    style="width: 100%"
                  /> -->
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="生产批次">
                  <a-input allowClear v-model="form.productionLot" placeholder="请输入"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :lg="12" :md="12" :sm="24">
                <a-form-model-item label="制造厂家">
                  <a-input allowClear v-model="form.manufacturers" placeholder="请输入"></a-input>
                </a-form-model-item>
              </a-col>

              <a-col :lg="24" :md="24" :sm="24" :span="24">
                <a-form-model-item label="网络地址" prop="streamUrl">
                  <a-input allowClear v-model="form.streamUrl" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </div>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
      </template>
    </ant-modal>
    <MapModal ref="mapModalRef" @close="showMapModal = false" @confirm="onMapModalConfirm" />
  </div>
</template>
<script lang="jsx">
  import { addCamera, updateCamera, getCamera } from '../services'
  import MapModal from '@/components/MapBox/MapboxModal.vue'
  import AntModal from '@/components/pt/dialog/AntModal'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'

  export default {
    name: 'FormDrawer',
    props: ['districtOptions', 'projectOptions', 'siteOptions', 'riverSystemOptions', 'otherObjectOptions'],
    components: { MapModal, AntModal, UploadFile },
    data() {
      return {
        showMapModal: false,
        formTitle: '',
        loading: false,
        modalLoading: false,
        // 表单参数
        form: {
          cameraId: undefined,

          cameraName: undefined,
          cameraCode: undefined,
          projectId: undefined,
          siteId: undefined,
          projectId: undefined,
          riverSystemId: undefined,
          districtCode: undefined,
          longitude: undefined,
          latitude: undefined,
          location: undefined,
          streamUrl: undefined,
          cameraType: null,
          channelCode: null,
          deviceCode: null,
          snapUrl: '',
          otherObjectId: undefined,
          productionDate: null,
        },
        open: false,

        rules: {
          cameraCode: [{ required: true, message: '视频编码不能为空', trigger: 'blur' }],
          cameraName: [{ required: true, message: '视频名称不能为空', trigger: 'blur' }],
          deviceCode: [{ required: true, message: '设备号不能为空', trigger: 'blur' }],
          channelCode: [{ required: true, message: '通道号不能为空', trigger: 'blur' }],
          projectId: [{ required: true, message: '所属工程不能为空', trigger: 'change' }],
          cameraType: [{ required: true, message: '视频类型不能为空', trigger: 'change' }],
          equipmentCode: [{ required: true, message: '设备号不能为空', trigger: 'blur' }],
        },
      }
    },
    filters: {},
    created() {
      // 字典获取下拉
    },
    computed: {},
    watch: {},
    methods: {
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 打开地图
      onMapOpen() {
        this.showMapModal = true
        const mapInfo = {
          longitude: this.form.longitude,
          latitude: this.form.latitude,
          location: this.form.location,
        }
        this.$nextTick(() => this.$refs.mapModalRef.handleOpen(mapInfo))
      },
      onMapModalConfirm(mapInfo) {
        this.form.longitude = mapInfo.longitude
        this.form.latitude = mapInfo.latitude
        this.form.location = mapInfo.location
        this.showMapModal = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handleAdd(r) {
        this.open = true
        this.formTitle = '新增'
        this.form.projectId = r.treeNodeId
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.formTitle = '修改'

        this.modalLoading = true
        getCamera({ cameraId: record.cameraId }).then(res => {
          this.form = res.data
          this.form.cameraType = res.data?.cameraType + ''
          this.modalLoading = false
        })
      },

      /** 提交按钮 */
      submitForm: function () {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            // this.form.productionDate = moment(this.form.productionDate).format('YYYY-MM-DD')
            const saveForm = this.form
            console.log(this.form, '5555')
            if (this.form.cameraId !== undefined) {
              updateCamera(saveForm)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            } else {
              addCamera(saveForm)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('ok')
                })
                .catch(err => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
