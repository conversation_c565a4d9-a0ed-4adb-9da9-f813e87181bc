<template>
  <!-- 代表站配置 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :modalWidth="windowSize.width"
    @cancel="cancel"
    :modalHeight="windowSize.height"
    :footer="false"
  >
    <div slot="content">
      <!-- 筛选栏 -->
      <div class="common-table-page">
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          border
          :columns="columns"
          :tableData="list"
          :loading="loading"
          @refresh="getList"
          :tablePage="false"
          show-footer
          :footer-method="footerMethod"
          :merge-footer-items="[{ row: 0, col: 0, rowspan: 0, colspan: 5 }]"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
          </div>
        </VxeTable>
      </div>

      <FormBudget v-if="showFormBudget" ref="formBudgetRef" @ok="onOperationComplete" @close="showFormBudget = false" />
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import { deleteBudgetManage, getBudgetManage } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTableForm from '@/components/VxeTableForm'
  import VxeTable from '@/components/VxeTable'
  import FormBudget from './FormBudget'

  export default {
    name: 'BudgetManage',
    components: { AntModal, VxeTableForm, VxeTable, FormBudget },
    data() {
      return {
        open: false,
        showFormBudget: false,

        list: [],
        tableTitle: '预算管理',
        total: 0,
        isChecked: false,
        formTitle: '',
        remainingAmount: 0,

        queryParam: {
          projectId: undefined,
        },

        windowSize: {},
        ids: [],
        names: [],
        loading: false,
        modalLoading: false,
        exportLoading: false,
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '年份',
            field: 'year',
          },
          {
            title: '预算金额(元)',
            field: 'budgetAmount',
          },
          {
            title: '创建时间',
            field: 'createdTime',
          },
          {
            title: '操作',
            field: 'operate',
            width: 96,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    filters: {},
    created() {
      this.windowSize = {
        width: `${parseInt(window.innerWidth * 0.7)}`,
        height: `${parseInt(window.innerHeight * 0.85)}`,
      }
    },
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 按钮操作 */
      handleBudgetManage(record) {
        this.open = true
        this.formTitle = '预算管理'
        this.queryParam.projectId = record.projectId
        this.budgetTotal = record.contractAmount
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showFormBudget = false
        this.loading = true
        getBudgetManage({ projectId: this.queryParam.projectId }).then(response => {
          this.list = response?.data
          let curVal = response?.data?.map(item => item.budgetAmount * 1000).reduce((a, b) => a + b, 0)
          this.remainingAmount = (this.budgetTotal * 1000 - curVal) / 1000
          this.loading = false
        })
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showFormBudget = true
        this.$nextTick(() => this.$refs.formBudgetRef.handleFormBudget(this.queryParam.projectId))
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormBudget = true
        this.$nextTick(() => this.$refs.formBudgetRef.handleFormBudget(this.queryParam.projectId, record))
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const projectBudgetIds = row.projectBudgetId ? [row.projectBudgetId] : this.ids
        const names = row.year

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中年份为"' + names + '"的数据',
          onOk() {
            return deleteBudgetManage({ projectBudgetIds: projectBudgetIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.getList()
            })
          },
          onCancel() {},
        })
      },
      footerMethod({ columns, data }) {
        const footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '剩余合同金额(元)：' + this.remainingAmount
            }
            return null
          }),
        ]
        return footerData
      },
      // 导出
      handleExport() {},
    },
  }
</script>
<style lang="less" scoped>
  .common-table-page {
    height: calc(100vh - 280px);
    :deep(.vxe-footer--column) {
      text-align: center;
    }
  }
</style>
