<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content">
      <a-tabs v-model="activeFcstRange" size="small">
        <a-tab-pane
          v-for="(el, idx) in list"
          :key="el.fcstRange"
          :tab="fcstRangeOptions.find(ele => ele.value == el.fcstRange)?.label || el.fcstRange"
        >
          <div style="text-align: right; padding-bottom: 10px">
            <a-button size="small" @click="handleAdd">增加</a-button>
          </div>

          <a-table :columns="columns" :data-source="el.sites" :pagination="false" rowKey="rowKey" />
        </a-tab-pane>
      </a-tabs>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="onSubmit" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getInWaterRange, getIndexCodeSites, setInWaterRange } from '../services.js'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'FormDrawer',
    props: ['fcstRangeOptions'],
    components: { AntModal },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '站点配置',
        activeFcstRange: undefined,
        siteOptions: [],
        list: [],
        columns: [
          {
            title: '站点编码',
            colSpan: 1,
            customRender: (text, record, index) => {
              const idx = this.list.findIndex(el => el.fcstRange === this.activeFcstRange)
              const cIndexs = this.list[idx].sites.map(el => el.siteId)
              const cOptions = this.siteOptions.filter(el => !cIndexs.includes(el.value) || el.value === record.siteId)

              if (this.list[idx].sites[index] === undefined) return

              const obj = {
                attrs: { colSpan: 2 },
                children: (
                  <a-select style={{ width: '100%' }} v-model={this.list[idx].sites[index].siteId} placeholder='请选择'>
                    {cOptions.map((el, k) => {
                      return (
                        <a-select-option key={k} value={el.value}>
                          <div class='terminal-select-item'>
                            <span>{el.siteCode}</span>
                            <span>{el.siteName}</span>
                          </div>
                        </a-select-option>
                      )
                    })}
                  </a-select>
                ),
              }
              return obj
            },
          },
          {
            title: '站点名称',
            customRender: (v, r, i) => {
              const obj = {
                attrs: { colSpan: 0 },
              }
              return obj
            },
          },
          {
            title: '操作',
            align: 'center',
            customRender: (text, record, index) => {
              return (
                <a
                  onClick={() => {
                    const idx = this.list.findIndex(el => el.fcstRange === this.activeFcstRange)
                    this.list[idx].sites = this.list[idx].sites
                      .filter(el => {
                        return el.siteId !== record.siteId
                      })
                      .map((el, idx) => ({ ...el, rowKey: idx }))
                  }}
                >
                  删除
                </a>
              )
            },
          },
        ],
      }
    },
    filters: {},
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleShow() {
        this.open = true
        this.modalLoading = true

        getIndexCodeSites({ indexCode: 'rainfall' }).then(res => {
          this.siteOptions = res.data.map(el => ({ ...el, label: el.siteName, value: el.siteId }))

          getInWaterRange().then(resp => {
            this.activeFcstRange = this.fcstRangeOptions[0].value

            this.list = this.fcstRangeOptions.map((el, idx) => {
              return {
                fcstRange: el.value,
                sites: resp.data[idx] ? resp.data[idx].sites : [],
              }
            })

            this.modalLoading = false
          })
        })
      },

      handleAdd() {
        const idx = this.list.findIndex(el => el.fcstRange === this.activeFcstRange)

        this.list[idx].sites.push({
          rowKey: this.list[idx].sites.length,
          siteId: undefined,
          siteName: undefined,
        })
      },

      onSubmit() {
        setInWaterRange(this.list.map(el => ({ ...el, siteId: el.sites.map(ele => ele.siteId) }))).then(res => {
          this.$message.success('配置成功', 3)
          this.$emit('close')
          this.$emit('ok')
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .terminal-select-item {
    display: flex;
    width: 100%;
    justify-content: space-between;

    > span {
      flex: 1;
      text-align: left;
    }
  }

  ::v-deep .ant-select-selection-selected-value {
    width: 100%;
  }

  ::v-deep .ant-table-wrapper {
    margin: 0;
  }
</style>
