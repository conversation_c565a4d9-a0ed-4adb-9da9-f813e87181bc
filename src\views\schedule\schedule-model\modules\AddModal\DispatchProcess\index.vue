<template>
  <div style="flex: 1; display: flex">
    <Type1 v-if="baseInfo.dispathType === 1" ref="type1Ref" :baseInfo="baseInfo" />
    <Type2 v-if="baseInfo.dispathType === 2" ref="type2Ref" :baseInfo="baseInfo" />
    <Type3 v-if="baseInfo.dispathType === 3" ref="type3Ref" :baseInfo="baseInfo" />
  </div>
</template>

<script>
  import Type1 from './type1.vue'
  import Type2 from './type2.vue'
  import Type3 from './type3.vue'

  export default {
    name: 'DispatchProcess',
    components: {
      Type1,
      Type2,
      Type3,
    },
    props: ['baseInfo'],
    data() {
      return {}
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
      save() {
        if (this.baseInfo.dispathType === 1) {
          this.$emit('saveData', this.$refs.type1Ref.save())
        } else if (this.baseInfo.dispathType === 2) {
          this.$emit('saveData', this.$refs.type2Ref.save())
        } else if (this.baseInfo.dispathType === 3) {
          this.$emit('saveData', this.$refs.type3Ref.save())
        }
      },
    },
  }
</script>

<style lang="less" scoped></style>
