import request from '@/utils/request'

// 列表分页查询
export function getHighPage(data) {
  return request({
    url: '/prjstd/high/page',
    method: 'post',
    data,
  })
}
// 增加
export function addHigh(data) {
  return request({
    url: '/prjstd/high/add',
    method: 'post',
    data,
  })
}
// 详情
export function getHigh(params) {
  return request({
    url: '/prjstd/high/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function updateHigh(data) {
  return request({
    url: '/prjstd/high/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteHigh(params) {
  return request({
    url: '/prjstd/high/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
