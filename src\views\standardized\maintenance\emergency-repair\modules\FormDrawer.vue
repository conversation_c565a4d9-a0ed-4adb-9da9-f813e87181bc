<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <!-- <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col> -->
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="工程名称" prop="projectId">
              <!-- <a-select
                mode="multiple"
                :default-value="[]"
                style="width: 100%"
                placeholder="请选择工程名称"
                v-model="form.projectId"
              >
                <a-select-option v-for="item in propertyInProject" :key="item.projectId">
                  {{ item.projectName }}
                </a-select-option>
              </a-select> -->
              <a-tree-select
                v-model="form.projectId"
                style="width: 100%"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="projectOptions"
                show-search
                treeNodeFilterProp="title"
                allowClear
                placeholder="请选择"
                :replaceFields="{
                  children: 'children',
                  title: 'projectName',
                  key: 'projectId',
                  value: 'projectId',
                }"
                tree-default-expand-all
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="维修人员" prop="repairUserId">
              <a-select show-search placeholder="请输入" v-model="form.repairUserId" option-filter-prop="children">
                <a-select-option v-for="(d, index) in chargeOptions" :key="index" :value="d.userId">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="维修结果" prop="repairResult">
              <a-select show-search placeholder="请输入" v-model="form.repairResult" option-filter-prop="children">
                <a-select-option v-for="item in repairResultOptions" :key="item.key" :value="item.key">
                  {{ item.value }}
                </a-select-option>
              </a-select>
              <!-- <a-input v-model="form.propertyId" placeholder="请输入" allow-clear /> ,'propertyList' -->
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="维修时间" prop="repairTime">
              <a-date-picker
                v-model="form.repairTime"
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="维修内容" prop="repairContent">
              <a-textarea style="width: 100%" v-model="form.repairContent" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">合同</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile
                :fileUrl.sync="form.contractUrls"
                :multiple="true"
                listType="text"
                folderName="projectCover"
              />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">其他附件</div>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="">
              <UploadFile :fileUrl.sync="form.otherUrls" :multiple="true" listType="text" folderName="projectCover" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { addRepair, editRepair, getRepairById, getProjectListByPropertyId } from '../services'
  import { getOptions, getSysUserPage } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import UploadFile from '@/components/UploadFile/index.vue'
  import moment from 'moment'
  import { validEmail } from '@/utils/validate'

  export default {
    name: 'FormDrawer',
    components: { AntModal, UploadFile },
    props: ['projectOptions', 'repairResultOptions'],
    data() {
      return {
        isEdit: false,
        chargeOptions: [],

        formTitle: '',
        loading: false,
        modalLoading: false,
        form: {
          contractUrls: [],
          isProperty: '',
          otherUrls: [],
          projectId: null,
          repairContent: '',
          repairId: null,
          repairResult: '',
          repairTime: '',
          repairUserId: null,
        },
        open: false,
        propertyInProject: [],
        rules: {
          projectId: [{ required: true, message: '工程名称不能为空', trigger: 'change' }],

          repairUserId: [{ required: true, message: '维修人员不能为空', trigger: 'change' }],
          repairResult: [{ required: true, message: '维修结果不能为空', trigger: 'change' }],
          repairTime: [{ required: true, message: '维修时间不能为空', trigger: 'change' }],

          repairContent: [{ required: true, message: '维修内容不能为空', trigger: 'blur' }],
        },
      }
    },
    created() {
      getSysUserPage({
        pageNum: 1,
        pageSize: Number.MAX_SAFE_INTEGER,
        isDisabled: 0,
        deptId: null,
      }).then(response => {
        this.chargeOptions = response.data?.data
      })
    },
    mounted() {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
        }
      },
      /** 新增按钮操作 */
      handle(row) {
        this.open = true
        this.formTitle = '新增'
        if (row != undefined) {
          this.modalLoading = true
          this.formTitle = '修改'
          getRepairById({
            repairId: row.repairId,
          }).then(res => {
            if (res.code == 200) {
              //附件显示
              this.form = {
                ...res.data,
                contractUrls: res.data.contractUrls?.map(el => el.attachUrl),
                otherUrls: res.data.otherUrls?.map(el => el.attachUrl),
              }
              // this.form.propertyId = this.propertyList.find(el => el.propertyId == this.form.propertyId)?.propertyId
              // propertyId: this.form.propertyId
              // this.form.year = moment(`${res.data.year}-01-01`)
              // getProjectListByPropertyId({ propertyId: 1 }).then(res => {
              //   this.propertyInProject = res?.data
              // })
            }
            this.modalLoading = false
          })
        }
      },
      //

      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.repairTime = moment(this.form.repairTime).format('YYYY-MM-DD HH:mm:ss')
            this.form.isProperty = this.$route.meta.query?.isProperty ? 'true' : ''
            if (this.form.repairId == null) {
              addRepair(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              editRepair(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
  }
</style>
