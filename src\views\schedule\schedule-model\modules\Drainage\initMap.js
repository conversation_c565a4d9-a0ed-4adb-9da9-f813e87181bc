import axios from 'axios'
import { mapBoundGeo } from '@/utils/mapBounds.js'
import { getValueByKey } from '@/api/common.js'

export default function initMap(mapIns) {
  // 天地图
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-base-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${process.env.VUE_APP_TIANDI_BASE}/img_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[0].id,
  )
  // 天地图标注
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-label-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${process.env.VUE_APP_TIANDI_BASE}/cia_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[1].id,
  )

  // 灌区边界
  getValueByKey('gis.irr.boundary').then(res => {
    axios(res.data).then(resp => {
      mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })

      mapIns.addLayer({
        id: 'gq-area-line',
        type: 'line',
        source: {
          type: 'geojson',
          data: resp.data, //区划的面数据
        },
        paint: {
          'line-color': '#03FFCD',
          'line-width': 2,
        },
      })
    })
  })
}
