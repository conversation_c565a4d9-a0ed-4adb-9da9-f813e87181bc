import request from '@/utils/request'

export function getAairportList(data) {
  return request({
    url: '/custom/airport/page',
    method: 'post',
    data,
  })
}

export function getYear() {
  return request({
    url: '/custom/water/backbone/page',
    method: 'post',
    data,
  })
}

// 增加
export function addAairport(data) {
  return request({
    url: '/custom/airport/add',
    method: 'post',
    data,
  })
}
// 详情
export function getAairportById(params) {
  return request({
    url: '/custom/airport/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 更新
export function editAairport(data) {
  return request({
    url: '/custom/airport/update',
    method: 'post',
    data,
  })
}
// 删除
export function deleteAairport(params) {
  return request({
    url: '/custom/airport/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
