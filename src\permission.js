import router from './router'
import store from './store'
import storage from 'store'
import NProgress from 'nprogress' // progress bar
import '@/components/NProgress/nprogress.less' // progress bar custom style
import notification from 'ant-design-vue/es/notification'
import { setDocumentTitle } from '@/utils/domUtil'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Cookies from 'js-cookie'
import QueryString from 'query-string'
import { constantRouterMap } from '@/config/router.config'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const allowList = [
  'login',
  'demo',
  '404',
  '403',
  '500',
  ...constantRouterMap.filter(el => el.meta?.whiteList).map(el => el.name),
] // no redirect allowList

const loginRoutePath = '/user/login'
const defaultRoutePath = '/index'

//
const findByType = (tree, targetType) => {
  for (const node of tree) {
    if (node?.type === targetType) {
      return node
    } else if (node.children && node.children.length > 0) {
      const result = findByType(node.children, targetType)
      if (result) {
        return result
      }
    }
  }
  return null
}

router.beforeEach((to, from, next) => {
  // incoming route
  NProgress.start() // start progress bar
  to.meta &&
    typeof to.meta.title !== 'undefined' &&
    setDocumentTitle(`${to.meta.title} - ${localStorage.getItem('appName')}`)
  // 存储params参数到本地
  const paramsJson = JSON.stringify(to.params)
  if (paramsJson !== '{}') {
    localStorage.setItem('routerParams' + to.name, paramsJson)
  }

  if (allowList.includes(to.name)) {
    // 在免登录名单，直接进入
    next()
    return
  }

  /* has token */
  if (storage.get(ACCESS_TOKEN)) {
    if (store.getters.roles.length === 0) {
      // request login userInfo
      store
        .dispatch('GetInfo')
        .then(res => {
          // 获取用户信息，根据用户角色分配角色
          storage.set('userId', res.data.userId)
          storage.set('user', res.data)

          // res.roles = ['admin']
          const roles = res.roles
          // generate dynamic router
          store
            .dispatch('GenerateRoutes', { roles })
            .then(() => {
              // 根据roles权限生成可访问的路由表
              // 动态添加可访问路由表
              router.addRoutes(store.getters.addRouters)
              // 请求带有 redirect 重定向时，登录自动重定向到该地址
              // next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
              if (to.path === loginRoutePath || to.path === '/') {
                const routerList = JSON.parse(localStorage.getItem('asyncRouters'))
                if (routerList != null) {
                  const currentRow = findByType(routerList, 3)
                  if (currentRow == null) {
                    next({ path: '/noContent' })
                  } else {
                    next({ path: currentRow.path })
                  }
                  NProgress.done()
                }
              } else {
                next({ path: to.path })
              }
            })
            .catch(res => {})
        })
        .catch(res => {})
    } else {
      next()
    }
  } else {
    if (to.query?.ticket) {
      if (Array.isArray(to.query.ticket)) {
        sessionStorage.setItem('ticket', to.query.ticket[to.query.ticket.length - 1])
      } else {
        sessionStorage.setItem('ticket', to.query.ticket)
      }
      let quy = { ...to.query }
      Reflect.deleteProperty(quy, 'ticket')
      next({
        path: loginRoutePath,
        query: { redirect: `${to.path}${Object.keys(quy).length ? `?${QueryString.stringify(quy)}` : ''}` },
      })
    } else {
      next({ path: loginRoutePath, query: { redirect: to.fullPath } })
    }

    NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
