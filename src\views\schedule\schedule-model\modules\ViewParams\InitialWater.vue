<template>
  <div style="height: 100%; display: flex; flex-direction: column; padding-bottom: 16px">
    <a-select
      v-model="river1"
      style="width: 240px; margin: 10px 0 16px"
      :options="dataSource"
      placeholder="请选择"
      allowClear
    />
    <div style="flex: 1">
      <VxeTable
        size="small"
        :columns="columns"
        :tableData="tableList"
        :loading="false"
        :isDrop="false"
        :tablePage="false"
        :isShowTableHeader="false"
      />
    </div>
  </div>
</template>

<script>
  import VxeTable from '@/components/VxeTable/index.vue'

  export default {
    name: 'InitialWater',
    components: {
      VxeTable,
    },
    props: {
      chSiteWaters: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        dataSource: [],
        columns: [],
        river1: null,
      }
    },
    computed: {
      tableList() {
        return this.river1 ? this.dataSource.find(el => el.newProjectName === this.river1).sites : []
      },
    },
    created() {
      this.river1 = this.chSiteWaters[0].newProjectName
      this.dataSource = this.chSiteWaters.map(el => ({
        ...el,
        label: el.newProjectName,
        value: el.newProjectName,
      }))
      this.columns = [
        { title: '站点', field: 'siteName', minWidth: 150 },
        { title: '水位', field: 'wlv', minWidth: 150 },
      ]
    },
    methods: {},
  }
</script>

<style lang="less" scoped></style>
