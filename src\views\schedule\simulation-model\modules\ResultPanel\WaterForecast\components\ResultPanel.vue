<template>
  <div style="height: 100%; width: 100%; display: flex">
    <div style="height: 100%; width: 60%">
      <BarAndLineMix
        :dataSource="chartData"
        :markLineXAxis="resultData?.reals?.length - 1"
        :markPointsData="markPointsData"
      />
    </div>
    <div style="height: 100%; width: 40%">
      <ResultTable :dataSource="resultData.fcsts || []" />
    </div>
  </div>
</template>

<script lang="jsx">
  import BarAndLineMix from './BarAndLineMix.vue'
  import ResultTable from './ResultTable.vue'
  import { maxBy } from 'lodash'

  export default {
    name: 'ResultPanel',
    props: ['resultData'],
    components: { BarAndLineMix, ResultTable },
    data() {
      return {
        chartData: [],
      }
    },
    computed: {
      markPointsData() {
        const predFlow = maxBy(this.resultData.fcsts, 'wlv')
        const predFlowMax = {
          x:
            this.resultData.fcsts.findIndex(el => (predFlow ? predFlow.tm === el.tm : false)) +
            this.resultData.reals.length,
          y: predFlow?.inflow,
        }

        const predWater = maxBy(this.resultData.fcsts, 'wlv')

        const predWaterMax = {
          x:
            this.resultData.fcsts.findIndex(el => (predWater ? predWater.tm === el.tm : false)) +
            this.resultData.reals.length,
          y: predWater?.wlv,
        }

        return {
          water: [{ name: '预测', value: predWaterMax.y, xAxis: predWaterMax.x, yAxis: predWaterMax.y }],
          flow: [{ value: predFlowMax.y, xAxis: predFlowMax.x, yAxis: predFlowMax.y }],
        }
      },
    },
    watch: {
      resultData: {
        handler(newVal) {
          const data = newVal

          const rainData = {
            name: '雨量',
            data: data.reals.map(el => [el.tm, el.rain]).concat(data.fcsts.map(el => [el.tm, el.rain])),
          }
          const sumRainData = {
            name: '累计雨量',
            data: rainData.data.map((el, idx) => {
              const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
              return [el[0], +sum.toFixed(1)]
            }),
          }

          this.chartData = [
            rainData,
            sumRainData,
            {
              name: '水位',
              data: data.reals.map(el => [el.tm, el.wlv]).concat(data.fcsts.map(el => [el.tm, el.wlv])),
            },
            {
              name: '来水流量',
              data: data.reals.map(el => [el.tm, el.inflow]).concat(data.fcsts.map(el => [el.tm, el.inflow])),
            },
          ]
        },
        deep: true,
        immediate: true,
      },
    },
  }
</script>

<style lang="less" scoped></style>
