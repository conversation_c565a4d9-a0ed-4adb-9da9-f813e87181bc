.tree-table-right-panel {
  border: 1px solid #d9d9d9;
  background: #fff;
}

::v-deep .table-panel {
  display: flex;
  height: calc(100vh - 116px);
  width: 100%;

  .tree-panel {
    // margin-top: 20px;
    // margin-right: 10px;
    border-radius: 2px;
    border-top: 1px solid rgba(217, 217, 217, 0.6);
    border-left: 0px solid #d9d9d9;
    border-right: 1px solid rgba(217, 217, 217, 0.6);
    border-bottom: 1px solid rgba(217, 217, 217, 0.6);
    height: calc(100vh - 120px);
    min-width: 160px;
    max-width: 240px;
    // background: red;
    .tab-pane {
      min-width: 160px;
      max-width: 220px;
      // background: green;
      // width: 220px;
      margin-left: 8px;
    }
    .type-tree-panel {
      // width: 220px;
      min-width: 160px;
      max-width: 220px;
      height: 100%;
    }

    .tab-tree-panel-box {
      overflow-y: auto;
      overflow-x: hidden;
      min-width: 160px;
      max-width: 220px;
      height: calc(100vh - 240px);
    }
  }
  .transfer-panel {
    flex: 1;
    width: 100%;
    // margin-left: 6px;
    height: calc(100vh - 120px);
    .left-table {
      // margin-top: 20px;
      box-sizing: border-box;
      width: 44.8%;
      height: 100%;
      float: left;
      border-left: 1px solid rgba(217, 217, 217, 0.6);
      border-right: 1px solid rgba(217, 217, 217, 0.6);
      .tab-title {
        height: 40px;
        background-color: #edf1fe; //#1890ff;
      }
      .tab-item {
        display: flex;
        margin: 0px 10px;
        flex-wrap: nowrap;
        .item {
          // width: 240px;
          width: 50%;
          // margin-left: 8px;
          margin-top: 14px;
          .tab-input {
            margin-left: 8px;
            height: 30px;
            // width: 160px;
            min-width: 80px;
            max-width: 60%;
          }
        }
      }
      .vxetab-table {
        margin-top: 14px;
        height: calc(100vh - 240px);
      }
    }
    .table-button {
      box-sizing: border-box;
      width: 9.4%;
      height: 100%;
      float: left;
      .tab-group-div {
        width: 100% !important;
        height: 100% !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        .tab-group {
          width: 100px;
          height: 100px;
          display: flex !important;
          flex-direction: column;
          justify-content: center !important;
          align-items: center !important;
          .tab-btn {
            width: 68px !important;
            height: 24px !important;
            margin-top: 10px !important;
          }
        }
      }
    }
    .right-table {
      // margin-top: 20px;
      box-sizing: border-box;
      width: 44.8%;
      height: 100%;
      float: left;
      // border: 1px solid #d9d9d9;
      border-left: 1px solid rgba(217, 217, 217, 0.6);
      border-right: 1px solid rgba(217, 217, 217, 0.6);
      .tab-title {
        height: 40px;
        color: #000 !important;
        background-color: #edf1fe; //#1890ff;
      }

      .tab-item {
        display: flex;
        margin: 0px 10px;
        flex-wrap: nowrap; /* 允许项目换行 */
        .item {
          // width: 240px;
          width: 50%;
          // margin-left: 8px;
          margin-top: 14px;

          .tab-input {
            margin-left: 8px;
            height: 30px;
            // width: 160px;
            min-width: 80px;
            max-width: 60%;
          }
        }
      }

      .vxetab-table {
        margin-top: 14px;
        height: calc(100vh - 240px);
      }
    }
    .left-span {
      float: left;
      margin-top: 8px;
      margin-left: 12px;
      font-weight: 600;
      color: #000 !important;
    }
    .right-span {
      float: right;
      margin-top: 8px;
      margin-right: 12px;
      color: #a19e98 !important;
    }
  }
}

::v-deep .table-box {
  position: absolute;
  height: 100%;
  overflow: hidden;
  position: relative;
}

::v-deep .ant-tabs {
  // min-width: 200px;
  width: 200px !important;

  // max-width: 240px;
}
::v-deep .ant-tabs-nav-wrap {
  height: 30px !important;
  background: transparent !important;
}
::v-deep .ant-tabs-tab {
  // flex: 1;
  width: 25% !important;
  // background: #1890ff;
  background: transparent !important;
  // padding: 6px 5px !important;
  padding-left: 10px !important;
  padding-right: 2px !important;
  // max-width: 20%;
  // height: 39px !important;
  height: 30px !important;
  // line-height: 40px !important;
  // margin-left: 5px !important;
  // margin-right: 0px !important;
  border: 0 solid transparent !important;
  border-radius: 0px !important;
  margin-top: 0px !important;
  text-align: center;
}

::v-deep .ant-tabs-tab:hover {
  // background-color: #1890ff !important;
  color: #1890ff !important;
}

::v-deep .vxe-select > .vxe-input {
  // width: 72px !important;
  width: 87px !important;
}
// ::v-deep .vxe-pager .vxe-pager--wrapper {
//   min-width: 520px !important;
// }
::v-deep .vxe-pager .vxe-pager--sizes {
  // width: 70px !important;
  width: 85px !important;
}
::v-deep.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-nav-container {
  height: 40px !important;
}
::v-deep .vxe-grid--pager-wrapper {
  min-width: 360px !important;
  padding-right: 10px !important;
}
